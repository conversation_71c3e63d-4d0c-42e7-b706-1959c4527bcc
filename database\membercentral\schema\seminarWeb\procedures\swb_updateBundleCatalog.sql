ALTER PROC dbo.swb_updateBundleCatalog
@bundleID INT,
@catalogOrgCode VARCHAR(10),
@allowCatalog BIT,
@isPriceBasedOnActual BIT,
@dateCatalogStart DATE,
@dateCatalogEnd DATE,
@freeRateDisplay VARCHAR(5),
@revenueGLAccountID INT,
@isFeatured BIT,
@sponsorUsageIdsList VARCHAR(MAX),
@includeSpeakers BIT,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblExistingSettings') IS NOT NULL
		DROP TABLE #tblExistingSettings;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #tblExistingSettings(bundleID INT, allowCatalog BIT, isPriceBasedOnActual BIT, dateCatalogStart DATE, 
		dateCatalogEnd DATE, freeRateDisplay VARCHAR(5), revenueGLAccountID INT, isFeatured BIT, includeSpeakers BIT);
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

	DECLARE @orgID INT, @siteID int, @participantID int, @isPublisher bit, @handlesOwnPayment BIT, @crlf VARCHAR(10), 
		@msgjson VARCHAR(MAX);

	SET @crlf = CHAR(13) + CHAR(10);
	
	SELECT @participantID = p.participantID, @orgID = mcs.orgID, @siteID = mcs.siteID
	FROM dbo.tblParticipants as p
	INNER JOIN memberCentral.dbo.sites AS mcs ON mcs.siteCode = p.OrgCode
	WHERE p.orgCode = @catalogOrgCode;
	
	SELECT @handlesOwnPayment = p.handlesOwnPayment, @isPublisher = CASE WHEN @participantID = p.participantID THEN 1 ELSE 0 END
	FROM dbo.tblBundles as b
	INNER JOIN dbo.tblParticipants as p on p.participantID = b.participantID
	WHERE b.bundleID = @bundleID;

	INSERT INTO #tblExistingSettings(bundleID, allowCatalog, isPriceBasedOnActual, dateCatalogStart, dateCatalogEnd, 
		freeRateDisplay, revenueGLAccountID, isFeatured, includeSpeakers)
	SELECT b.bundleID, 
		CASE WHEN @isPublisher = 1 THEN CASE WHEN b.dateCatalogStart IS NOT NULL THEN 1 ELSE 0 END ELSE optIn.sellCatalog END AS allowCatalog, 
		CASE WHEN @isPublisher = 1 THEN b.isPriceBasedOnActual ELSE ISNULL(optIn.isPriceBasedOnActual,0) END, 
		b.dateCatalogStart, b.dateCatalogEnd, 
		CASE WHEN @isPublisher = 1 THEN b.freeRateDisplay ELSE ISNULL(optIn.freeRateDisplay,'$0.00') END, 
		b.revenueGLAccountID, 
		CASE WHEN f.featuredID IS NOT NULL THEN 1 ELSE 0 END AS isFeatured, ISNULL(b.includeSpeakers, 0) AS includeSpeakers
	FROM dbo.tblBundles b
	LEFT OUTER JOIN dbo.tblFeaturedPrograms AS f ON f.participantID = @participantID AND f.bundleID = b.bundleID
	LEFT JOIN dbo.tblBundlesOptIn AS optIn ON optIn.bundleID = b.bundleID AND optIn.participantID = @participantID
	WHERE b.bundleID = @bundleID; 

	IF @revenueGLAccountID IS NOT NULL BEGIN
		IF @handlesOwnPayment = 1
			SELECT @revenueGLAccountID = GLAccountID
			FROM memberCentral.dbo.tr_GLAccounts
			WHERE GLAccountID = @revenueGLAccountID
			AND orgID = @orgID
			AND [status] = 'A';
		ELSE
			SET @revenueGLAccountID = NULL;
	END

	BEGIN TRAN;
		IF @isPublisher = 1 BEGIN
			IF @allowCatalog is not null
				UPDATE dbo.tblBundles
				SET isPriceBasedOnActual = @isPriceBasedOnActual,
					revenueGLAccountID = NULLIF(@revenueGLAccountID,0),
					dateCatalogStart = @dateCatalogStart,
					dateCatalogEnd = @dateCatalogEnd,
					freeRateDisplay = @freeRateDisplay
				WHERE bundleID = @bundleID; 
			ELSE
				UPDATE dbo.tblBundles
				SET isPriceBasedOnActual = @isPriceBasedOnActual,
					freeRateDisplay = @freeRateDisplay
				WHERE bundleID = @bundleID;
		END

		IF @allowCatalog = 1 AND @isPublisher = 0 BEGIN
			EXEC dbo.sw_optInBundle @orgcode=@catalogOrgCode, @bundleID=@bundleID, @recordedByMemberID=@recordedByMemberID;
		END 

		IF @allowCatalog = 1 BEGIN
			UPDATE dbo.tblBundlesOptIn
			SET sellCatalog = @allowCatalog,
				isPriceBasedOnActual = @isPriceBasedOnActual,
				freeRateDisplay = @freeRateDisplay
			WHERE participantID = @participantID 
			AND bundleID = @bundleID;

			IF @isPublisher = 1 BEGIN
				UPDATE dbo.tblBundles
				SET includeSpeakers = @includeSpeakers
				WHERE bundleID = @bundleID;
			END
		END

		IF @allowCatalog = 0 BEGIN 
			-- mark bundle rates as deleted
			UPDATE sr
			SET sr.siteResourceStatusID = 3
			FROM memberCentral.dbo.cms_siteResources AS sr
			INNER JOIN dbo.tblBundlesAndRates AS bar ON bar.siteResourceID = sr.siteResourceID
			WHERE bar.bundleID = @bundleID
			AND sr.siteResourceStatusID = 1
			AND bar.participantID = @participantID;

			UPDATE bar
			SET bar.rateGroupingID = NULL
			FROM dbo.tblBundlesAndRates bar
			INNER JOIN dbo.tblBundlesAndRatesGrouping AS rg ON rg.rateGroupingID = bar.rateGroupingID
			WHERE rg.bundleID = @bundleID
			AND bar.participantID = @participantID
			AND rg.participantID = @participantID;

			DELETE FROM dbo.tblBundlesAndRatesGrouping
			WHERE bundleID = @bundleID
			AND participantID = @participantID;

			UPDATE dbo.tblBundlesOptIn
			SET sellCatalog = @allowCatalog
			where participantID = @participantID 
			AND bundleID = @bundleID;
 
	 		IF @isPublisher = 1 BEGIN
				UPDATE dbo.tblBundles SET isPriceBasedOnActual = 0, includeSpeakers = 0 WHERE bundleID = @bundleID;
		
				EXEC membercentral.dbo.cms_deleteFeaturedImageUsage @referenceID=@bundleID, @referenceType='swbprogram'; 

				IF @sponsorUsageIdsList IS NOT NULL
					DELETE FROM membercentral.dbo.sponsorsUsage 
					WHERE sponsorUsageID IN (select listitem from membercentral.dbo.fn_IntListToTable(@sponsorUsageIdsList,',')); 
			END

			IF @isPublisher = 0
				UPDATE dbo.tblBundlesOptIn 
				SET isPriceBasedOnActual = 0 
				WHERE bundleID = @bundleID
				AND participantID = @participantID;
		END 

		IF @allowCatalog = 0 AND EXISTS (SELECT 1 FROM #tblExistingSettings WHERE isFeatured = 1)
			EXEC dbo.sw_toggleFeaturedProgram @orgCode=@catalogOrgCode, @programType='swb', @programID=@bundleID, @isFeatured=0, @recordedByMemberID=@recordedByMemberID;
		IF @isFeatured IS NOT NULL AND EXISTS (SELECT 1 FROM #tblExistingSettings WHERE isFeatured <> @isFeatured)
			EXEC dbo.sw_toggleFeaturedProgram @orgCode=@catalogOrgCode, @programType='swb', @programID=@bundleID, @isFeatured=@isFeatured, @recordedByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	-- audit log
	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Display for No-Charge Enrollments changed from ' + 
		CASE WHEN freeRateDisplay = 'FREE' THEN '[Display amount as FREE]'
		WHEN freeRateDisplay = '$0.00' THEN '[Display amount as $0.00]' 
		ELSE '[Hide amount]' 
		END +
		' to ' +
		CASE WHEN @freeRateDisplay = 'FREE' THEN '[Display amount as FREE]'
		WHEN @freeRateDisplay = '$0.00' THEN '[Display amount as $0.00]' 
		ELSE '[Hide amount]' 
		END
	FROM #tblExistingSettings
	WHERE bundleID = @bundleID
	AND freeRateDisplay <> @freeRateDisplay;

	IF @allowCatalog is not null BEGIN
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'I want to sell this webinar in the catalog: setting changed from [' + 
			CASE WHEN allowCatalog = 1 THEN 'Enabled' ELSE 'Disabled' END + '] to [' +
			CASE WHEN @allowCatalog = 1 THEN 'Enabled' ELSE 'Disabled' END + '].'
		FROM #tblExistingSettings
		WHERE bundleID = @bundleID
		AND allowCatalog <> @allowCatalog;

		IF @allowCatalog = 1 AND EXISTS(SELECT 1 FROM #tblExistingSettings WHERE bundleID = @bundleID AND allowCatalog = 1) BEGIN
			INSERT INTO #tmpLogMessages(msg)
			SELECT 'Catalog Start Date changed from ' + ISNULL(CONVERT(VARCHAR(20),dateCatalogStart,120),'[blank]') + ' to ' + ISNULL(CONVERT(VARCHAR(20),@dateCatalogStart,120),'[blank]') + '.'
			FROM #tblExistingSettings
			WHERE bundleID = @bundleID
			AND ISNULL(dateCatalogStart,'') <> ISNULL(@dateCatalogStart,'');

			INSERT INTO #tmpLogMessages(msg)
			SELECT 'Catalog End Date changed from ' + ISNULL(CONVERT(VARCHAR(20),dateCatalogEnd,120),'[blank]') + ' to ' + ISNULL(CONVERT(VARCHAR(20),@dateCatalogEnd,120),'[blank]') + '.'
			FROM #tblExistingSettings
			WHERE bundleID = @bundleID
			AND ISNULL(dateCatalogEnd,'') <> ISNULL(@dateCatalogEnd,'');

			INSERT INTO #tmpLogMessages(msg)
			SELECT 'Registrant must qualify for rate based on their group membership: Setting changed from [' + 
				CASE WHEN isPriceBasedOnActual = 1 THEN 'Enabled' ELSE 'Disabled' END + '] to [' +
				CASE WHEN @isPriceBasedOnActual = 1 THEN 'Enabled' ELSE 'Disabled' END + '].'
			FROM #tblExistingSettings
			WHERE bundleID = @bundleID
			AND isPriceBasedOnActual <> @isPriceBasedOnActual;
		END
	END

	IF @allowCatalog is null and @isPriceBasedOnActual is not null
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Registrant must qualify for rate based on their group membership: Setting changed from [' + 
			CASE WHEN isPriceBasedOnActual = 1 THEN 'Enabled' ELSE 'Disabled' END + '] to [' +
			CASE WHEN @isPriceBasedOnActual = 1 THEN 'Enabled' ELSE 'Disabled' END + '].'
		FROM #tblExistingSettings
		WHERE bundleID = @bundleID
		AND isPriceBasedOnActual <> @isPriceBasedOnActual;

	IF @revenueGLAccountID is not null
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Revenue GL Override changed from [' + ISNULL(NULLIF(rgl1.thePathExpanded,''),'blank') + '] to [' + ISNULL(NULLIF(rgl2.thePathExpanded,''),'blank') + '].'
		FROM #tblExistingSettings AS tmp
		LEFT OUTER JOIN memberCentral.dbo.fn_getRecursiveGLAccounts(@orgID) as rgl1 on rgl1.GLAccountID = tmp.revenueGLAccountID
		LEFT OUTER JOIN memberCentral.dbo.fn_getRecursiveGLAccounts(@orgID) as rgl2 on rgl2.GLAccountID = @revenueGLAccountID
		WHERE bundleID = @bundleID
		AND ISNULL(revenueGLAccountID,'') <> ISNULL(@revenueGLAccountID,'');

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SET @msgjson = 'Catalog Details for swb-' + CAST(@bundleID AS VARCHAR(10)) + ' has been updated.' + @crlf + 'The following changes have been made:';

		SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
		FROM #tmpLogMessages
		WHERE msg IS NOT NULL;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES ('{ "c":"auditLog", "d": {
			"AUDITCODE":"SW",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
	END 

	IF OBJECT_ID('tempdb..#tblExistingSettings') IS NOT NULL
		DROP TABLE #tblExistingSettings;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
