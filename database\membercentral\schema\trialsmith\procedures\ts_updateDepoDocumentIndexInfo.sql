ALTER PROC dbo.ts_updateDepoDocumentIndexInfo
    @documentID int,
    @transcript_date varchar(25),
    @case_citation varchar(255),
    @jurisdiction varchar(50),
    @expert_firstname varchar(500),
    @expert_middlename varchar(50),
    @expert_lastname varchar(500),
    @expert_full_name varchar(500)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
    DECLARE @valid_date DATE;
    DECLARE @valid_jurisdiction varchar(2);

    SET @valid_date = TRY_CAST(@transcript_date AS DATE);
    SET @valid_jurisdiction = CASE
        WHEN LEN(@jurisdiction) > 2 THEN
            (SELECT Code FROM dbo.states WHERE Name = @jurisdiction)
        WHEN LEN(@jurisdiction) = 2 THEN
            (SELECT Code FROM dbo.states WHERE Code = UPPER(@jurisdiction))
        ELSE NULL
    END;

    UPDATE dbo.depoDocuments
    SET
        ExpertName = @expert_full_name,
        fname = NULLIF(@expert_firstname,''),
        mname = NULLIF(@expert_middlename,''),
        lname = NULLIF(@expert_lastname,''),
        STYLE = @case_citation,
        Jurisdiction =  @valid_jurisdiction,
        DateLastmodified = getdate(),
        DocumentDate = @valid_date
    WHERE DocumentID = @documentID
    AND DocumentTypeID = 1
    AND STYLE IS NULL
    AND fname IS NULL
    AND lname IS NULL;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
