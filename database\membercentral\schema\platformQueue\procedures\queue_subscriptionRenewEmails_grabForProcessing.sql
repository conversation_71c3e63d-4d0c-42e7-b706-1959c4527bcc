ALTER PROC dbo.queue_subscriptionRenewEmails_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @environmentName varchar(50), @environmentID int;
	EXEC dbo.queue_getQueueTypeID @queueType='subscriptionRenewEmails', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	SELECT @environmentName = tier FROM membercentral.dbo.fn_getServerSettings();
	SELECT @environmentID = environmentID FROM membercentral.dbo.platform_environments where environmentName = @environmentName;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL
		DROP TABLE #tmpSubscribers;
	CREATE TABLE #tmpSubscribers (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpSubscribers
	FROM dbo.queue_subscriptionRenewEmails AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_subscriptionRenewEmails
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	select distinct qid.itemID, qid.orgID, qid.siteID, s.subscriberID, m.activeMemberID as memberID, subs.renewEmailTemplateID,
		se.siteID, se.siteCode, se.subscriptionsSiteResourceID, sh.hostname as mainHostname,
		se.useRemoteLogin, se.siteName, se.defaultLanguageId,
		n.supportProviderEmail, n.emailFrom as networkEmailFrom, oi.organizationName as orgName
	FROM #tmpSubscribers as tmp
	INNER JOIN dbo.queue_subscriptionRenewEmails as qid ON qid.itemID = tmp.itemID
	inner join membercentral.dbo.sub_subscribers as s on s.orgID = qid.orgID
		and s.subscriberID = qid.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
	inner join membercentral.dbo.sites as se on se.siteID = qid.siteID
	inner join membercentral.dbo.siteHostnames as sh on sh.siteID = se.siteID
	inner join membercentral.dbo.siteEnvironments senv on senv.siteID=sh.siteID
		and senv.environmentID = @environmentID
		and senv.mainHostnameID = sh.hostNameID
	inner join membercentral.dbo.networkSites as ns on ns.siteID = se.siteID
		and ns.isLoginNetwork = 1
	inner join membercentral.dbo.networks as n on n.networkID = ns.networkID
	inner join membercentral.dbo.organizations as o on o.orgid = se.orgid
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID
		and oi.orgIdentityID = o.defaultOrgIdentityID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL
		DROP TABLE #tmpSubscribers;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
