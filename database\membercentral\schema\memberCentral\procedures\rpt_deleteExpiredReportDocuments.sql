ALTER PROC dbo.rpt_deleteExpiredReportDocuments
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @30DaysAgo datetime = DATEADD(DAY,-30,GETDATE());

	IF OBJECT_ID('tempdb..#tmpDocumentsToDelete') IS NOT NULL
		DROP TABLE #tmpDocumentsToDelete;
	CREATE TABLE #tmpDocumentsToDelete (documentID int, siteID int);

	INSERT INTO #tmpDocumentsToDelete (documentID, siteID)
	SELECT d.documentID, d.siteID
	FROM dbo.rpt_documents as rd 
	INNER JOIN dbo.cms_documents AS d ON d.documentID = rd.documentID
	WHERE rd.dateExpire <= @30DaysAgo;

	SET @itemCount = @@ROWCOUNT;
    
	IF @itemCount > 0 BEGIN
		EXEC dbo.cms_deleteDocuments;

		DELETE rd
		FROM dbo.rpt_documents as rd
		INNER JOIN #tmpDocumentsToDelete as tmp on tmp.documentID = rd.documentID;
	END		
	
	IF OBJECT_ID('tempdb..#tmpDocumentsToDelete') IS NOT NULL
		DROP TABLE #tmpDocumentsToDelete;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
