ALTER PROC dbo.tr_changeInvoiceNumberPrefixFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusProcessing int, @itemStatus int, @orgID int,
		@invoiceID int, @prefix varchar(10);

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='invNumPrefix', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;

	select @itemStatus = statusID, @orgID = orgID, @invoiceID = invoiceID, @prefix = prefix
	from platformQueue.dbo.queue_invNumPrefix
	where itemID = @itemID;

	-- if itemID is not readyToProcess, kick out now
	IF @itemStatus <> @statusReady
		RAISERROR('Item not in readyToProcess state',16,1);

	-- update to processingItem
	UPDATE platformQueue.dbo.queue_invNumPrefix
	SET statusID = @statusProcessing,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- set new fullinvoicenumber based on new prefix
	IF @prefix <> ''
		UPDATE dbo.tr_invoices
		SET fullInvoiceNumber = @prefix + '-' + RIGHT('00000000' + CAST(invoiceNumber AS VARCHAR(8)), 8)
		WHERE orgID = @orgID
		AND invoiceID = @invoiceID;
	ELSE		
		UPDATE dbo.tr_invoices
		SET fullInvoiceNumber = RIGHT('00000000' + CAST(invoiceNumber AS VARCHAR(8)), 8)
		WHERE orgID = @orgID
		AND invoiceID = @invoiceID;

	-- Delete the queue item after processing
	DELETE FROM platformQueue.dbo.queue_invNumPrefix
	WHERE itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
