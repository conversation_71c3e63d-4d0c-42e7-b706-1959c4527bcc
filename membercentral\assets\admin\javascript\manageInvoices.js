function hideButtonBar() {
	$('#invoiceButtonBar').hide();
	$('#invoiceButtonBarWait').show().html('<div style="c"><i class="fa-light fa-circle-notch fa-spin fa-2x"></i> &nbsp; <b>Please wait...</b></div>');
}
function showButtonBar() {
	$('#invoiceButtonBarWait').hide().html('');
	$('#invoiceButtonBar').show();
}
function reloadData(resetSelections) { filterInvoiceGrid(resetSelections); }

function editMember(mid) {
	window.open(link_mtl + '&memberID=' + mid + '&tab=transactions','_blank');
}

function closeMoveIT(vid) {
	reloadData(true);
	if (typeof vid == 'undefined') MCModalUtils.hideModal();
}

function closeAdjustTransaction() { 
	reloadData(true);
	MCModalUtils.hideModal();
}

function closeVoidTransaction(vid) { 
	reloadData(true);
	if (typeof vid == 'undefined') MCModalUtils.hideModal();
}

function viewInvoiceInfo(vid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Invoice ' + $('#invoiceRow_'+vid).data('invoiceNumber'),
		iframe: true,
		contenturl: link_vii + '&vid=' + vid
	});
}

function createInvoice() {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Create Invoice',
		iframe: true,
		contenturl: link_ei + '&vid=0',
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmInvoice :submit").click',
			extrabuttonlabel: 'Save Invoice'
		}
	});
}
function doneEditInvoice() { reloadData(true); }

function addPayment(po) {
	mca_addPayment(po,link_ap);
}
function closeAddPayment(po) { 
	reloadData(true); 
	if (transAllocatePayment==0)
		MCModalUtils.hideModal();
	else
		allocIndivPayment(po);
}
function allocIndivPayment(po) {
	mca_allocIndivPayment(po,link_allocp);
}
function closeAllocPayment() { reloadData(true); MCModalUtils.hideModal(); }
function updateField(fldID, mID, mNum, mName) {
	var fld = $('#'+fldID);
	var fldName = $('#associatedVal');
	fld.val(mID);
	$(".trAssoc").hide();
	if ((mName.length > 0) && (mNum.length > 0)) {
		fldName.html('<div style="padding:8px 0;"><b>' + mName + ' (' + mNum + ')</b></div>');
		$(".trAssoc").show();
		resetAssocOptions();
		$('#associatedGroupID').val("");
	} else {
		fldName.html('');
	}
}

function selectMemberInvFilter() {
	var selhref = link_msgtl + '&mode=direct&fldName=associatedMemberID&dispTitle=';
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter Invoices by Member',
		iframe: true,
		contenturl: selhref,
		strmodalfooter : {
			classlist: 'd-none'
		}
	});
}

function closeBox() { MCModalUtils.hideModal(); }

function updateGroupField(fldID,gID,gPath) {
	var fld = $('#'+fldID);
	var fldName = $('#associatedVal');
	fld.val(gID);
	$(".trAssoc").hide();
	if (gPath.length > 0) {
		var newgPath = gPath.split("\\");
			newgPath.shift();
			newgPath = newgPath.join(" \\ ");
		fldName.html('<div style="padding:8px 0;"><b>' + newgPath + '</b></div>');
		$(".trAssoc").show();
		resetAssocOptions();
		$('#associatedMemberID').val("");
	} else {
		fldName.html('');
	}
}

function selectGroupInvFilter() {
	var selhref = link_gsgtl + '&mode=direct&fldName=associatedGroupID&retFunction=top.updateGroupField&dispTitle=' + escape('Filter Invoices by Group');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter Invoices by Group',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}

function resetAssocOptions() {
	$("#linkedRecordsAll").attr("checked",true);
	$(".linkedRecordOptions").each(function(){
		$(this).attr("checked",true);
	});
}

function checkFilterForm() {
	var das = $('#dueAmtStart');
	var dae = $('#dueAmtEnd');
	var ias = $('#invAmtStart');
	var iae = $('#invAmtEnd');
	if (das.val().length > 0) das.val(formatCurrency(das.val()));
	if (dae.val().length > 0) dae.val(formatCurrency(dae.val()));
	var lad = formatCurrency(das.val()).replace(/\,/g,'');
	var had = formatCurrency(dae.val()).replace(/\,/g,'');
	if (parseFloat(lad) > parseFloat(had)) {
		alert('The low due amount must be lower than the high due amount if using Amount Due to filter data.');
		return false;
	}
	if (ias.val().length > 0) ias.val(formatCurrency(ias.val()));
	if (iae.val().length > 0) iae.val(formatCurrency(iae.val()));
	var lai = formatCurrency(ias.val()).replace(/\,/g,'');
	var hai = formatCurrency(iae.val()).replace(/\,/g,'');
	if (parseFloat(lai) > parseFloat(hai)) {
		alert('The low invoice amount must be lower than the high invoice amount if using Invoice Amount to filter data.');
		return false;
	}
	if ($('input:radio[name=assocType]:checked').val() != undefined && !$(".trAssoc").is(":visible")) {
		alert('No specific member or group was selected.');
		return false;
	}
	return true;
}

function getFilters() {
	var objParams = {};

	var s = $('#statusID').val() || '';
	if (s.length > 0) { objParams.statuses = s.toString(); } else { objParams.statuses = ''; }

	var cof = $('#cardOnFile').val() || '';
	if (cof.length > 0) { objParams.cardOnFile = cof.toString(); } else { objParams.cardOnFile = ''; }

	var ip = $('#invProfile').val() || '';
	if (ip.length > 0) { objParams.invoiceProfiles = ip.toString(); } else { objParams.invoiceProfiles = ''; }

	objParams.billedStartDate = $('#billeddateStart').val();
	objParams.billedEndDate = $('#billeddateEnd').val();
	objParams.dueStartDate = $('#duedateStart').val();
	objParams.dueEndDate = $('#duedateEnd').val();
	objParams.invoiceNumber = $.trim($('#invoiceNumber').val());
	objParams.trDetail = $('#trDetail').val();
	objParams.dueAmtStart = $('#dueAmtStart').val().replace(/\,/g,'');
	objParams.dueAmtEnd = $('#dueAmtEnd').val().replace(/\,/g,'');
	objParams.invAmtStart = $('#invAmtStart').val().replace(/\,/g,'');
	objParams.invAmtEnd = $('#invAmtEnd').val().replace(/\,/g,'');
	objParams.assocType = ($('input:radio[name=assocType]:checked').val() != undefined ? $('input:radio[name=assocType]:checked').val() : "");
	objParams.associatedMemberID = $('#associatedMemberID').val();
	objParams.associatedGroupID = $('#associatedGroupID').val();

	var lr = "";
	var ar = "";
	if ($(".trAssoc").is(":visible") == true) {
		if ($('input:radio[name=linkedRecords]:checked').val() != undefined)
			lr = $('input:radio[name=linkedRecords]:checked').val();
		if ($('input:checkbox[name=assocRules]:checked').val() != undefined) {
			var linkedRecordOptions = [];
			$('.linkedRecordOptions').each(function() {
				if (this.checked) linkedRecordOptions.push(this.value);
			});
			if (linkedRecordOptions.length > 0) ar = linkedRecordOptions.join(",");
		}
	}
	objParams.linkedRecords = lr;
	objParams.linkedRecordOptions = ar;

	objParams.chkAll = checkAllInvs;
	objParams.invoiceIDList = checkAllInvs ? '' : arrChkedInvs.join(',');
	objParams.notInvoiceIDList = checkAllInvs ? arrUnchkedInvs.join(',') : '';

	return objParams;
}
function clearFilterInvoices() {
	$('#frmFilter')[0].reset();
	$('#aClearAssocType').click();
	$('#frmFilter [data-toggle="custom-select2"]').trigger('change');
	filterInvoiceGrid(true);
}
function filterInvoiceGrid(resetSelections) {
	if (!checkFilterForm()) return false;

	if(resetSelections) resetAllRecordSelections(true);
	reloadInvoiceListTable();
}
function resetAllRecordSelections(f) {
	$('#masterCheckBox').prop('checked',f);
	doCheckAllInv(f);
}
function generateInvoiceBundle() {
	if (!checkFilterForm()) return false;

	if (!existsInvoicesOnFilteredGrid()) return false;

	var opts = getFilters();

	var localOptions = '';
	localOptions += '&statusID=' + opts.statuses;
	localOptions += '&cof=' + opts.cardOnFile;
	localOptions += '&ip=' + opts.invoiceProfiles;
	localOptions += '&bs=' + opts.billedStartDate;
	localOptions += '&be=' + opts.billedEndDate;
	localOptions += '&ds=' + opts.dueStartDate;
	localOptions += '&de=' + opts.dueEndDate;
	localOptions += '&vn=' + opts.invoiceNumber;
	localOptions += '&trd=' + opts.trDetail;
	localOptions += '&as=' + opts.dueAmtStart;
	localOptions += '&ae=' + opts.dueAmtEnd;
	localOptions += '&ias=' + opts.invAmtStart;
	localOptions += '&iae=' + opts.invAmtEnd;
	localOptions += '&at=' + opts.assocType;
	localOptions += '&am=' + opts.associatedMemberID;
	localOptions += '&ag=' + opts.associatedGroupID;
	localOptions += '&lr=' + opts.linkedRecords;
	localOptions += '&ar=' + opts.linkedRecordOptions;
	localOptions += '&chkall=' + opts.chkAll;
	localOptions += '&incids=' + opts.invoiceIDList;
	localOptions += '&excids=' + opts.notInvoiceIDList;

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Download Invoices',
		iframe: true,
		contenturl: link_gib + '&' + localOptions,
		strmodalfooter: {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: 'generateInvoiceBundleButtonHandler',
			extrabuttonlabel: 'Generate Invoices',
		}
	});
}
function generateInvoiceBundleButtonHandler(){
	$('#MCModalBodyIframe')[0].contentWindow.doGenerateInvoiceBundle();
}
function emailInvoices() {
	if (!checkFilterForm()) return false;

	if (!existsInvoicesOnFilteredGrid()) return false;

	var opts = getFilters();

	var localOptions = '';
	localOptions += '&statusID=' + opts.statuses;
	localOptions += '&cof=' + opts.cardOnFile;
	localOptions += '&ip=' + opts.invoiceProfiles;
	localOptions += '&bs=' + opts.billedStartDate;
	localOptions += '&be=' + opts.billedEndDate;
	localOptions += '&ds=' + opts.dueStartDate;
	localOptions += '&de=' + opts.dueEndDate;
	localOptions += '&vn=' + opts.invoiceNumber;
	localOptions += '&trd=' + opts.trDetail;
	localOptions += '&as=' + opts.dueAmtStart;
	localOptions += '&ae=' + opts.dueAmtEnd;
	localOptions += '&ias=' + opts.invAmtStart;
	localOptions += '&iae=' + opts.invAmtEnd;
	localOptions += '&at=' + opts.assocType;
	localOptions += '&am=' + opts.associatedMemberID;
	localOptions += '&ag=' + opts.associatedGroupID;
	localOptions += '&lr=' + opts.linkedRecords;
	localOptions += '&ar=' + opts.linkedRecordOptions;
	localOptions += '&chkall=' + opts.chkAll;
	localOptions += '&incids=' + opts.invoiceIDList;
	localOptions += '&excids=' + opts.notInvoiceIDList;

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'E-mail Filtered Invoices',
		iframe: true,
		contenturl: link_mei + localOptions,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function massCloseInvoices() {
	if (!existsInvoicesOnFilteredGrid()) return false;
	var msg = '<div class="alert alert-warning">This process will close the <i>open</i> and <i>pending</i> invoices based on your current invoice filters. Any <i>closed</i>, <i>delinquent</i>, or <i>paid</i> invoices in the current invoice filters will remain untouched.</div>';
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Mass Close Invoices',
		iframe: false,
		strmodalbody: { 
			content: msg
		},
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: 'doMassCloseInvoices',
			extrabuttonlabel: 'Yes, I Understand'
		}
	});
}
function doMassCloseInvoices() 	{
	if (!checkFilterForm()) { MCModalUtils.hideModal();return false; }
	var opts = getFilters();
	hideButtonBar();
	$('#btnMCModalSave').prop('disabled',true);
	var doMassCloseInvoicesresult = function(r) {
		if (!r.success || r.success.toLowerCase() != 'true') {
			alert('Unable to complete this action.');
		}
		showButtonBar();
		MCModalUtils.hideModal();
		reloadData(true);
	};
	TS_AJX('INVOICEADMIN','doMassCloseInvoices',opts,doMassCloseInvoicesresult,doMassCloseInvoicesresult,25000,doMassCloseInvoicesresult);
}
function manuallyProcessPayments() {
	if (!existsInvoicesOnFilteredGrid()) return false;
	var msg = '<div class="alert alert-warning"><b>Are you sure you want to manually process the invoices based on your current invoice filters?</b><br/>We will immediately begin to process payments (including charging credit cards or bank drafts) and will mark these invoices as paid.<br/><br/>Invoices that do not have an associated payment profile ("card on file") will remain untouched.</div>';
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Mass Process Invoices',
		iframe: false,
		strmodalbody: { 
			content: msg
		},
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: 'doManuallyProcessPayments',
			extrabuttonlabel: 'Yes, I Understand'
		}
	});
}
function doManuallyProcessPayments() {
	if (!checkFilterForm()) { MCModalUtils.hideModal();return false; }
	var opts = getFilters();
	hideButtonBar();
	$('#btnMCModalSave').prop('disabled',true);
	var doManuallyProcessPaymentsresult = function(r) {
		if (!r.success || r.success.toLowerCase() != 'true') {
			alert('Unable to complete this action.');
		}
		showButtonBar();
		MCModalUtils.hideModal();
		resetAllRecordSelections(false);
		reloadData(false);
	};
	TS_AJX('INVOICEADMIN','doManuallyProcessPayments',opts,doManuallyProcessPaymentsresult,doManuallyProcessPaymentsresult,25000,doManuallyProcessPaymentsresult);
}
function updateInvoiceMessages() {
	if (!existsInvoicesOnFilteredGrid()) return false;
	var msg = '<div class="alert alert-info">This process will update invoices based on your current invoice filters with the latest version of the end-of-invoice messages based on their invoice profile.</div>';
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Mass Update End-of-Invoice Messages',
		iframe: false,
		strmodalbody: { 
			content: msg
		},
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: 'doUpdateInvoiceMessages',
			extrabuttonlabel: 'Yes, I Understand'
		}
	});
}
function doUpdateInvoiceMessages() {
	if (!checkFilterForm()) { MCModalUtils.hideModal();return false; }
	var opts = getFilters();
	hideButtonBar();
	$('#btnMCModalSave').prop('disabled',true);
	var doUpdateInvoiceMessagesresult = function(r) { 
		showButtonBar(); 
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			resetAllRecordSelections(false);
			reloadData(false);
		} else {
			alert('Unable to complete this action.');
		}
	};
	TS_AJX('INVOICEADMIN','doMassRefreshInvoiceMessages',opts,doUpdateInvoiceMessagesresult,doUpdateInvoiceMessagesresult,25000,doUpdateInvoiceMessagesresult);
}
function changeInvoiceDates() {
	if (!checkFilterForm()) return false;
	if (!existsInvoicesOnFilteredGrid()) return false;
	
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Mass Change Invoice Dates',
		iframe: true,
		contenturl: link_cid,
		strmodalfooter : {
			classlist: 'text-right'
		}
	});
}
function filterInvoices() {
	if (!$('#divFilterForm').is(':visible')) {
		$('#divFilterForm').show();
	}
}
function setPayProfiles() {
	if (!checkFilterForm()) return false;
	if (!existsInvoicesOnFilteredGrid()) return false;

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Mass Change Invoice Payment Profiles',
		iframe: true,
		contenturl: link_sipp,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.doChangePayProfiles',
			extrabuttonlabel: 'Change Payment Profiles'
		}
	});
}
function prepFilterForm() {
	mca_setupDatePickerRangeFields('duedateStart','duedateEnd');
	mca_setupDatePickerRangeFields('billeddateStart','billeddateEnd');
	mca_setupCalendarIcons('frmFilter');
	mca_setupSelect2();

	$(".trAssoc").hide();
	$("#aClearAssocType").hide();

	$(".assocType").click(function(){
		var assocType = $('input:radio[name=assocType]:checked').val();
		if ( assocType != undefined) {
			if (assocType == "group") selectGroupInvFilter();
			else selectMemberInvFilter();
		}
		$("#aClearAssocType").show();
	});

	$("#aClearAssocType").click(function(){
		$(".assocType").each(function(){
			$(this).attr("checked",false);
		});
		$('#associatedVal').html("");
		$('#associatedMemberID').val("");
		$('#associatedGroupID').val("");
		resetAssocOptions();
		$(".trAssoc").hide();
		$("#aClearAssocType").hide();
	});
}
function existsInvoicesOnFilteredGrid() {
	if(totalInvCount == 0) {
		alert('There are no invoices to act upon.');
		return false;
	}
	else if((!checkAllInvs && arrChkedInvs.length == 0) || (checkAllInvs && arrUnchkedInvs.length == totalInvCount)){
		alert('There are no selected invoices to act upon.');
		return false;
	} else {
		return true;
	}
}
function showEmailInvoicesMergeInstr(u) {
	$('#divComposeMessageForm,#divPreviewMessageForm').addClass('d-none');
	$('#divMergeCodeScreen').removeClass('d-none');
	$('#divMergeCodeScreen div#divMergeCodeScreenContent').html(mca_getLoadingHTML('Loading the merge codes...')).load(u);
}
function showEmailInvoicesComposeMessageForm() {
	$('#divMergeCodeScreen div#divMergeCodeScreenContent').html('');
	$('#divMergeCodeScreen,#divPreviewMessageForm').addClass('d-none');
	$('#divComposeMessageForm').removeClass('d-none');
}
function hideEmailInvoiceAlerts() {
	mca_hideAlert('err_email_compose');
	mca_hideAlert('err_email_preview');
	mca_hideAlert('err_email_templatesave');
}
function loadEmailInvoicesEmailTemplateContent() {
	hideEmailInvoiceAlerts();
	$('#divEmailFields').addClass('d-none');

	var result = function(r) {
		$('#loadingTemp').addClass('d-none');
		if (r.success && r.success.toLowerCase() == 'true'){
			try { CKEDITOR.instances['templateContent'].resize('100%','100%'); } catch(e) {};
			CKEDITOR.instances['templateContent'].setData(r.emailcontent);
			$('#emailSubject').val(r.subjectline);
			$('#emailReplyTo').val(r.emailfrom);
			$('#emailFromName').val(r.emailfromname);
			$('#divEmailFields').removeClass('d-none');
		} else {
			mca_showAlert('err_email_compose', 'An error occurred while loading the template.');
		}
	};

	try { CKEDITOR.instances['templateContent'].setData(''); } catch(e) {};
	$('#emailSubject, #emailReplyTo, #emailFromName').val('');
	if ($('#fEmailTemplateID').val() != 0) {
		$('#loadingTemp').removeClass('d-none');
		var objParams = { emailTemplateID:$('#fEmailTemplateID').val() };
		TS_AJX('MASSEMAIL','getEmailTemplateContent',objParams,result,result,10000,result);
	}
}
function editEmailInvoicesInvoiceMessage() {
	hideEmailInvoiceAlerts();
	$('#btnEmailInvoice').prop('disabled',false);
	$('#divComposeMessageForm').removeClass('d-none');
	$('#divPreviewMessageForm, #divTestEmail').addClass('d-none');
	$('#divEmailDisp, #spFrom, #spSubject').html('');
	$('#spTestBtn').addClass('d-none');
}
function showEmailInvoicesBuildEmailOptions(val) {
	hideEmailInvoiceAlerts();
	$('#emailFromName, #emailReplyTo, #emailSubject').val('');
	try { CKEDITOR.instances['templateContent'].setData(''); } catch(e) {};
	$('#divBuildEmailTemplate, #divEmailFields').addClass('d-none');

	if (val == 'new') {
		$("#fEmailTemplateID").val(null).trigger('change');
		if (mc_numTemplates > 0) $('#divEmailTemplateSel').addClass('d-none');
		try { CKEDITOR.instances['templateContent'].resize('100%','100%'); } catch(e) {};
		$('#divEmailFields, #divBuildEmailTemplate').removeClass('d-none');
		$('#emailFromName').val(mc_orgname);
	} else {
		$('#divEmailTemplateSel, #divBuildEmailTemplate').removeClass('d-none');
	}
}
function showCustomEmailInvoiceTemplates(r) {
	var arrReq = [];
	var emailList = $('#recipientEmailList').val().trim().split(';');
	
	if(emailList.length == 0) arrReq[arrReq.length] = 'Enter the recipient e-mail address.';
	else{
		var valid = true;
		$.each(emailList, function(index, email) {
			email = email.trim();
			var emailRegEx = new RegExp(r,"i");

			if (!(emailRegEx.test(email))) {
				valid = false;
				return false;
			}
		});
		if(!valid) arrReq[arrReq.length] = 'Some email addresses are invalid.';
	}
	
	if(arrReq.length){
		mca_showAlert('err_email_preview', arrReq.join('<br/>'), true);
		return false;
	}


	if (mc_numTemplates > 0) {
		if ($('select#fEmailTemplateID').length && $('#fEmailTemplateID').val() != '') {
			$('b#selTemplateOption').html($('#fEmailTemplateID option:selected').text());
			$('#selTemplateRow').removeClass('d-none');
		}
	}

	$('#divPreviewMessageForm').addClass('d-none');
	$('#divTemplateSaveOptions').removeClass('d-none');
}
function hideEmailInvoicesNewTemplateSaveDetails() {
	hideEmailInvoiceAlerts();
	$('#templateName').val('');
	$('#selCategory').val('');
	$('#newTemplateDetailsRow').addClass('d-none');
}
function showEmailInvoicesNewTemplateSaveDetails() {
	hideEmailInvoiceAlerts();
	$('#newTemplateDetailsRow').removeClass('d-none');
	changeEmailInvoicesTemplateCategoryOptions();
}
function validateMassInvoiceEmailForm() {
	$('#btnSubmit').text('Please Wait..').attr('disabled',true);
	var arrReq = new Array();
	if ($('input[name="saveTemplateOption"]:checked').val() == 1 && $('#templateName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the template name.';
	if ($('input[name="massEmailScheduling"]:checked').val() == 'later' && $('#emailDateScheduled').val().length == 0) arrReq[arrReq.length] = 'Enter the scheduled sending date.';
	
	if(arrReq.length){
		$('#btnSubmit').text('Send E-mails').attr('disabled',false);
		mca_showAlert('err_email_preview', arrReq.join('<br/>'), true);
		return false;
	}
	return true;
}
function changeEmailInvoicesTemplateCategoryOptions() {
	if($('#selCategory').val() == 0)
		$('#divNewCategory').removeClass('d-none');
	else {
		$('#newCategoryName').val('');
		$('#divNewCategory').addClass('d-none');
	}
}
function validateInvoiceEmailComposeForm(r){
	var arrReq = new Array();
	var emailRegEx = new RegExp(r,"i");
	if($('#emailFromName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the e-mail from.';
	else if($('#emailFromName').val().trim().length > 0 && emailRegEx.test($('#emailFromName').val())) arrReq[arrReq.length] = 'E-mail From Name cannot contain an email address.';
	
	if($('#emailReplyTo').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the e-mail reply-to address.';
	else if ($('#emailReplyTo').val().trim().length > 0 && !(emailRegEx.test($('#emailReplyTo').val()))) arrReq[arrReq.length] = 'Enter a valid e-mail reply-to address.';
	
	if($('#emailSubject').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the e-mail subject.';
	if($.trim(CKEDITOR.instances['templateContent'].getData()).length == 0) arrReq[arrReq.length] = 'Enter the e-mail content.';

	if(arrReq.length){
		mca_showAlert('err_email_compose', arrReq.join('<br/>'), true);
		return false;
	}

	return true;
}
function previewCustomEmailInvoiceMessage(r) {
	hideEmailInvoiceAlerts();
	$('#btnEmailInvoice').prop('disabled',true);

	if(!validateInvoiceEmailComposeForm(r)) {
		$('#btnEmailInvoice').prop('disabled',false);
		return false;
	}

	showCustomEmailInvoiceMessage(invIDList);
}
function showCustomEmailInvoiceMessage(id) {
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			$('#divPreviewEmailMessageLoading').addClass('d-none');
			$('#divTestEmail, #spTestBtn').removeClass('d-none');
			$('#divEmailDisp').html(r.templatedisp);
			$('#spFrom').html($('#emailReplyTo').val() + ' (' + $('#emailFromName').val() + ')');
			$('#spSubject').html(decodeURIComponent(r.subjectline));
		} else {
			alert('Unable to generate message preview.');
			$('#divPreviewEmailMessageLoading,#btnContinuePreview').addClass('d-none');
		}
	};

	$('#divComposeMessageForm').addClass('d-none');
	$('#divPreviewMessageForm, #divPreviewEmailMessageLoading,#btnContinuePreview').removeClass('d-none');
	$('#spTestBtn').addClass('d-none');
	$('#divTestPreviewMessage').html('').addClass('d-none');

	var templateContent = CKEDITOR.instances['templateContent'].getData();
	var objParams = { invoiceIDList:id, templateContent:encodeURIComponent(templateContent), 
		subjectLine:encodeURIComponent($('#emailSubject').val()), emailMode:$('#emailMode').val() };
	TS_AJX('INVOICEADMIN','getPreviewCustomEmailInvoiceMessage',objParams,result,result,20000,result);
}
function sendCustomTestInvoiceEmail() {
	var sendTestResult = function(r) {
		$('#btnTestTemplate').attr('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') { 
			$('#divTestPreviewMessage').html('<i class="fa-regular fa-thumbs-up fa-lg"></i> Test e-mail sent successfully');
		} else {
			$('#divTestPreviewMessage').html('Error sending test e-mail').addClass('class','text-danger font-weight-bold');
		}
	};

	$('#btnTestTemplate').attr('disabled',true);
	$('#divTestPreviewMessage').removeClass('text-danger font-weight-bold').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Sending test e-mail...').removeClass('d-none');

	var templateContent = CKEDITOR.instances['templateContent'].getData();
	var objParams = { invoiceIDList:invIDList, memberID:$('#memberID').val(), templateContent:encodeURIComponent(templateContent), 
		subjectLine:encodeURIComponent($('#emailSubject').val()), emailFromName:$('#emailFromName').val(), emailfrom:$('#emailReplyTo').val(),
		emailMode:$('#emailMode').val() };
	TS_AJX('INVOICEADMIN','sendCustomInvoiceTestEmailMessage',objParams,sendTestResult,sendTestResult,40000,sendTestResult);
}
function validateInvoiceEmailForm(r) {
	hideEmailInvoiceAlerts();

	if(!validateInvoiceEmailComposeForm(r) || $('#divTemplateSaveOptions').hasClass('d-none')) {
		return false;
	}
	
	$('#btnSubmit').text('Please Wait..').attr('disabled',true);
	
	var arrReq = new Array();
	if ($('input[name="saveTemplateOption"]:checked').val() == 1) {
		if ($('#templateName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the template name.';
		if (Number($('#selCategory').val()) == 0 && $('#newCategoryName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the new template category name.';
	}
	if ($('input[name="massEmailScheduling"]:checked').val() == 'later' && $('#emailDateScheduled').val().length == 0) arrReq[arrReq.length] = 'Enter the scheduled sending date.';
	
	if(arrReq.length){
		$('#btnSubmit').text('Send E-mails').attr('disabled',false);
		mca_showAlert('err_email_templatesave', arrReq.join('<br/>'), true);
		return false;
	}
	return true;
}
function doCheckAllInv(chk) {
	arrUnchkedInvs = [];
	arrChkedInvs = [];
	checkAllInvs = chk ? 1 : 0;
	if (chk) {
		$("#masterCheckBox").val(1);
		$(".invoiceCheckbox:checkbox").prop('checked', true);
	}else{
		$("#masterCheckBox").val(0);
		$(".invoiceCheckbox:checkbox").prop('checked', false);
	}
	checkInvoiceEntry();
}
function onCheckInvoiceEntry(thisObj) {
	var invoiceID = $(thisObj).val();
	if ($(thisObj).is(':checked')) {
		if(arrUnchkedInvs.includes(invoiceID)){
			arrUnchkedInvs = $.grep(arrUnchkedInvs, function(value) {
				return value != invoiceID;
			});
		}		
		if(!arrChkedInvs.includes(invoiceID)){
			arrChkedInvs.push(invoiceID);
		}
	}else{
		if(arrChkedInvs.includes(invoiceID)){
			arrChkedInvs = $.grep(arrChkedInvs, function(value) {
				return value != invoiceID;
			});
		}
		if(!arrUnchkedInvs.includes(invoiceID)){
			arrUnchkedInvs.push(invoiceID);
		}
	}
	checkInvoiceEntry();
}
function setSelectedInvCountDisplay(c){
	if(totalInvCount != 0){
		$('.selInvCountDisp').html('<b>' + totalInvCount + '</b> ' + (totalInvCount == 1 ? "Invoice" : "Invoices") + ' found / <b>' + c + '</b> ' + (c == 1 ? "Invoice" : "Invoices") + ' selected').show();
	}else{
		$('.selInvCountDisp').html('');
	}
}
function checkInvoiceEntry(){
	$.each(arrUnchkedInvs, function( index, value ) {
		$(".invoiceCheckbox:checkbox[value="+value+"]").prop('checked', false);
	});
	var displayCount = 0;
	if ($('#masterCheckBox').is(':checked')) {
		displayCount = totalInvCount - arrUnchkedInvs.length;
	} else {
		var selectedIds = arrChkedInvs.join(",");
		displayCount = selectedIds.length ? selectedIds.split(',').length : 0;
	}
	setSelectedInvCountDisplay(displayCount);
}
function initInvoicesTable(){
	let domString = "<'row'<'col-sm-5 col-md-5'<'float-left mt-2'l><'float-left p-1 m-2 selInvCountDisp'>><'col-sm-7 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

	invoiceListTable = $('#invoiceList').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": invoiceListLink,
			"type": "post",
			"data": function(d) {
				var opts = getFilters();
				d['statusID'] = opts.statuses;
				d['cof'] = opts.cardOnFile;
				d['ip'] = opts.invoiceProfiles;
				d['bs'] = opts.billedStartDate;
				d['be'] = opts.billedEndDate;
				d['ds'] = opts.dueStartDate;
				d['de'] = opts.dueEndDate;
				d['vn'] = opts.invoiceNumber;
				d['trd'] = opts.trDetail;
				d['as'] = opts.dueAmtStart;
				d['ae'] = opts.dueAmtEnd;
				d['ias'] = opts.invAmtStart;
				d['iae'] = opts.invAmtEnd;
				d['at'] = opts.assocType;
				d['am'] = opts.associatedMemberID;
				d['ag'] = opts.associatedGroupID;
				d['lr'] = opts.linkedRecords;
				d['ar'] = opts.linkedRecordOptions;
				d['chkAll'] = opts.chkAll;
			},
			"deferLoading":400
		},
		"autoWidth": false,
		"columns": [
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						var isChecked = "";
						if($("#masterCheckBox:checkbox:checked").length){
							isChecked = "checked";
						}
						renderData += '<input type="checkbox" name="invoiceCheckbox" class="invoiceCheckbox" '+ isChecked +' value="' + data.invoiceID + '" onclick="onCheckInvoiceEntry(this);">';
					}
					return type === 'display' ? renderData : data;
				},
				'searchable': false,
				'orderable': false,
				'className': 'text-center align-top',
				"width": "5%",
			},
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						var title='Due: '+ data.dateDue + ', Billed: '+ data.dateBilled +', Created: '+data.dateCreated;
						renderData += '<div title="'+title+'">'+data.dateDue+'</div>';
						renderData += '<div class="text-dim">'+data.invoiceStatus+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%"
			},
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						var inPaymentQueue = "";
						if(data.inPaymentQueue) inPaymentQueue = " (Queued)";

						if(data.hasCard)
							renderData += '<span class="text-warning float-right ml-1" title="Pay Method Associated"><i class="fa-solid fa-credit-card"></i></span>';
						renderData += '<div>'+data.invoiceNumber+inPaymentQueue+'</div>';
						renderData += '<div>'+data.invoiceProfile+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				'className': 'align-top',
				"width": "15%"
			},
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div><a href="javascript:editMember('+data.assignedTomemberid+')" title="View Member Record for '+data.membername+'">'+data.membername+'</a></div>';
						renderData += '<div>'+data.company+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				'className': 'align-top',
				"width": "40%"
			},			
			{ "data": "InvAmt", 'className': 'align-top', "width": "10%" },
			{ "data": "InvDue", 'className': 'align-top', "width": "10%" },
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if(data.inPaymentQueue) 
							var title = "This invoice is currently queued for payment. Click to view invoice details.";
						else 
							var title = 'View invoice detail for '+data.invoiceNumber;

						renderData += '<a href="javascript:viewInvoiceInfo('+data.invoiceID+')" title="'+title+'" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>'
						if($.trim(data.addPaymentEncString).length){
							renderData += '<a href="javascript:addPayment(\''+data.addPaymentEncString+'\');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Pay Invoice"><i class="fa-solid fa-money-bill"></i></a>'
						}else{
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-money-bill"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center align-top",
				"orderable": false
			}
		],
		"order": [[1, 'desc']],
		"searching": false,
		"drawCallback": function(settings) {
			totalInvCount = invoiceListTable.page.info().recordsTotal;
			$(".invoiceCheckbox:checkbox").prop('checked', checkAllInvs);

			if(checkAllInvs == 1) {
				$.each(arrUnchkedInvs, function( index, value ) {
					$(".invoiceCheckbox:checkbox[value="+value+"]").prop('checked', false);
				});
			}
			else {
				$.each(arrChkedInvs, function( index, value ) {
					$(".invoiceCheckbox:checkbox[value="+value+"]").prop('checked', true);
				});
			}

			checkInvoiceEntry();
		}
	});
}
function reloadInvoiceListTable(){
	invoiceListTable.draw();
}