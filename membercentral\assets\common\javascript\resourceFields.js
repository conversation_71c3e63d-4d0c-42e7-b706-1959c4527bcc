function mccf_setupDateControls(scope, fldclass, fldClearClass) {
	if(scope && scope.find('.'+fldclass).length) {
		var config = { format:'M/D/YYYY', formatDate: "M/D/YYYY", timepicker:false, todayButton:false, scrollInput:false };
			config.onChangeMonth = function(ct, $input) { if (ct) $input.val(moment(ct).format(config.format)); };
			config.onChangeYear = function(ct, $input) { if (ct) $input.val(moment(ct).format(config.format)); };
		
		$.datetimepicker.setDateFormatter('moment');
		scope.find('.'+fldclass).datetimepicker('destroy').datetimepicker(config);

		scope.find('.'+fldClearClass).click(function(event){
			var linkedDateControlID = $(this).data('linkeddatecontrol');
			$('#' + linkedDateControlID).val('').change();
			event.preventDefault();
		});
	}
}
function mccf_validate_fieldIsRequired(thisField) {
	var fld = $(thisField);
	var fldName = $(thisField).attr('name');
	var displayTypeCode = fld.data('displaytypecode');
	var dataTypeCode = fld.data('datatypecode');
	var returnMsg = '';
	switch(displayTypeCode) {
		case 'TEXTBOX':
		case 'TEXTAREA':
		case 'DATE':
			if (fld.val().trim() == '') {
				returnMsg =  fld.data('requiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
			}
		break;
		case 'SELECT':
			if (fld.val() == '' && fld.find('option:not(:disabled)').length > 1) {
				returnMsg =  fld.data('requiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
			}
		break;
		case 'RADIO':
		case 'CHECKBOX':
			if ($('input[name="' + fldName + '"]').not(':disabled').length > 0 && !$('input[name="' + fldName + '"]').not(':disabled').is(':checked')) {
				returnMsg =  fld.data('requiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
			}
		break;
		case 'DOCUMENT':
			var oldFileName = fldName.replace('cf_','cf_oldDoc_');
			var deleteFileName = fldName.replace('cf_','cf_remDoc_');
			if (fld.val().trim() == '' && ( $('input[name="' + oldFileName + '"]').val() == '' || Number($('input[name="' + deleteFileName + '"]').val()) == 1 )) {
				returnMsg =  fld.data('requiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
			}
		break;
	}
	return returnMsg;
}
function mccf_validate_textControlValidInteger(thisField) {
	var returnMsg = '';
	var dataTypeDisplay = 'whole number';
	var fld = $(thisField);
	var enteredFldVal = fld.val().trim();
	var fldval = Number(enteredFldVal);
	var supportqty = fld.data('supportqty');
	if(supportqty == 1) {
		dataTypeDisplay = 'quantity';
	}

	if (fldval != '') {
		if (fldval !== parseInt(fldval) || fldval.toString() != enteredFldVal ) {
			returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('mccf_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (supportqty == 1) {
			if (fldval < 0) {
				returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('mccf_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			} else if (fldval > Number(fld.data('fieldqtymaxallowed'))) {
				returnMsg = 'Enter a quantity between 0 and ' + Number(fld.data('fieldqtymaxallowed')) + ' for ' + fld.data('mccf_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			}
		}
	}
	return returnMsg;
}
function mccf_validate_textControlValidDecimal(thisField) {
	var returnMsg = '';
	var dataTypeDisplay = 'decimal number';
	var fld = $(thisField);
	var fldval = Number(fld.val().trim());
	var supportamt = fld.data('supportamt');
	if(supportamt == 1) {
		dataTypeDisplay = 'amount';
	}

	if (fldval != '') {
		if (fldval !== parseFloat(fldval)) {
			returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('mccf_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (supportamt == 1 && parseFloat(fldval) < 0) {
			returnMsg = 'Enter a valid ' + dataTypeDisplay + ' for ' + fld.data('mccf_fieldtext').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		}
	}
	return returnMsg;
}
function mccf_loadNameControl(scope) {
	var regName = $('#registrantName').val();
	scope.find('.MCCFNameField').keyup(function(event){
		if(!$(this).val().trim().length) {
			$(this).next().show().next().hide();
		} else {
			$(this).next().hide().next().show();
		}
	});

	scope.find('.MCCFNameControlLink').html('Add "' + regName +'"');

	scope.find('.MCCFNameControlLink').click(function(event){
		$(this).hide().next().show();
		var linkedNameControlID = $(this).data('linkednamecontrol');
		$('#' + linkedNameControlID).val(regName);
		event.preventDefault();
	});

	scope.find('.MCCFNameClearControlLink').click(function(event){
		$(this).hide().prev().show();
		var linkedNameControlID = $(this).data('linkednamecontrol');
		$('#' + linkedNameControlID).val('');
		event.preventDefault();
	});
}
function mccf_deleteDoc(id) {
	var msg = 'Are you sure you want to delete the document?';
	if(confirm(msg)) {
		$('#cf_remDoc_' +id+'_').val(1);
		$('#mccf_div_currFile_' +id + ', #mccf_delBtn_' +id).hide();
		$('#mccf_div_remMsg_' +id).show();
	}
}