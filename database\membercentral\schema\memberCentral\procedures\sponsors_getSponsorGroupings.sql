ALTER PROC dbo.sponsors_getSponsorGroupings
@siteID int,
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	WITH SponsorGroupings AS (
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM dbo.sponsorsGrouping
		WHERE siteID = @siteID
		AND referenceType = @referenceType
		AND referenceID = @referenceID
			UNION ALL
		SELECT 0, 'Default - No Grouping', 0
	)
	SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
	FROM SponsorGroupings
	ORDER BY sponsorGroupingOrder;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
