ALTER PROC dbo.queue_s3Verify_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	select 's3Verify', qs.queueStatus, count(qi.fileID), min(qi.dateUpdated), 1
	from dbo.queue_s3Verify as qi
	inner join dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID
	group by qs.queueStatus
	having count(qi.fileID) > 0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
