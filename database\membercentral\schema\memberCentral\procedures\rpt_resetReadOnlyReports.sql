ALTER PROC dbo.rpt_resetReadOnlyReports
@itemCount INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='scheduledReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	DECLARE @tblResetReports TABLE (reportID int);
	DECLARE @nowDate datetime = GETDATE(), @20MinsAgo datetime;
	SET @20MinsAgo = DATEADD(minute, -20, @nowDate);

	-- update to not read only
	UPDATE sr WITH (UPDLOCK, READPAST)
	SET sr.isReadOnly = 1,
		sr.readOnlyUpdateDate = GETDATE()
		OUTPUT inserted.reportID
		INTO @tblResetReports
	FROM dbo.rpt_savedReports as sr
	WHERE sr.isReadOnly = 0
	AND sr.readOnlyUpdateDate < @20MinsAgo
	AND EXISTS (select itemID from dbo.rpt_scheduledReports where reportID = sr.reportID);

	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0 BEGIN
		-- were any of these reports run in the last 15 minutes? (this proc runs every 15 min)
		-- add to queue to run immediately (all scheduled runs for the updated reports)
		;WITH LatestRun AS (
			SELECT rl.reportID, rl.memberID, rl.dateRun, ROW_NUMBER() OVER(PARTITION BY rl.reportID ORDER BY rl.dateRun DESC) AS rn
			FROM @tblResetReports as tmp
			INNER JOIN platformstatsMC.dbo.rpt_runLog as rl on rl.reportID = tmp.reportID
		)
		INSERT INTO platformQueue.dbo.queue_scheduledReport (srItemID, reportID, resetByMemberID, isReset, statusID, dateAdded, dateUpdated)
		SELECT sch.itemID, sch.reportID, L.memberID, 1, @statusReady, GETDATE(), GETDATE()
		FROM LatestRun as L
		INNER JOIN dbo.rpt_scheduledReports as sch on sch.reportID = L.reportID
		WHERE L.rn = 1
		AND L.dateRun >= DATEADD(minute, -15, GETDATE())
		AND NOT EXISTS (
			SELECT itemID
			FROM platformQueue.dbo.queue_scheduledReport
			where srItemID = sch.itemID
		);

		IF @@ROWCOUNT > 0
			EXEC dbo.sched_resumeTask @name='Process Scheduled Report Queue', @engine='MCLuceeLinux';
	END

    RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
