ALTER PROC dbo.queue_addMCPayECheckNSFBatches_load
@itemGroupUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @itemAsStr varchar(60), @xmlMessage xml;

	IF OBJECT_ID('tempdb..#tblMsgs') IS NOT NULL 
		DROP TABLE #tblMsgs;
	CREATE TABLE #tblMsgs (itemAsStr varchar(60) not null);

	insert into #tblMsgs (itemAsStr)			
	select distinct itemID
	from dbo.queue_addMCPayECheckNSFBatches
	where itemGroupUID = @itemGroupUID;

	select @itemAsStr = min(itemAsStr) from #tblMsgs;
	while @itemAsStr is not null BEGIN
		SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
		EXEC dbo.queue_addMCPayECheckNSFBatches_sendMessage @xmlMessage=@xmlMessage;
		select @itemAsStr = min(itemAsStr) from #tblMsgs where itemAsStr > @itemAsStr;
	end

	IF OBJECT_ID('tempdb..#tblMsgs') IS NOT NULL 
		DROP TABLE #tblMsgs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
