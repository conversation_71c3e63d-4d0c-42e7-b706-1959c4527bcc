ALTER PROC dbo.queue_analyzeMCPayECheckBatches_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @readyStatusID int, @processingStatusID int, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @itemAsStr VARCHAR(60), @xmlMessage xml;
	EXEC dbo.queue_getQueueTypeID @queueType='analyzeMCPayECheckBatches', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- analyzeMCPayECheckBatches / processingItem autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -15, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_analyzeMCPayECheckBatches WHERE statusID = @processingStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_analyzeMCPayECheckBatches
		SET statusID = @readyStatusID, 
			dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_analyzeMCPayECheckBatches_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'analyzeMCPayECheckBatches Queue Issue';
		SET @errorSubject = 'analyzeMCPayECheckBatches queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- analyzeMCPayECheckBatches catchall
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_analyzeMCPayECheckBatches WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'analyzeMCPayECheckBatches Queue Issue';
		SET @errorSubject = 'analyzeMCPayECheckBatches queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
