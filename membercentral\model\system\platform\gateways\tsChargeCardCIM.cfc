<cfcomponent>

	<!--- testmode settings:
		0: no testing. real deal.   
		1: testing using auth test request.    
	--->
	<cfif application.MCEnvironment neq "production">
		<cfset variables.x_testmode = 1>
		<cfset variables.x_solution_id = "AAA100302"> <!--- this is auth sandbox's solution id --->
	<cfelse>
		<cfset variables.x_testmode = 0>
		<cfset variables.x_solution_id = "AAA174267"> <!--- this is membercentral's solution id --->
	</cfif>
	<cfset variables.requesttimeout="60">

	<cffunction name="gather" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="autoShowForm" type="boolean" required="no" default="0">
		<cfargument name="editMode" type="string" required="no" default="frontEndPayment" hint="frontEndPayment|frontEndManage|controlPanelPayment">
		<cfargument name="chargeInfo" type="struct" required="no" default="#setDefaultChargeInfo()#">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.jsvalidation = ''>
		<cfset local.returnStruct.inputForm = ''>
		<cfset local.returnStruct.headcode = ''>

		<cfset local.hostNameWithProtocol = getHostNameWithProtocol()>

		<cfset local.qryGetCIMInfo = getCIMInfo(merchantOrgcode=arguments.merchantOrgcode)>

		<cfquery name="local.qryProfilesOnFile" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select payProfileID, detail, depomemberdataid, customerProfileID, paymentProfileID, expiration
			from dbo.ccMemberPaymentProfiles 
			where orgcode = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and customerid = <cfqueryparam value="#arguments.customerid#" cfsqltype="CF_SQL_VARCHAR">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfscript>
		// charge info
		local.chargeInfo = setDefaultChargeInfo(useChargeInfo=arguments.chargeInfo);
		local.chargeInfoJSON = serializeJSON(local.chargeInfo);

		local.acceptApplePay = local.chargeInfo.acceptApplePay EQ 1 AND local.qryGetCIMInfo.ApplePayEnabled EQ 1;
		local.acceptGooglePay = local.chargeInfo.acceptGooglePay EQ 1 AND local.qryGetCIMInfo.GooglePayEnabled EQ 1;

		if (NOT application.objPlatform.isRequestSecure()) {
			local.acceptApplePay = 0;
			local.acceptGooglePay = 0;
		}


		if (local.acceptApplePay AND NOT existsApplePayDomain(merchantOrgcode=arguments.merchantOrgcode)) {
			local.registerDomainSuccess = registerCurrentHostNameWithApplePay(merchantOrgcode=arguments.merchantOrgcode);
			if (NOT local.registerDomainSuccess) {
				local.acceptApplePay = false;
			}
		}

		if (local.acceptApplePay) {
			local.applePayCardTypes = [ "amex", "discover", "masterCard", "visa" ];
		}
		
		if (local.acceptGooglePay) {
			local.googlePayCardTypes = [ "AMEX", "DISCOVER", "MASTERCARD", "VISA" ];
			local.qryTime = queryExecute("SELECT DATEDIFF(s, '1970-01-01 00:00:00', GETUTCDATE()) as epochTime", {}, { datasource=application.dsn.membercentral.dsn });
			local.payload = {
				'merchantOrigin': application.objPlatform.getCurrentHostname(),
				'merchantId': local.qryGetCIMInfo.googlePayMerchantID,
				'iat': local.qryTime.epochTime+(5*60)
			};
			local.jwt = new modules.jwtcfml.models.jwt();
			local.gpayAuthJWT = local.jwt.encode(local.payload, application.strPlatformAPIKeys[lCase("#arguments.merchantOrgcode#gpay")].privateKey, 'ES256');
		}
		</cfscript>

		<!--- customer profileID to use for adding new profiles --->
		<cfquery name="local.qryCustProfile" dbtype="query" maxrows="1">
			select customerProfileID, count(*) as theCount
			from [local].qryProfilesOnFile
			group by customerProfileID
			order by theCount
		</cfquery>		

		<cfset local.cidToUse = arguments.customerid>
		<cfset local.cpidToUse = local.qryCustProfile.customerProfileID>

		<cfset local.EncAddPayProfile = "<data><action>addPaymentProfile</action><mo>#xmlformat(arguments.merchantOrgcode)#</mo><cid>#arguments.customerID#</cid></data>">
		<cfset local.EncAddPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
		<cfset local.EncAddPayProfileReturn = "<data><action>addPaymentProfileReturn</action><mo>#xmlformat(arguments.merchantOrgcode)#</mo><cpid>#local.qryCustProfile.customerProfileID#</cpid><cid>#arguments.customerid#</cid><cinf>#local.chargeInfoJSON#</cinf></data>">
		<cfset local.EncAddPayProfileReturn = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfileReturn,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>

		<cfsavecontent variable="local.returnStruct.headcode">
			<cfoutput>
			<style type="text/css">
				##p_#arguments.merchantOrgcode#_DIV {font-family:"Segoe UI", sans-serif;}
				##p_#arguments.merchantOrgcode#_DIV .topInfo {font-size:18px;margin-bottom:10px;color:##0a2b49;}
				##p_#arguments.merchantOrgcode#_DIV .card-option {display:flex;align-items: center;background:##fff;border: 1px solid ##ddd;border-radius:10px;padding:16px 20px;margin-bottom:15px;box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);max-width:400px;}
				##p_#arguments.merchantOrgcode#_DIV .card-option input[type="radio"] {margin-right:10px;}
				##p_#arguments.merchantOrgcode#_DIV .card-details {flex:1;}
				##p_#arguments.merchantOrgcode#_DIV .card-number {font-weight:600;letter-spacing:1px;}
				##p_#arguments.merchantOrgcode#_DIV .card-expiry {color:##666;margin-left:8px;}
				##p_#arguments.merchantOrgcode#_DIV a.cof_edit { padding-left:20px;font-size:14px;color:##007bff;}
				##p_#arguments.merchantOrgcode#_DIV a.cof_edit:before { content: "\f09d"; font-family: FontAwesome; margin-right: 5px; }
				##p_#arguments.merchantOrgcode#_DIV .gateway-choose-mtd-container { display:flex;flex-direction:column;width:240px; }
				##p_#arguments.merchantOrgcode#_DIV .gateway-choose-mtd-btn {display:flex;align-items:center;gap:12px;background-color:##000;padding:7px;border-radius:4px;border:none;cursor:pointer;justify-content:center;}
				##p_#arguments.merchantOrgcode#_DIV .gateway-choose-mtd-btn img {height:28px;object-fit:contain;}
				##p_#arguments.merchantOrgcode#_DIV .gateway-choose-mtd-btn:hover {background-color:##3b3b3b;}
				@media (max-width: 576px) {
					##p_#arguments.merchantOrgcode#_DIV .card-details {display:flex;flex-direction:column;}
				}
			</style>
			<script language="javascript">
				$(function() {
					if ($('##divBuyNowPopup').length == 0)
						$('##CIMTable').after('<div style="text-align:center;display:none;" id="divBuyNowPopupLoading"><br/><img src="/assets/common/images/indicator.gif" width="100" height="100"><br/><br/><div class="tsAppHeading">Please wait while we load the secure payment form.</div></div><div id="divBuyNowPopup" style="display:none;"><iframe name="iframeBuyNow" id="iframeBuyNow" frameborder="0" scrolling="no"></iframe></div>');

					<cfif arguments.autoShowForm is 1 AND local.qryProfilesOnFile.recordcount is 0 AND NOT (local.acceptApplePay OR local.acceptGooglePay)>
						p_#arguments.merchantOrgcode#_addPaymentProfile();
					</cfif>
				});
				function p_#arguments.merchantOrgcode#_resizeIFrame() {
					var ifr = $('##iframeBuyNow');
					ifr.contents().find('body, div.container, body > div.bodyText').css({margin:0});
					var nh = ifr.contents().find("div##zoneMain").height();
					if (nh > 0) ifr.attr('height',nh+40 + 'px');
				}
				function p_#arguments.merchantOrgcode#_addPaymentProfile() {
					hideAlert();
					$('##CIMTable').hide();
					$('##caserefTable').hide();
					$('button.appContinueBtn').attr("disabled","disabled");
					$('##divBuyNowPopup').hide();
					$('##divBuyNowPopupLoading').show();
					
					$('##iframeBuyNow').off('load');
					$('##iframeBuyNow').attr('width','100%')
							.attr('height','700px')
							.attr('src','/?pg=buyNow&mode=direct&wizardTS=#JSStringFormat(local.EncAddPayProfile)#')
							.load(function() { p_#arguments.merchantOrgcode#_resizeIFrame(); })
							
							
					$('##divBuyNowPopupLoading').hide();
					$('##divBuyNowPopup').show();
				}
				function p_#arguments.merchantOrgcode#_addPaymentProfileReturn(obj,id) {
					<!--- post message to control app when card is added/updated --->
					if (obj && obj.a && obj.a=='save' && obj.mccardevent && ['cardadded','cardupdated'].indexOf(obj.mccardevent.toLowerCase()) != -1) {
						var message = { success:true, 
										messagetype:"TSGatewayEvent", 
										eventname:obj.mccardevent, 
										merchantorgcode:'#arguments.merchantOrgcode#',
										tspayprofileid:obj.tspayprofileid,
										profileid:'' };
						window.postMessage(message,'#JSStringFormat(local.hostNameWithProtocol)#');
					}

					$('##divBuyNowPopup').hide();
					$('##CIMTable').show();
					if (obj && obj.a && obj.a=='save') {
						$('##p_#arguments.merchantOrgcode#_DIV').attr('pofcount','0').html('Reloading... please wait.');
						$('##iframeBuyNow').attr('src','/?pg=buyNow&mode=direct&wizardTS=#JSStringFormat(local.EncAddPayProfileReturn)#');
					} else {
						$('##iframeBuyNow').attr('width','1px').attr('height','1px').attr('src','about:blank');
						$('button.appContinueBtn').removeAttr("disabled");
						$('##caserefTable').show();
					}

					if(parent.p_#arguments.merchantOrgcode#_showMsg) {
						_e = $('<input id="cardId">');
						parent.$("body").append(_e);
						_e.val(id).hide();
					}
					if (obj && obj.err) showAlert(obj.err);

					<!--- after card is added / updated, init apple/google pay --->
					<cfif local.acceptApplePay OR local.acceptGooglePay>
						setTimeout(() => {
							if ($('##applepay#arguments.merchantOrgcode#container').length && $('##applepay#arguments.merchantOrgcode#container').attr('data-init') == 0) {
								onApplePayLoaded#arguments.merchantOrgcode#();
							}
							if ($('##gpay#arguments.merchantOrgcode#container').length && $('##gpay#arguments.merchantOrgcode#container').attr('data-init') == 0) {
								onGooglePayLoaded#arguments.merchantOrgcode#();
							}
						}, 3000);
					</cfif>
				}
				
				function p_#arguments.merchantOrgcode#_editPaymentProfile(we) {
					hideAlert();
					$('##CIMTable').hide();
					$('##caserefTable').hide();
					$('button.appContinueBtn').attr("disabled","disabled");
					$('##divBuyNowPopup').hide();
					$('##divBuyNowPopupLoading').show();
					
					$('##iframeBuyNow').off('load');
					$('##iframeBuyNow').attr('width','100%')
							.attr('height','438px')
							.attr('src','/?pg=buyNow&mode=direct&wizardTS=' + escape(we))
							.load(function() { p_#arguments.merchantOrgcode#_resizeIFrame(); });
							
					$('##divBuyNowPopupLoading').hide();
					$('##divBuyNowPopup').show();
				}
				function p_#arguments.merchantOrgCode#_delay(t) {
					return new Promise(resolve => setTimeout(resolve, t));
				}
			</script>
			<cfif local.acceptApplePay>
				<script type="application/javascript">
					let p_#arguments.merchantOrgCode#_applePaySession;
					
					function p_#arguments.merchantOrgCode#_applePay() {
						<!--- Creating an Apple Pay Session --->
						var paymentRequest = {
							countryCode: 'US',
							currencyCode: 'USD',
							supportedNetworks: #serializeJson(local.applePayCardTypes)#,
							merchantCapabilities: ["supports3DS","supportsDebit","supportsCredit"],
							total: { label: '#encodeForHTML(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgName)#', amount: "#local.chargeInfo.amt#" },
						};
						p_#arguments.merchantOrgCode#_applePaySession = new ApplePaySession(3, paymentRequest);
				
						<!--- Merchant Validation --->
						p_#arguments.merchantOrgCode#_applePaySession.onvalidatemerchant = async function (event) {
							$.getJSON('/?event=proxy.ts_json&c=GATEPAY&m=TSApplePayOVM', { merchantOrgCode:'#arguments.merchantOrgCode#' })
								.done(function (validationResponse) {
									let merchantSession = validationResponse.data;
									p_#arguments.merchantOrgCode#_applePaySession.completeMerchantValidation(merchantSession);
								}).fail(function( jqxhr, textStatus, error ) {
									console.log(error);
									p_#arguments.merchantOrgCode#_applePaySession.abort();
								});
						};
				
						<!--- payment authorization --->
						p_#arguments.merchantOrgCode#_applePaySession.onpaymentauthorized = async function(event) {
							$.post('/?event=proxy.ts_json&c=GATEPAY&m=TSApplePayOPA', { merchantOrgCode: '#arguments.merchantOrgCode#', payload: JSON.stringify(event.payment)})
								.then(function (paymentResponse) {

									if (paymentResponse.success) {
										const result = { "status": ApplePaySession.STATUS_SUCCESS };
										p_#arguments.merchantOrgCode#_applePaySession.completePayment(result);
										p_#arguments.merchantOrgCode#_applePaySession = null;

										let message = { success:true, 
														messagetype:"TSGatewayEvent", 
														eventname:'applepaytokenready', 
														merchantorgcode:'#arguments.merchantOrgCode#',
														tokendata: paymentResponse.data };
										window.postMessage(message,'#JSStringFormat(local.hostNameWithProtocol)#');
									
									} else {
										p_#arguments.merchantOrgCode#_applePaySession.abort();
									} 

								}, function (error) {
									console.log(error);
									p_#arguments.merchantOrgCode#_applePaySession.abort();
								});
						};
				
						p_#arguments.merchantOrgCode#_applePaySession.oncancel = function(event) {};
				
						<!--- Begins the merchant validation process --->
						p_#arguments.merchantOrgCode#_applePaySession.begin();
					}

					function onApplePayLoaded#arguments.merchantOrgCode#() {
						return new Promise(async function(resolve,reject) {
							let maxDelay = 20, counter = 0;
							while(true) {
								counter++;
								await p_#arguments.merchantOrgCode#_delay(1000);
								if ($('##applepay#arguments.merchantOrgCode#container').length || counter == maxDelay) break;
							}

							resolve();

						}).then(function() {

							$('##applepay#arguments.merchantOrgcode#container').attr('data-init',1);
							
							<!--- Apple Pay availability --->
							if (window.ApplePaySession) {
								let merchantIdentifier#arguments.merchantOrgCode# = '#local.qryGetCIMInfo.authCIMGatewayAccountID#';
								ApplePaySession.applePayCapabilities(merchantIdentifier#arguments.merchantOrgCode#).then(function(capabilities) {
									switch (capabilities.paymentCredentialStatus) {
										case "paymentCredentialsAvailable":
											/* Display an Apple Pay button and offer Apple Pay as the primary payment option. */
											$('##applepay#arguments.merchantOrgCode#container').show();
											break;
										case "paymentCredentialStatusUnknown":
											/* Display an Apple Pay button and offer Apple Pay as a payment option. */
											$('##applepay#arguments.merchantOrgCode#container').show();
											break;
										case "paymentCredentialsUnavailable":
											/* Consider displaying an Apple Pay button. */
											$('##applepay#arguments.merchantOrgCode#container').show();
											break;
										case "applePayUnsupported":
											/* Don't show an Apple Pay button or offer Apple Pay. */
											break;
									}
								});
							}

						}).catch(function(err) {
							console.log(err);
						});
					}

					<!--- Load Apple Pay JS --->
					let applePaySDK#arguments.merchantOrgCode# = document.createElement('script');
					applePaySDK#arguments.merchantOrgCode#.onload = function () {
						onApplePayLoaded#arguments.merchantOrgCode#();
					};
					applePaySDK#arguments.merchantOrgCode#.async = true;
					applePaySDK#arguments.merchantOrgCode#.crossorigin = 'anonymous';
					applePaySDK#arguments.merchantOrgCode#.src = "https://applepay.cdn-apple.com/jsapi/1.latest/apple-pay-sdk.js";
					document.head.appendChild(applePaySDK#arguments.merchantOrgCode#);
				</script>
				<style type="text/css">
					apple-pay-button {
						--apple-pay-button-width: 240px;
						--apple-pay-button-height: 40px;
						--apple-pay-button-border-radius: 4px;
						--apple-pay-button-padding: 6px;
						--apple-pay-button-box-sizing: border-box;
					}
				</style>
			</cfif>
			<cfif local.acceptGooglePay>
				<script type="application/javascript">
					const baseRequest#arguments.merchantOrgCode# = { apiVersion: 2, apiVersionMinor: 0 };
					const tokenizationSpecification#arguments.merchantOrgCode# = {
						type: 'PAYMENT_GATEWAY',
						parameters: {
							'gateway': 'authorizenet',
							'gatewayMerchantId': '#local.qryGetCIMInfo.authCIMGatewayAccountID#'
						}
					};
					const allowedCardNetworks#arguments.merchantOrgCode# = #serializeJson(local.googlePayCardTypes)#;
					const allowedCardAuthMethods#arguments.merchantOrgCode# = ["PAN_ONLY", "CRYPTOGRAM_3DS"];
					const baseCardPaymentMethod#arguments.merchantOrgCode# = {
						type: 'CARD',
						parameters: {
							allowedAuthMethods: allowedCardAuthMethods#arguments.merchantOrgCode#,
							allowedCardNetworks: allowedCardNetworks#arguments.merchantOrgCode#,
							assuranceDetailsRequired: true,
							billingAddressRequired: true,
							billingAddressParameters: {
								format: "MIN"
							}
						}
					};
					const cardPaymentMethod#arguments.merchantOrgCode# = Object.assign({tokenizationSpecification: tokenizationSpecification#arguments.merchantOrgCode#},baseCardPaymentMethod#arguments.merchantOrgCode#);

					let paymentsClient#arguments.merchantOrgCode# = null;

					function getGoogleIsReadyToPayRequest#arguments.merchantOrgCode#() {
						return Object.assign({},baseRequest#arguments.merchantOrgCode#,{allowedPaymentMethods: [baseCardPaymentMethod#arguments.merchantOrgCode#]});
					}
					function getGooglePaymentDataRequest#arguments.merchantOrgCode#() {
						const paymentDataRequest = Object.assign({}, baseRequest#arguments.merchantOrgCode#);
						paymentDataRequest.allowedPaymentMethods = [cardPaymentMethod#arguments.merchantOrgCode#];
						paymentDataRequest.transactionInfo = getGoogleTransactionInfo#arguments.merchantOrgCode#();
						paymentDataRequest.merchantInfo = {
							merchantId: '#local.qryGetCIMInfo.googlePayMerchantID#',
							merchantOrigin: '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#',
							merchantName: '#encodeForHTML(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgName)#',
							authJwt: '#local.gpayAuthJWT#'
						};
						return paymentDataRequest;
					}
					function getGooglePaymentsClient#arguments.merchantOrgCode#() {
						if ( paymentsClient#arguments.merchantOrgCode# === null ) {
							paymentsClient#arguments.merchantOrgCode# = new google.payments.api.PaymentsClient({environment: <cfif application.MCEnvironment NEQ 'production'>'TEST'<cfelse>'PRODUCTION'</cfif>});
						}
						return paymentsClient#arguments.merchantOrgCode#;
					}
					function onGooglePayLoaded#arguments.merchantOrgCode#() {
						return new Promise(async function(resolve,reject) {
							let maxDelay = 20, counter = 0;
							while(true) {
								counter++;
								await p_#arguments.merchantOrgCode#_delay(1000);
								if ($('##gpay#arguments.merchantOrgCode#container').length || counter == maxDelay) break;
							}

							resolve();

						}).then(function() {

							$('##gpay#arguments.merchantOrgcode#container').attr('data-init',1);

							const paymentsClient = getGooglePaymentsClient#arguments.merchantOrgCode#();
							paymentsClient.isReadyToPay(getGoogleIsReadyToPayRequest#arguments.merchantOrgCode#())
								.then(function(response) {
									if (response.result) {
										addGooglePayButton#arguments.merchantOrgCode#();
										prefetchGooglePaymentData#arguments.merchantOrgCode#();
									}
								})
								.catch(function(err) {
									console.log(err);
								});
							
						}).catch(function(err) {
							console.log(err);
						});
					}
					function addGooglePayButton#arguments.merchantOrgCode#() {
						const paymentsClient = getGooglePaymentsClient#arguments.merchantOrgCode#();
						const button = 
							paymentsClient.createButton({
								buttonColor: 'black',
								buttonType: 'pay',
								buttonRadius: 4,
								buttonSizeMode: 'fill',
								onClick: onGooglePaymentButtonClicked#arguments.merchantOrgCode#,
								allowedPaymentMethods: [baseCardPaymentMethod#arguments.merchantOrgCode#]
							});
						document.getElementById('gpay#arguments.merchantOrgCode#container').appendChild(button);
					}
					function getGoogleTransactionInfo#arguments.merchantOrgCode#() {
						return {
							countryCode: 'US',
							currencyCode: 'USD',
							totalPriceStatus: 'FINAL',
							totalPrice: "#local.chargeInfo.amt#"
						};
					}
					function prefetchGooglePaymentData#arguments.merchantOrgCode#() {
						const paymentDataRequest = getGooglePaymentDataRequest#arguments.merchantOrgCode#();
						paymentDataRequest.transactionInfo = {
							totalPriceStatus: 'NOT_CURRENTLY_KNOWN',
							currencyCode: 'USD'
						};
						const paymentsClient = getGooglePaymentsClient#arguments.merchantOrgCode#();
						paymentsClient.prefetchPaymentData(paymentDataRequest);
					}
					function onGooglePaymentButtonClicked#arguments.merchantOrgCode#() {
						const paymentDataRequest = getGooglePaymentDataRequest#arguments.merchantOrgCode#();
						paymentDataRequest.transactionInfo = getGoogleTransactionInfo#arguments.merchantOrgCode#();
						const paymentsClient = getGooglePaymentsClient#arguments.merchantOrgCode#();
						paymentsClient.loadPaymentData(paymentDataRequest)
							.then(function(paymentData) {
								processPayment#arguments.merchantOrgCode#(paymentData);
							})
							.catch(function(err) {
								console.error(err);
							});
					}
					function processPayment#arguments.merchantOrgCode#(paymentData) {						
						let message = { success:true, 
										messagetype:"TSGatewayEvent", 
										eventname:'gpaytokenready', 
										merchantorgcode:'#arguments.merchantOrgCode#',
										tokendata: { tstokensource:'googlePay', moc:'#arguments.merchantOrgCode#', paymentData:paymentData } };
						window.postMessage(message,'#JSStringFormat(local.hostNameWithProtocol)#');
					}

					<!--- Load Google Pay JS --->
					let googlePaySDK#arguments.merchantOrgCode# = document.createElement('script');
					googlePaySDK#arguments.merchantOrgCode#.onload = function () {
						onGooglePayLoaded#arguments.merchantOrgCode#();
					};
					googlePaySDK#arguments.merchantOrgCode#.async = true;
					googlePaySDK#arguments.merchantOrgCode#.src = "https://pay.google.com/gp/p/js/pay.js";
					document.head.appendChild(googlePaySDK#arguments.merchantOrgCode#);
				</script>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.returnStruct.jsvalidation">
			<cfoutput>
			var hasPmtInfo#arguments.merchantOrgcode# = $('##p_#arguments.merchantOrgcode#_DIV').attr("pofcount") != 0;
			<cfif local.acceptApplePay OR local.acceptGooglePay>
				if ($('##p_#arguments.merchantOrgcode#_tokenData').length && $('##p_#arguments.merchantOrgcode#_tokenData').val().length)
					hasPmtInfo#arguments.merchantOrgcode# = true;
			</cfif>
			if (!hasPmtInfo#arguments.merchantOrgcode#)
				arrReq.push('Enter your payment information to continue.');
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.returnStruct.inputForm">
			<cfoutput>
			<div id="p_#arguments.merchantOrgcode#_DIV" pofcount="#local.qryProfilesOnFile.recordcount#">
				<cfif local.qryProfilesOnFile.recordcount gt 0>
					<cfif local.qryProfilesOnFile.recordcount is 1>
						<div class="topInfo">Use the following pay method:</div>
					<cfelse>
						<div class="topInfo">Select from the following pay methods:</div>
					</cfif>
					<cfloop query="local.qryProfilesOnFile">
						<cfset local.EncEditPayProfile = "<data><action>editPaymentProfile</action><mo>#xmlformat(arguments.merchantOrgcode)#</mo><cpid>#local.qryProfilesOnFile.customerProfileID#</cpid><cppid>#local.qryProfilesOnFile.paymentProfileID#</cppid></data>">
						<cfset local.EncEditPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncEditPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
						<div class="card-option">
							<input type="radio" name="p_#arguments.merchantOrgcode#_mppid" value="#local.qryProfilesOnFile.payProfileID#" <cfif local.qryProfilesOnFile.currentrow is 1>checked</cfif>>
							<div class="card-details">
								<span class="card-number">**** #right(local.qryProfilesOnFile.detail,4)#</span>
								<span class="card-expiry">Exp #DateFormat(local.qryProfilesOnFile.expiration,"mm/yy")#</span>
							</div>
							<a href="javascript:p_#arguments.merchantOrgcode#_editPaymentProfile('#JSStringFormat(local.EncEditPayProfile)#')" class="cof_edit" id="#local.qryProfilesOnFile.paymentProfileID#">Edit Card</a>
						</div>
					</cfloop>
					<br/>
				</cfif>
				<cfset local.enableCC = local.qryProfilesOnFile.recordcount is 0>
				<cfif local.enableCC OR local.acceptApplePay OR local.acceptGooglePay>
					<div class="gateway-choose-mtd-container">
						<cfif local.enableCC>
							<button type="button" class="gateway-choose-mtd-btn" onclick="p_#arguments.merchantOrgcode#_addPaymentProfile();" title="Credit Card" style="margin-bottom:10px;">
								<cfloop list="American Express,Discover,MasterCard,VISA" index="local.thisCardType">
									<img src="/assets/common/images/payment/#lCase(local.thisCardType)#-logo.png" alt="#local.thisCardType#">
								</cfloop>
							</button>
						</cfif>
						<cfif local.acceptApplePay>
							<div id="applepay#arguments.merchantOrgcode#container" style="margin-bottom:10px;display:none;" data-init="0">
								<apple-pay-button buttonstyle="black" type="pay" locale="en-US" onclick="p_#arguments.merchantOrgcode#_applePay();"></apple-pay-button>
							</div>
						</cfif>
						<cfif local.acceptGooglePay>
							<div id="gpay#arguments.merchantOrgcode#container" style="width:240px;height:40px;" data-init="0"></div>
						</cfif>
					</div>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createCustomerProfile" access="private" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.customerProfileId=''>
		<cfset local.returnStruct.head=''>

		<cfset local.qryGetCIMInfo = getCIMInfo(merchantOrgcode=arguments.merchantOrgcode)>

		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"createCustomerProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#local.qryGetCIMInfo.authUsername#", 
							"transactionKey": "#local.qryGetCIMInfo.authTransKey#" 
						},
						"profile": {
							"merchantCustomerId": "#left(encodeForHTML(arguments.customerID),20)#"
						}
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.arrKeysToMask = [
				{ key='createCustomerProfileRequest.merchantAuthentication.transactionKey', replacement='REDACTED' }
			]>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>

			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<!--- need to catch if profile could not be created because it already exists --->
				<!--- java needed because cf doesnt support look ahead/behinds --->
				<cfset local.objPattern = CreateObject("java","java.util.regex.Pattern").Compile("(?<=A duplicate record with ID )([0-9]+)(?= already exists\.)")>
				<cfset local.arrIDs = []>
				<cfloop array="#local.strAuthorize.arrErrors#" index="local.thisErr">
					<cfif local.thisErr.code EQ 'E00039'>
						<cfset local.objMatcher = local.objPattern.Matcher(local.thisErr.text)>
						<cfloop condition="local.objMatcher.Find()">
							<cfset ArrayAppend(local.arrIDs,local.objMatcher.Group())>
						</cfloop>
					</cfif>
				</cfloop>
				<cfif arrayLen(local.arrIDs) is 1>
					<cfset local.returnStruct.customerProfileId = local.arrIDs[1]>					
				<cfelse>
					<cfthrow message="Error creating customer profile.">
				</cfif>
			<cfelse>
				<cfset local.returnStruct.customerProfileId = local.strAuthorize.strAPIResponse.customerProfileId>
			</cfif>
			
			<!--- error if we dont have a customerProfileID now. --->
			<cfif NOT len(local.returnStruct.customerProfileid)>
				<cfthrow message="Error creating customer profile.">
			</cfif>
			
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="merchantorgcode" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>

		<!--- create customer profile id --->
		<cfset local.strCustProfile = createCustomerProfile(merchantorgcode=arguments.merchantorgcode, customerID=arguments.customerID)>
		<cfset local.customerProfileid = local.strCustProfile.customerProfileID>
		<cfif len(local.strCustProfile.head)>
			<cfset local.returnStruct.head = local.strCustProfile.head>
			<cfreturn local.returnStruct>
		</cfif>
		
		<!--- prefill data --->
		<cfset local.strPrefill = { 
				fld_1_ = '',	
				fld_2_ = '',	
				fld_4_ = '',	
				fld_6_ = '',	
				fld_11_ = '',	
				fld_12_ = '',	
				fld_13_ = '',	
				fld_14_ = '',	
				fld_15_ = ''
			}>
		
		<cfreturn showPaymentProfileForm(merchantorgcode=arguments.merchantorgcode, EncSaveCardURL=arguments.EncSaveCardURL, strPrefill=local.strPrefill)>
	</cffunction>

	<cffunction name="editPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerPaymentProfileId" type="string" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>

		<cfset local.qryGetCIMInfo = getCIMInfo(merchantOrgcode=arguments.merchantOrgcode)>

		<!--- get payment profile info from CIM --->
		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"getCustomerPaymentProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#local.qryGetCIMInfo.authUsername#", 
							"transactionKey": "#local.qryGetCIMInfo.authTransKey#" 
						},
						"customerProfileId": "#arguments.customerProfileid#",
						"customerPaymentProfileId": "#arguments.customerPaymentProfileId#",
						"includeIssuerInfo": "false"
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.arrKeysToMask = [
				{ key='getCustomerPaymentProfileRequest.merchantAuthentication.transactionKey', replacement='REDACTED' }
			]>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>

			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<cfthrow message="Unable to locate credit card for editing.">
			<cfelse>
				<cfset local.paymentProfile = local.strAuthorize.strAPIResponse.paymentProfile>
				<cfset local.strPrefill = { 
					fld_1_ = local.paymentProfile.billTo.keyExists('firstName') ? local.paymentProfile.billTo.firstName : "",
					fld_2_ = local.paymentProfile.billTo.keyExists('lastName') ? local.paymentProfile.billTo.lastName : "",
					fld_4_ = local.paymentProfile.payment.creditCard.cardNumber,
					fld_6_ = local.paymentProfile.payment.creditCard.expirationDate,
					fld_11_ = local.paymentProfile.billTo.keyExists('zip') ? local.paymentProfile.billTo.zip : "",
					fld_12_ = local.paymentProfile.billTo.keyExists('address') ? local.paymentProfile.billTo.address : "",
					fld_13_ = local.paymentProfile.billTo.keyExists('city') ? local.paymentProfile.billTo.city : "",
					fld_14_ = local.paymentProfile.billTo.keyExists('state') ? local.paymentProfile.billTo.state : "",
					fld_15_ = local.paymentProfile.billTo.keyExists('country') ? local.paymentProfile.billTo.country : ""
				}>
			</cfif>
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.merchantorgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantorgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>

		<cfreturn showPaymentProfileForm(merchantorgcode=arguments.merchantorgcode, EncSaveCardURL=arguments.EncSaveCardURL, strPrefill=local.strPrefill)>
	</cffunction>

	<cffunction name="showPaymentProfileForm" access="private" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">
		<cfargument name="strPrefill" type="struct" required="yes">

		<cfset local.returnStruct = { html='', head='' }>
		
		<cfset local.defaultTemplateSettings = application.objSiteResource.getSiteResourceSettingsStruct(siteResourceID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultTemplateSiteResourceID)>
		<!--- validation js --->
		<cfsavecontent variable="local.returnStruct.head">
			<cfoutput>
			<style type="text/css">
				<cfif (local.defaultTemplateSettings.supportsBootstrap eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
					.purchaseFormContent .wellContainer { border: 1px solid ##c2c2c2; border-radius: 5px; padding-bottom: 15px; width: 350px;}
					.purchaseFormContent .crdInput { width: 95%; height: 42px !important; margin: 5px 0px 5px 0px; padding-left: 10px; border-color: ##bebebe;}
					.purchaseFormContent .crdInputSplit { width: 95%; height: 42px !important; margin: 5px 0px 5px 0px; padding-left: 10px; border-color: ##bebebe;}
					.purchaseFormContent .wellHeading { font-size: 20px;}
					.purchaseFormContent ##everr { width: 95%;  margin-top: 10px; margin-bottom: 10px; }
					.purchaseFormContent input::placeholder {font-size: 13px;}
					.purchaseFormContent .nameField::placeholder {font-size: 12px;}
					.purchaseFormContent ##fld_6_::placeholder {font-size: 11px;}
					.purchaseFormContent .disclaimer { padding-right:15px; margin-top:10px; font-size:10px; color:##707070;line-height:1.5em;}
					.purchaseFormContent .disclaimer img { width:20px;padding-right:3px; }
					.purchaseFormContent .btnContinue { margin-top: 10px;}
					.purchaseFormContent .span12 .span6:nth-child(2) { margin-left: 0px}
					@media only screen and (max-width: 750px) and (min-width: 380px) {
						.purchaseFormContent .crdInput { width: 96%;}
						.purchaseFormContent .crdInputSplit { width: 92%;}
						.purchaseFormContent .wellContainer .span6 {width: 50%;float: left;}
						.purchaseFormContent .span12 {margin-left:2%;} 
					}
					@media only screen and (max-width: 430px) {
						.purchaseFormContent .wellContainer {width:100%}
						.purchaseFormContent ##fld_6_::placeholder {font-size: 11px;}
						.purchaseFormContent .nameFields::placeholder {font-size: 11px;}
					}
					@media only screen and (max-width: 379px) {
						.purchaseFormContent .span12 {margin-left:2%;}
						.purchaseFormContent ##fld_6_::placeholder {font-size: 12px;}
						.purchaseFormContent .nameFields::placeholder {font-size: 13px;}
					}
				<cfelse>
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
					input[type=text], input[type=password], select { margin-top: 4px; margin-bottom: 0px; }
					input[type=text], input[type=password] { border: 1px solid ##7F9DB9; padding: 2px; }
					select { border: 1px solid ##7F9DB9; }
					input[type=text][disabled=disabled] { background-color: ##EBEBE4; }
					input.Disabled { background-color: ##EBEBE4; }
					.FieldGroupSeparator { background-color: ##e0e0e0; font-weight: bold; padding: 5px 10px; margin-bottom: 10px; }
					.FieldGroupSeparatorBillingInfo { margin-top: 15px; }
					.PaymentPadlock { position:absolute; top: 15px; right: 10px; width: 13px; height: 15px; background-image: url('/assets/common/images/padlock.png'); }
					.Comment { font-size: 11px; }
					.EditButtons { background-color: ##e0e0e0; padding: 5px; text-align: center; margin-top: 10px; }
					.disclaimer { padding-left:5px; margin-top:20px; }
					.disclaimer img { padding-right:8px; }
					.FieldGroupSeparatorPaymentInfo { display: block; }
					.PaymentItemEditData { padding: 4px; }
					.CreditCardInfo .DataLabelEdit,.AddressEdit .DataLabelEdit { display: inline-block; text-align: right; width: 144px; }
					@media screen and (max-width:480px){
						.CreditCardInfo .DataLabelEdit,.AddressEdit .DataLabelEdit { display: inline-block; text-align: left; width: 100%; }
					}
					.DataLabelEdit, .DataLabelEdit2 { font-weight: bold; }
					.DataValEditCardNum input { width: 150px; }
					.DataValEditExpDate input { width: 70px; }
					.DataValEditFirstName input { width: 200px; }
					.DataValEditLastName input { width: 200px; }
					.DataValEditStreet input { width: 200px; }
					.DataValEditCity input { width: 200px; }
					.DataValEditState input { width: 55px; }
					.DataValEditZip input { width: 75px; }
					.DataValEditCountry input { width: 200px; }
				</cfif>
			</style>
			<script language="javascript">
				function hideAlert() { 
					$('##everr').html('').hide();
					if (parent.p_#arguments.merchantOrgcode#_resizeIFrame) parent.p_#arguments.merchantOrgcode#_resizeIFrame();
				};
				function showAlert(msg) { 
					$('##everr').html(msg).show(); 
					if (parent.p_#arguments.merchantOrgcode#_resizeIFrame) parent.p_#arguments.merchantOrgcode#_resizeIFrame();
				};
				function cancelIt() { 
					var obj = new Object();
						obj.a = 'cancel';
					if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(obj);
				}
				function _FB_hasValue(obj, obj_type) {
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){
						tmp = obj.value;
						tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
						if (tmp.length == 0) return false;
						else return true;
					} else if (obj_type == 'SELECT'){
						for (var i=0; i < obj.length; i++) {
							if (obj.options[i].selected){
								tmp = obj.options[i].value;
								tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
								if (tmp.length > 0) return true;
							}
						}
						return false;	
					} else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){
						if (obj.checked) return true;
						else return false;	
					} else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){
						if (obj.length == undefined && obj.checked) return true;
						else{
							for (var i=0; i < obj.length; i++){
								if (obj[i].checked) return true;
							}
						}
						return false;
					}else{
						return true;
					}
				}
				function valExpDate() {
					var bad = false;
					<cfif len(arguments.strPrefill.fld_4_)>if ($('##fld_6_').val() != 'XXXX') {</cfif>
						var ed = $('##fld_6_').val().split(/[\/\-\s]+/);
						var pattern = /^\d+$/;
						if (!pattern.test(ed[0]) || !pattern.test(ed[1])) bad = true;
						if (ed[0] < 1 || ed[0] > 12 || ed[1] < 10 || ed[1] > 99) bad = true;
						if (!bad) $('##fld_6_').val(ed[0] + '/' + ed[1]);
					<cfif len(arguments.strPrefill.fld_4_)>}</cfif>
					return bad;
				}
				function valCardForm() {
					$('form##frmAuthorizeCIM button[type="submit"]').prop('disabled',true);
					hideAlert();
					var arrReq = new Array();
					var thisForm = document.forms["frmAuthorizeCIM"];
					
					<cfif len(arguments.strPrefill.fld_4_)>if (thisForm['fld_4_'].value.indexOf("XXXX") < 0) {</cfif>
						if (!_CF_checkcreditcard(thisForm['fld_4_'].value, true)) arrReq[arrReq.length] = 'Card Number must be valid.';
					<cfif len(arguments.strPrefill.fld_4_)>}</cfif>
					if (!_FB_hasValue(thisForm['fld_6_'], 'TEXT')) arrReq[arrReq.length] = 'Expiration Date must be valid.';
					else if (valExpDate()) arrReq[arrReq.length] = 'Expiration Date must be valid.';

					if (arrReq.length == 0) {
						if (!_FB_hasValue(thisForm['fld_1_'], 'TEXT')) arrReq[arrReq.length] = 'First Name on Card cannot be blank.';
						if (!_FB_hasValue(thisForm['fld_2_'], 'TEXT')) arrReq[arrReq.length] = 'Last Name on Card cannot be blank.';
					}
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						$('form##frmAuthorizeCIM button[type="submit"]').prop('disabled',false);
						return false;
					}
					return true;
				}
				function onReceivePaymentAppMessage(event) {
					if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype.toLowerCase() == 'tsfrontendpaymentevent') {
						$('form##frmAuthorizeCIM button[type="submit"]').text(event.data.paymentbuttonname);
					} else {
						return false;
					}
				}

				$(function() {
					if (window.addEventListener) {
						window.addEventListener("message", onReceivePaymentAppMessage, false);
					} else if (window.attachEvent) {
						window.attachEvent("message", onReceivePaymentAppMessage);
					}

					<!--- let parent know that payment form is ready and if there is anything to broadcast --->
					parent.postMessage({ success:true, messagetype:'TSPaymentFormLoadEvent', merchantorgcode:'#arguments.merchantorgcode#' },'#JSStringFormat(getHostNameWithProtocol())#');
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.returnStruct.html">
			<cfoutput>
			<cfform name="frmAuthorizeCIM" id="frmAuthorizeCIM" method="post" action="/?pg=buyNow&mode=direct&wizardTS=#arguments.EncSaveCardURL#" onsubmit="return valCardForm();">
				<cfif (local.defaultTemplateSettings.supportsBootstrap eq "true") or (isdefined("session.enableMobile") and session.enableMobile) >
					<div class="container-fluid purchaseFormContent">
						<div class="row-fluid">
							<div class="wellContainer span5">
								<div class="well well-sm">
									<span class="wellHeading">Credit Card</span>
								</div>
								<div class="span12">
									<cfinput autocomplete="off" class="tsAppBodyText crdInput" onkeypress="hideAlert();" id="fld_4_" name="fld_4_" maxLength="16" type="text" value="#arguments.strPrefill.fld_4_#" placeholder="Card Number *">
								</div>
								<div class="span12 clearfix">
									<div class="span6">
										<cfinput autocomplete="off" class="tsAppBodyText crdInputSplit" onkeypress="hideAlert();" id="fld_6_" name="fld_6_" maxLength="5" type="text" value="#arguments.strPrefill.fld_6_#" placeholder="Exp Date (MM/YY) *">
									</div>
								</div>
								<div class="span12">
									<div class="span6">
										<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInputSplit nameFields" id="fld_1_" name="fld_1_" maxLength="50" type="text" value="#arguments.strPrefill.fld_1_#" placeholder="First Name on Card *">
									</div>
									<div class="span6">
										<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInputSplit nameFields" id="fld_2_" name="fld_2_" maxLength="50" type="text" value="#arguments.strPrefill.fld_2_#" placeholder="Last Name on Card *">
									</div>
								</div>
								<div class="span12">
									<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInput" id="fld_12_" name="fld_12_" maxLength="60" type="text" value="#arguments.strPrefill.fld_12_#" placeholder="Address">
								</div>
								<div class="span12">
									<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInput" id="fld_13_" name="fld_13_" maxLength="40" type="text" value="#arguments.strPrefill.fld_13_#" placeholder="City">
								</div>
								<div class="span12 clearfix">
									<div class="span6">
										<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInputSplit" id="fld_14_" name="fld_14_" maxLength="40" type="text" value="#arguments.strPrefill.fld_14_#" placeholder="State">
									</div>
									<div class="span6">
										<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInputSplit" id="fld_11_" name="fld_11_" maxLength="20" type="text" value="#arguments.strPrefill.fld_11_#" placeholder="Postal Code">
									</div>
								</div>
								<div id="everr" class="span12 alert alert-error" style="display:none;"></div>
								<div class="span12 btnContinue">
									<button class="btn btn-success" type="submit">Save</button>
									<button class="btn btn-danger" onclick="cancelIt()" type="button">Cancel</button>
								</div>
								<div class="span12 disclaimer">
									<img src="/assets/common/images/padlock.png" width="32" height="32" align="left"> Your payment information is safe & secure and will be processed in accordance with PCI credit card security standards.
								</div>
							</div>
						</div>
					</div>
				<cfelse>
					<div class="tsAppBodyText">
						<div class=PaymentItemEditData>
							<div class="FieldGroupSeparator FieldGroupSeparatorPaymentInfo">Credit Card Information</div>
							<div class=CreditCardInfo>
								<div><span class="DataLabelEdit">Card Number:&nbsp;</span> <span class="DataValEditCardNum"><cfinput autocomplete="off" class="tsAppBodyText" onkeypress="hideAlert();" id="fld_4_" name="fld_4_" maxLength="16" type="text" value="#arguments.strPrefill.fld_4_#"> * </span></div>
								<div><span class="DataLabelEdit">Expiration Date:&nbsp;</span> <span class="DataValEditExpDate"><cfinput autocomplete="off" class="tsAppBodyText" onkeypress="hideAlert();" id="fld_6_" name="fld_6_" maxLength="5" type="text" value="#arguments.strPrefill.fld_6_#"> * <span class="Comment">(mm/yy)</span></span></div>
							</div>
							<div class="FieldGroupSeparator FieldGroupSeparatorBillingInfo">Billing Information</div>
							<div class="AddressEdit">
								<div><span class="DataLabelEdit">First Name on Card:&nbsp;</span> <span class="DataValEditFirstName"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_1_" name="fld_1_" maxLength="50" type="text" value="#arguments.strPrefill.fld_1_#"></span> * </div>
								<div><span class="DataLabelEdit">Last Name on Card:&nbsp;</span> <span class="DataValEditLastName"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_2_" name="fld_2_" maxLength="50" type="text" value="#arguments.strPrefill.fld_2_#"></span> * </div>
								<div><span class="DataLabelEdit">Address:&nbsp;</span> <span class="DataValEditStreet"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_12_" name="fld_12_" maxLength="60" type="text" value="#arguments.strPrefill.fld_12_#"></span> </div>
								<div><span class="DataLabelEdit">City:&nbsp;</span> <span class="DataValEditCity"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_13_" name="fld_13_" maxLength="40" type="text" value="#arguments.strPrefill.fld_13_#"></span> </div>
								<div><span class="DataLabelEdit">State:&nbsp;</span> <span class="DataValEditState"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_14_" name="fld_14_" maxLength="40" type="text" value="#arguments.strPrefill.fld_14_#"></span> <span class="DataLabelEdit2">&nbsp; Postal Code:&nbsp;</span> <span class="DataValEditZip"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_11_" name="fld_11_" maxLength="20" type="text" value="#arguments.strPrefill.fld_11_#"></span> </div>
							</div>
						</div>
						<div id="everr" class="alert" style="display:none;margin:6px 0;"></div>
						<div class=EditButtons><button class="tsAppBodyButton" type="submit">Save</button> <button class="tsAppBodyButton" onclick="cancelIt()" type="button">Cancel</button></div>
						<div class="disclaimer"><img src="/assets/common/images/padlock.png" width="32" height="32" align="left"> Your payment information is safe & secure and will be processed in accordance with PCI credit card security standards.</div>
					</div>
				</cfif>
			</cfform>
			</cfoutput>
		</cfsavecontent>		
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="tokenArgs" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>

		<!--- create customer profile id if it is blank/invalid (if we just added 1st card on file, for example) --->
		<cfif NOT isValidCustomerProfile(merchantorgcode=arguments.merchantorgcode, customerProfileID=arguments.customerProfileID)>
			<cfset local.strCustProfile = createCustomerProfile(merchantorgcode=arguments.merchantorgcode, customerID=arguments.customerID)>
			<cfset arguments.customerProfileid = local.strCustProfile.customerProfileID>
			<cfif len(local.strCustProfile.head)>
				<cfset local.returnStruct.head = local.strCustProfile.head>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>

		<!--- Ensure card number is valid formed --->
		<cfset arguments.tokenArgs.fld_4_ = rereplace(arguments.tokenArgs.fld_4_,"[^0-9]","","ALL")>
		<cfif NOT len(arguments.tokenArgs.fld_4_) or NOT isValid("creditcard",arguments.tokenArgs.fld_4_)>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'There was an error saving the credit card. Invalid Card Number.';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfif>
		
		<!--- Ensure expiration can be made into proper format --->
		<cftry>
			<cfset local.expirationDate = "20" & GetToken(arguments.tokenArgs.fld_6_,2,'/') & "-" & numberformat(GetToken(arguments.tokenArgs.fld_6_,1,'/'),"09")>
			<cfif len(local.expirationDate) is not 7>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'There was an error saving the credit card. Invalid Expiration Date.';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<cfset local.qryGetCIMInfo = getCIMInfo(merchantOrgcode=arguments.merchantOrgcode)>

		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"createCustomerPaymentProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#local.qryGetCIMInfo.authUsername#", 
							"transactionKey": "#local.qryGetCIMInfo.authTransKey#" 
						},
						"customerProfileId": "#arguments.customerProfileID#",
						"paymentProfile": {
							"billTo": {
								"firstName": "#left(encodeForHTML(arguments.tokenArgs.fld_1_),50)#",
								"lastName": "#left(encodeForHTML(arguments.tokenArgs.fld_2_),50)#"
								<cfif StructKeyExists(arguments.tokenArgs,"fld_12_")>, "address": "#left(encodeForHTML(arguments.tokenArgs.fld_12_),60)#"</cfif>
								<cfif StructKeyExists(arguments.tokenArgs,"fld_13_")>, "city": "#left(encodeForHTML(arguments.tokenArgs.fld_13_),40)#"</cfif>
								<cfif StructKeyExists(arguments.tokenArgs,"fld_14_")>, "state": "#left(encodeForHTML(arguments.tokenArgs.fld_14_),40)#"</cfif>
								<cfif StructKeyExists(arguments.tokenArgs,"fld_11_")>, "zip": "#left(encodeForHTML(arguments.tokenArgs.fld_11_),20)#"</cfif>
							},
							"payment": {
								"creditCard": {
									"cardNumber": "#arguments.tokenArgs.fld_4_#",
									"expirationDate": "#local.expirationDate#"
								}
							},
							"defaultPaymentProfile": false
						},
						"validationMode": "none"
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.arrKeysToMask = [
				{ key='createCustomerPaymentProfileRequest.merchantAuthentication.transactionKey', replacement='REDACTED' },
				{ key='createCustomerPaymentProfileRequest.paymentProfile.payment.creditCard.cardNumber', replacement='XXXX#right(arguments.tokenArgs.fld_4_,4)#' }
			]>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>

			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<!--- need to catch if profile could not be created because it already exists --->
				<cfif findNoCase("E00039",local.strAuthorize.rawAPIResponse)>
					<cfthrow message="We couldn't save that credit card because it looks like it already exists on the account.">
				<cfelse>
					<cfthrow message="There was an error saving the credit card.">
				</cfif>
			<cfelse>
				<cfset local.customerPaymentProfileId = local.strAuthorize.strAPIResponse.customerPaymentProfileId>
				
				<cfset local.expMonth = numberformat(GetToken(local.expirationDate,2,'-'),"09")>
				<cfset local.expYear = GetToken(local.expirationDate,1,'-')>
				<cfset local.expDate = DateAdd("d",-1,DateAdd("m",1,"#local.expMonth#/1/#local.expYear#"))>
				
				<cfquery name="local.qryInsertCC" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET NOCOUNT ON;

					INSERT INTO dbo.ccMemberPaymentProfiles (customerid, depomemberdataid, orgcode, detail, expiration, customerProfileID, paymentProfileID, 
						dateAdded, declined, cardType, checkedForZIP, hasZIP)
					VALUES (
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerid#">,
						<cfif getToken(arguments.customerid,1,'_') eq "olddid">
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#getToken(arguments.customerid,2,'_')#">,
						<cfelse>
							null,
						</cfif>
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.merchantOrgcode#">,
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="XXXX#right(arguments.tokenArgs.fld_4_,4)#">,
						<cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.expDate#">,
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerProfileid#">,
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(local.customerPaymentProfileId,50)#">,
						GETDATE(),
						0,
						'',
						1, 
						<cfif StructKeyExists(arguments.tokenArgs,"fld_11_") and len(arguments.tokenArgs.fld_11_)>
							1
						<cfelse>
							0
						</cfif>
					);

					SELECT SCOPE_IDENTITY() as payProfileID;
				</cfquery>

				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objRet = new Object();
								objRet.a = 'save';
								objRet.mccardevent = 'cardAdded';
								objRet.tspayprofileid = #val(local.qryInsertCC.payProfileID)#;
							if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objRet);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
			</cfif>
			
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updatePaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerPaymentProfileId" type="string" required="yes">
		<cfargument name="tokenArgs" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>

		<!--- Ensure card number is valid formed --->
		<cfif NOT FindNoCase("XXXX",arguments.tokenArgs.fld_4_)>
			<cfset arguments.tokenArgs.fld_4_ = rereplace(arguments.tokenArgs.fld_4_,"[^0-9]","","ALL")>
			<cfif NOT len(arguments.tokenArgs.fld_4_) or NOT isValid("creditcard",arguments.tokenArgs.fld_4_)>
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'There was an error saving the credit card.';
							if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>
		
		<!--- Ensure expiration can be made into proper format --->
		<cfif NOT FindNoCase("XXXX",arguments.tokenArgs.fld_6_)>
			<cftry>
				<cfset local.expirationDate = "20" & GetToken(arguments.tokenArgs.fld_6_,2,'/') & "-" & numberformat(GetToken(arguments.tokenArgs.fld_6_,1,'/'),"09")>
				<cfif len(local.expirationDate) is not 7>
					<cfthrow>
				</cfif>
			<cfcatch type="Any">
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'There was an error saving the credit card.';
							if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn local.returnStruct>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfset local.expirationDate = arguments.tokenArgs.fld_6_>
		</cfif>
		
		<cfset local.qryGetCIMInfo = getCIMInfo(merchantOrgcode=arguments.merchantOrgcode)>

		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"updateCustomerPaymentProfileRequest": {
						"merchantAuthentication": {
							"name": "#local.qryGetCIMInfo.authUsername#",
							"transactionKey": "#local.qryGetCIMInfo.authTransKey#"
						},
						"customerProfileId": "#arguments.customerProfileid#",
						"paymentProfile": {
							"billTo": {
								"firstName": "#left(encodeForHTML(arguments.tokenArgs.fld_1_),50)#",
								"lastName": "#left(encodeForHTML(arguments.tokenArgs.fld_2_),50)#"
								<cfif StructKeyExists(arguments.tokenArgs,"fld_12_")>, "address": "#left(encodeForHTML(arguments.tokenArgs.fld_12_),60)#"</cfif>
								<cfif StructKeyExists(arguments.tokenArgs,"fld_13_")>, "city": "#left(encodeForHTML(arguments.tokenArgs.fld_13_),40)#"</cfif>
								<cfif StructKeyExists(arguments.tokenArgs,"fld_14_")>, "state": "#left(encodeForHTML(arguments.tokenArgs.fld_14_),40)#"</cfif>
								<cfif StructKeyExists(arguments.tokenArgs,"fld_11_")>, "zip": "#left(encodeForHTML(arguments.tokenArgs.fld_11_),20)#"</cfif>
							},
							"payment": {
								"creditCard": {
									"cardNumber": "#arguments.tokenArgs.fld_4_#",
									"expirationDate": "#local.expirationDate#"
								}
							},
							"defaultPaymentProfile": false,
							"customerPaymentProfileId": "#arguments.customerPaymentProfileid#"
						},
						"validationMode": "none"
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.arrKeysToMask = [
				{ key='updateCustomerPaymentProfileRequest.merchantAuthentication.transactionKey', replacement='REDACTED' },
				{ key='updateCustomerPaymentProfileRequest.paymentProfile.payment.creditCard.cardNumber', replacement='XXXX#right(arguments.tokenArgs.fld_4_,4)#' }
			]>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>

			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<cfthrow message="There was an error saving the credit card.">
			<cfelse>
				<cfif NOT FindNoCase("XXXX",arguments.tokenArgs.fld_6_)>
					<cfset local.expMonth = numberformat(GetToken(arguments.tokenArgs.fld_6_,1,'/'),"09")>
					<cfset local.expYear = "20#GetToken(arguments.tokenArgs.fld_6_,2,'/')#">
					<cfset local.expDate = DateAdd("d",-1,DateAdd("m",1,"#local.expMonth#/1/#local.expYear#"))>
				</cfif>

				<cfquery name="local.qryUpdateCC" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET NOCOUNT ON;

					DECLARE @orgcode VARCHAR(10), @customerProfileID VARCHAR(50), @paymentProfileID VARCHAR(50), @detail VARCHAR(8);
					SET @orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.merchantOrgcode#">;
					SET @customerProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerProfileid#">;
					SET @paymentProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerPaymentProfileid#">;
					SET @detail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="XXXX#right(arguments.tokenArgs.fld_4_,4)#">;

					UPDATE dbo.ccMemberPaymentProfiles
					SET declined = 0,
						detail = @detail,
						<cfif NOT FindNoCase("XXXX",arguments.tokenArgs.fld_6_)>
							expiration = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.expDate#">,
						</cfif>
						checkedForZIP = 1,
						hasZIP = <cfif StructKeyExists(arguments.tokenArgs,"fld_11_") and len(arguments.tokenArgs.fld_11_)>1<cfelse>0</cfif>
					WHERE paymentProfileID = @paymentProfileID 
					AND customerProfileID = @customerProfileID
					AND orgcode = @orgcode;

					SELECT payProfileID
					FROM dbo.ccMemberPaymentProfiles
					WHERE paymentProfileID = @paymentProfileID 
					AND customerProfileID = @customerProfileID
					AND detail = @detail
					AND orgcode = @orgcode
					AND declined = 0;
				</cfquery>

				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objRet = new Object();
								objRet.a = 'save';
								objRet.mccardevent = 'cardUpdated';
								objRet.tspayprofileid = #val(local.qryUpdateCC.payProfileID)#;
							if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objRet,#arguments.customerPaymentProfileid#);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
			</cfif>
			
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="refreshPaymentProfiles" access="private" returntype="void" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.arrPayProfiles = arrayNew(1)>
		
		<cfset local.qryGetCIMInfo = getCIMInfo(merchantOrgcode=arguments.merchantOrgcode)>
		
		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"getCustomerProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#local.qryGetCIMInfo.authUsername#", 
							"transactionKey": "#local.qryGetCIMInfo.authTransKey#" 
						},
						"customerProfileId": "#arguments.customerProfileID#",
						"unmaskExpirationDate": true
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.arrKeysToMask = [
				{ key='getCustomerProfileRequest.merchantAuthentication.transactionKey', replacement='REDACTED' }
			]>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>

			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<!--- need to catch if profile was not found --->
				<cfif findNoCase("E00040",local.strAuthorize.rawAPIResponse)>
					<!--- do nothing. --->
				<cfelse>
					<cfthrow message="Error retrieving customer profile.">
				</cfif>
			<cfelse>
				<cfif local.strAuthorize.strAPIResponse.profile.keyExists("paymentProfiles")>
					<cfloop array="#local.strAuthorize.strAPIResponse.profile.paymentProfiles#" index="local.thisPaymentProfile">
						<cfset local.strProfile = { cpid=local.qryGetProfiles.customerProfileId, ppid=local.thisPaymentProfile.customerPaymentProfileId, 
							detail=local.thisPaymentProfile.payment.creditcard.cardnumber, haszip=0, expDate="" } >
						<cfif structKeyExists(local.thisPaymentProfile, "billTo") and structKeyExists(local.thisPaymentProfile.billTo, "zip") and len(local.thisPaymentProfile.billTo.zip)>
							<cfset local.strProfile.haszip = 1>
						</cfif>
						<cfif structKeyExists(local.thisPaymentProfile.payment.creditcard, "expirationDate") and len(local.thisPaymentProfile.payment.creditcard.expirationDate)>
							<cfset local.expMonth = numberformat(GetToken(local.thisPaymentProfile.payment.creditcard.expirationDate,2,'-'),"09")>
							<cfset local.expYear = GetToken(local.thisPaymentProfile.payment.creditcard.expirationDate,1,'-')>
							<cfset local.strProfile.expDate = DateAdd("d",-1,DateAdd("m",1,"#local.expMonth#/1/#local.expYear#"))>
						</cfif>
						<cfset arrayAppend(local.arrPayProfiles,local.strProfile)>
					</cfloop>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset local.arrPayProfiles = arrayNew(1)>
		</cfcatch>
		</cftry>	
		
		<cfif ArrayLen(local.arrPayProfiles)>
			<cfquery name="local.qryUpdateMPP" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @orgcode varchar(10), @customerProfileID varchar(50);
					declare @tblPP TABLE (cpid varchar(50), ppid varchar(50), detail varchar(50), expiration date, haszip bit);

					select @orgcode = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">;
					select @customerProfileID = <cfqueryparam value="#arguments.customerProfileID#" cfsqltype="CF_SQL_VARCHAR">;
				
					BEGIN TRAN
						<cfloop from="1" to="#ArrayLen(local.arrPayProfiles)#" index="local.pp">
							INSERT INTO @tblPP (cpid, ppid, detail, expiration, haszip)
							VALUES (
								<cfqueryparam value="#local.arrPayProfiles[local.pp].cpid#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.arrPayProfiles[local.pp].ppid#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.arrPayProfiles[local.pp].detail#" cfsqltype="CF_SQL_VARCHAR">,
								<cfif len(local.arrPayProfiles[local.pp].expDate)>
									<cfqueryparam value="#local.arrPayProfiles[local.pp].expDate#" cfsqltype="CF_SQL_DATE">,
								<cfelse>
									NULL,
								</cfif>
								<cfqueryparam value="#local.arrPayProfiles[local.pp].haszip#" cfsqltype="CF_SQL_BIT">
							);
						</cfloop>

						delete from dbo.ccMemberPaymentProfiles
						where customerProfileID = @customerProfileID
						and orgcode = @orgcode
						and not exists (
							select cpid
							from @tblPP
							where cpid = ccMemberPaymentProfiles.customerProfileID
							and ppid = ccMemberPaymentProfiles.paymentProfileID
							and detail = ccMemberPaymentProfiles.detail
						);
						
						update mpp
						set mpp.declined = 0,
							mpp.expiration = tbl.expiration
						from dbo.ccMemberPaymentProfiles as mpp
						inner join @tblPP as tbl on tbl.ppid = mpp.paymentProfileID and tbl.cpid = mpp.customerProfileID and tbl.detail = mpp.detail
						where mpp.orgcode = @orgcode;

						insert into dbo.ccMemberPaymentProfiles (customerid, depomemberdataid, orgcode, detail, expiration, customerProfileID, paymentProfileID, 
							dateAdded, declined, cardType, checkedForZIP, hasZIP)
						select '#arguments.customerid#', <cfif getToken(arguments.customerid,1,'_') eq "olddid">#getToken(arguments.customerid,2,'_')#<cfelse>null</cfif>, 
							@orgcode, detail, expiration, cpid, ppid, getdate(), 0, '', 1, haszip
						from @tblPP as tbl
						where not exists (
							select payProfileID
							from dbo.ccMemberPaymentProfiles
							where orgcode = @orgcode
							and customerProfileID = tbl.cpid
							and paymentProfileID = tbl.ppid
						);

						IF NOT EXISTS (
							SELECT TOP 1 payProfileID
							FROM dbo.ccMemberPaymentProfiles
							WHERE customerProfileID = @customerProfileID
						) BEGIN
							
							DECLARE @gatewayUsername varchar(50), @gatewayPassword varchar(75);
							SET @gatewayUsername = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryGetCIMInfo.authUsername#">;
							SET @gatewayPassword = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryGetCIMInfo.authTransKey#">;

							EXEC platformQueue.dbo.queue_authCIMCustCheck_load @profileID=0, @gatewayUsername=@gatewayUsername, 
								@gatewayPassword=@gatewayPassword, @customerProfileID=@customerProfileID;
						END
					COMMIT TRAN

					SELECT 1 AS success;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfif getToken(arguments.customerid,1,'_') eq "olddid">
				<cfquery name="local.qryinsertNote" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					insert into dbo.CustomerNotes (depomemberdataid, NoteTypeID, Note)
					values(
						<cfqueryparam value="#getToken(arguments.customerid,2,'_')#" cfsqltype="CF_SQL_INTEGER">,
						1,
						<cfqueryparam value="#arguments.merchantOrgcode# Credit Card Refreshed" cfsqltype="CF_SQL_VARCHAR">
					)
				</cfquery>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="addPaymentProfileReturn" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="chargeInfo" type="struct" required="no" default="#setDefaultChargeInfo()#">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.head = ''>

		<!--- create customer profile id if it is blank/invalid (if we just added 1st card on file, for example) --->
		<cfif NOT isValidCustomerProfile(merchantorgcode=arguments.merchantorgcode, customerProfileID=arguments.customerProfileID)>
			<cfset local.strCustProfile = createCustomerProfile(merchantOrgcode=arguments.merchantOrgcode, customerID=arguments.customerID)>
			<cfset arguments.customerProfileid = local.strCustProfile.customerProfileID>
			<cfif len(local.strCustProfile.head)>
				<cfset local.returnStruct.head = local.strCustProfile.head>
			</cfif>
		</cfif>

		<!--- get customer profile --->
		<cfif len(arguments.customerProfileId)>
			<cfset refreshPaymentProfiles(merchantOrgcode=arguments.merchantOrgcode, customerProfileID=arguments.customerProfileid, customerID=arguments.customerID)>

			<!--- get the gather form to update the calling page --->
			<cfset local.strGather = gather(merchantOrgcode=arguments.merchantOrgcode, customerID=arguments.customerID, chargeInfo=arguments.chargeInfo)>

			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				#local.returnStruct.head#
				<script language="javascript">
					$(function() {
						if (parent.p_#arguments.merchantOrgcode#_refresh) parent.p_#arguments.merchantOrgcode#_refresh();
						else{ 
							if(parent.p_#arguments.merchantOrgcode#_showMsg) {
								parent.$('##p_#arguments.merchantOrgcode#_DIV').parent().html('#JSStringFormat(local.strGather.inputForm)#');
								parent.p_#arguments.merchantOrgcode#_showMsg(); 
							} else
								parent.$('##p_#arguments.merchantOrgcode#_DIV').parent().html('#JSStringFormat(local.strGather.inputForm)#');
						
						}
						
						parent.$('button.appContinueBtn').removeAttr("disabled");
						parent.$('##caserefTable').show();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="chargeCard" access="public" returntype="struct" output="no">
		<cfargument name="CIMUsername" type="string" required="yes">
		<cfargument name="CIMPassword" type="string" required="yes">
		<cfargument name="payProfileID" type="numeric" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="detail" type="string" required="yes">
		<cfargument name="TransactionDepoMemberDataID" type="numeric" required="yes">
		<cfargument name="tokenData" type="struct" required="no" hint="required for applepay/googlepay">

		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.rawResponse=''>
		<cfset local.returnStruct.responseCode=99999>
		<cfset local.returnStruct.responseReasonText='Invalid request.'>
		<cfset local.returnStruct.publicResponseReasonText = "Invalid Request.">
		<cfset local.returnStruct.responseReasonCode=''>
		<cfset local.returnStruct.transactionID=''>
		<cfset local.returnStruct.cardType=''>
		<cfset local.returnStruct.authorizationCode=''>
		<cfset local.returnStruct.accountNumber=''>
		<cfset local.returnStruct.transactionDetail=''>
		<cfset local.isApplePay = 0>
		<cfset local.isGooglePay = 0>

		<!--- create request --->
		<cftry>

			<cfset local.clientIPAddress = application.objPlatform.getClientIP() />
			<cfset local.clientIsPrivateIPAddress = application.objPlatform.isPrivateIPAddress(local.clientIPAddress)/>

			<cfif isDefined("arguments.tokenData.tstokensource") and arguments.tokenData.tstokensource eq "applePay">
				<!--- Convert date from Apple's YYMMDD to YYYY-MM --->
				<cfset local.expDate = "20#left(arguments.tokenData.decryptedToken.applicationExpirationDate,2)#-#mid(arguments.tokenData.decryptedToken.applicationExpirationDate, "3", "2")#">

				<cfset local.cofPayProfileID = 0>
				<cfset local.merchantOrgCode = arguments.tokenData.moc>
				<cfset local.isApplePay = 1>

				<cfsavecontent variable="local.apiRequestBody">
					<cfoutput>{
						"createTransactionRequest": {
							"merchantAuthentication": {
								"name": "#arguments.CIMUsername#",
								"transactionKey": "#arguments.CIMPassword#"
							},
							"transactionRequest": {
								"transactionType": "authCaptureTransaction",
								"amount": "#arguments.amount#",
								"currencyCode": "USD",
								"payment": {
									"creditCard": {
										"cardNumber": "#arguments.tokenData.decryptedToken.applicationPrimaryAccountNumber#",
										"expirationDate": "#local.expDate#",
										"isPaymentToken": true,
										"cryptogram": "#arguments.tokenData.decryptedToken.paymentdata.onlinePaymentCryptogram#"
									}
								},
								"solution": {
									"id": "#variables.x_solution_id#"
								},
								"order": {
									"description": "#encodeForHTML(arguments.detail)#"
								},
								"tax": {
									"amount": 0
								},
								<cfif not local.clientIsPrivateIPAddress>
									"customerIP": "#local.clientIPAddress#",
								</cfif>
								"retail": {
									"marketType":0
								},
								"transactionSettings": {
									"setting": {
										"settingName": "recurringBilling",
										"settingValue": "false"
									}
								}
							}
						}
					}</cfoutput>
				</cfsavecontent>
				<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>
			
				<cfset local.arrKeysToMask = [
					{ key='createTransactionRequest.merchantAuthentication.transactionKey', replacement='REDACTED' },
					{ key='createTransactionRequest.transactionRequest.payment.creditCard.cardNumber', replacement='REDACTED' },
					{ key='createTransactionRequest.transactionRequest.payment.creditCard.cryptogram', replacement='REDACTED' }
				]>

			<cfelseif isDefined("arguments.tokenData.tstokensource") and arguments.tokenData.tstokensource eq "googlePay">
				<cfset local.cofPayProfileID = 0>
				<cfset local.merchantOrgCode = arguments.tokenData.moc>
				<cfset local.isGooglePay = 1>

				<cfsavecontent variable="local.apiRequestBody">
					<cfoutput>{
						"createTransactionRequest": {
							"merchantAuthentication": {
								"name": "#arguments.CIMUsername#",
								"transactionKey": "#arguments.CIMPassword#"
							},
							"transactionRequest": {
								"transactionType": "authCaptureTransaction",
								"amount": "#arguments.amount#",
								"currencyCode": "USD",
								"payment": {
									"opaqueData": {
										"dataDescriptor": "COMMON.GOOGLE.INAPP.PAYMENT",
										"dataValue": "#toBase64(arguments.tokenData.paymentData.paymentMethodData.tokenizationData.token)#"
									}
								},
								"solution": {
									"id": "#variables.x_solution_id#"
								},
								"order": {
									"description": "#encodeForHTML(arguments.detail)#"
								},
								"tax": {
									"amount": 0
								},
								"billTo": {
									"zip": "#arguments.tokenData.paymentData.paymentMethodData.info.billingAddress.postalCode#",
									"country": "#arguments.tokenData.paymentData.paymentMethodData.info.billingAddress.countryCode#"
								},
								<cfif not local.clientIsPrivateIPAddress>
									"customerIP": "#local.clientIPAddress#",
								</cfif>
								"retail": {
									"marketType": 0
								},
								"transactionSettings": {
									"setting": {
										"settingName": "recurringBilling",
										"settingValue": "false"
									}
								}
							}
						}
					}</cfoutput>
				</cfsavecontent>
				<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

				<cfset local.arrKeysToMask = [
					{ key='createTransactionRequest.merchantAuthentication.transactionKey', replacement='REDACTED' },
					{ key='createTransactionRequest.transactionRequest.payment.opaqueData.dataValue', replacement='REDACTED' }
				]>

			<cfelse>

				<cfquery name="local.qrygetCard" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					select customerid, detail, customerProfileID, paymentProfileID, orgcode
					from dbo.ccMemberPaymentProfiles 
					where payProfileID = <cfqueryparam value="#arguments.payProfileID#" cfsqltype="CF_SQL_INTEGER">
					and declined = 0
				</cfquery>

				<cfset local.cofPayProfileID = local.qrygetCard.paymentProfileID>
				<cfset local.merchantOrgCode = local.qrygetCard.orgcode>

				<cfsavecontent variable="local.apiRequestBody">
					<cfoutput>{
						"createTransactionRequest": {
							"merchantAuthentication": {
								"name": "#arguments.CIMUsername#",
								"transactionKey": "#arguments.CIMPassword#"
							},
							"transactionRequest": {
								"transactionType": "authCaptureTransaction",
								"amount": "#arguments.amount#",
								"currencyCode": "USD",
								"profile": {
									"customerProfileId": "#local.qrygetCard.customerProfileID#",
									"paymentProfile": { "paymentProfileId": "#local.qrygetCard.paymentProfileID#" }
								},
								"solution": {
									"id": "#variables.x_solution_id#"
								},
								"order": {
									"description": "#encodeForHTML(arguments.detail)#"
								},
								"tax": {
									"amount": 0
								},
								<cfif not local.clientIsPrivateIPAddress>
									"customerIP": "#local.clientIPAddress#",
								</cfif>
								"transactionSettings": {
									"setting": {
										"settingName": "recurringBilling",
										"settingValue": "false"
									}
								}
							}
						}
					}</cfoutput>
				</cfsavecontent>
				<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

				<cfset local.arrKeysToMask = [
					{ key='createTransactionRequest.merchantAuthentication.transactionKey', replacement='REDACTED' }
				]>
			</cfif>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>
			<cfset local.apiResponse = parseAuthorizeResponse(strAuthorizeResponse=local.strAuthorize, mode="charge")>
			
			<cfset local.returnStruct.rawResponse = local.strAuthorize.rawAPIResponse>
			<cfset local.returnStruct.responseCode = local.apiResponse.responseCode>
			<cfset local.returnStruct.responseReasonText = local.apiResponse.responseReasonText>
			<cfset local.returnStruct.publicResponseReasonText = local.apiResponse.publicResponseReasonText>
			<cfset local.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode>
			<cfset local.returnStruct.transactionid = local.apiResponse.transactionid>
			<cfset local.returnStruct.cardType = left(local.apiResponse.cardType,1)>
			<cfset local.returnStruct.accountNumber = local.apiResponse.accountNumber>
			<cfset local.returnStruct.authorizationCode = local.apiResponse.authorizationCode>
			<cfif local.returnStruct.responseCode is 1>
				<cfif local.isApplePay>
					<cfset local.returnStruct.transactionDetail = "Payment by Apple Pay #arguments.tokenData.encryptedToken.paymentMethod.displayname#">
				<cfelseif local.isGooglePay>
					<cfset local.returnStruct.transactionDetail = "Payment by Google Pay #arguments.tokenData.paymentData.paymentMethodData.description#">
				<cfelse>
					<cfset local.returnStruct.transactionDetail = "Payment by card #local.apiResponse.accountNumber#">
				</cfif>
			</cfif>
			
		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = cfcatch.message>
			<cfset local.returnStruct.publicResponseReasonText = "Payment Invalid.">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
		
		<cfif local.returnStruct.responseCode is 1>
			<cfset local.PaymentDescription = local.returnStruct.transactionDetail>
		<cfelse>
			<cfif local.cofPayProfileID GT 0 AND left(local.qrygetCard.customerid,7) eq "oldsid_">
				<cfset local.PaymentDescription = "Credit Card Declined - #local.qrygetCard.detail# - $#arguments.amount# - #local.returnStruct.responseReasonText#">
			<cfelseif local.isApplePay>
				<cfset local.PaymentDescription = "Apple Pay Declined - #arguments.tokenData.encryptedToken.paymentMethod.displayname# - $#arguments.amount#">
			<cfelseif local.isGooglePay>
				<cfset local.PaymentDescription = "Google Pay Declined - #arguments.tokenData.paymentData.paymentMethodData.description# - $#arguments.amount#">
			<cfelse>
				<cfset local.PaymentDescription = "Credit Card Declined - #local.returnStruct.accountNumber# - $#arguments.amount# - #local.returnStruct.responseReasonText#">
			</cfif>
		</cfif>

		<cfif arguments.TransactionDepoMemberDataID gt 0>
			<cfquery name="local.qryInsertTrans" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;

				declare @transactionID int;

				insert into dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depomemberdataid, 
					sourcestate, approvalCode, paymentmethod, ccTransactionID, ccResponseCode, ccResponseReasonCode, isPayment,
					statsSessionID, merchantOrgCode, refundableAmount, isApplePay, isGooglePay)
				select <cfqueryparam value="#local.PaymentDescription#" cfsqltype="CF_SQL_VARCHAR">, 
					<cfif local.returnStruct.responseCode is 1>
						<cfqueryparam value="#arguments.amount * -1#" cfsqltype="CF_SQL_DOUBLE">,
						0,
					<cfelse>
						0,
						0,
					</cfif>
					getdate(), depomemberdataid, tlamemberstate, 
					<cfqueryparam value="#local.returnStruct.authorizationCode#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#local.returnStruct.cardType#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#local.returnStruct.transactionID#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#local.returnStruct.responseCode#" cfsqltype="CF_SQL_INTEGER">,
					<cfqueryparam value="#local.returnStruct.responseReasonCode#" cfsqltype="CF_SQL_INTEGER">, 1,
					<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
					<cfqueryparam value="#local.merchantOrgCode#" cfsqltype="CF_SQL_VARCHAR">,
					<cfif local.returnStruct.responseCode is 1>
						<cfqueryparam value="#arguments.amount#" cfsqltype="CF_SQL_DOUBLE">
					<cfelse>
						null
					</cfif>,
					<cfqueryparam value="#local.isApplePay#" cfsqltype="CF_SQL_BIT">,
					<cfqueryparam value="#local.isGooglePay#" cfsqltype="CF_SQL_BIT">
				from dbo.depomemberdata
				where depomemberdataid = <cfqueryparam value="#arguments.TransactionDepoMemberDataID#" cfsqltype="CF_SQL_INTEGER">;

				select @transactionID = SCOPE_IDENTITY();

				select @transactionID as depoTransactionID;
			</cfquery>
			<cfset local.returnStruct.depoTransactionID = local.qryInsertTrans.depoTransactionID>
		<cfelse>
			<cfset local.returnStruct.strInsertTrans = structNew()>
			<cfset local.returnStruct.strInsertTrans.description = local.PaymentDescription>
			<cfif local.returnStruct.responseCode is 1>
				<cfset local.returnStruct.strInsertTrans.AmountBilled = arguments.amount * -1>
			<cfelse>
				<cfset local.returnStruct.strInsertTrans.AmountBilled = 0>
			</cfif>
			<cfset local.returnStruct.strInsertTrans.approvalCode = local.returnStruct.authorizationCode>
			<cfset local.returnStruct.strInsertTrans.paymentmethod = local.returnStruct.cardType>
			<cfset local.returnStruct.strInsertTrans.ccTransactionID = local.returnStruct.transactionID>
			<cfset local.returnStruct.strInsertTrans.ccResponseCode = local.returnStruct.responseCode>
			<cfset local.returnStruct.strInsertTrans.ccResponseReasonCode = local.returnStruct.responseReasonCode>
			<cfset local.returnStruct.strInsertTrans.payProfileID = arguments.payProfileID>
			<cfset local.returnStruct.strInsertTrans.customerid = local.cofPayProfileID GT 0 ? local.qrygetCard.customerid : "">
			<cfset local.returnStruct.strInsertTrans.transactionDetail = local.returnStruct.transactionDetail>
			<cfset local.returnStruct.strInsertTrans.isApplePay = local.isApplePay>
			<cfset local.returnStruct.strInsertTrans.isGooglePay = local.isGooglePay>
		</cfif>

		<cfif arguments.payProfileID>
			<cfquery name="local.qrySetDeclined" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				update dbo.ccMemberPaymentProfiles 
				set declined = 
					<cfif (local.returnStruct.responseCode is 1) OR (local.returnStruct.responseCode is 3 AND FindNoCase("A duplicate transaction has been submitted",local.returnStruct.responseReasonText))>
						0
					<cfelse>
						1
					</cfif>
					<cfif len(local.returnStruct.cardType)>, cardType = <cfqueryparam value="#local.returnStruct.cardType#" cfsqltype="CF_SQL_VARCHAR"></cfif>
				where payProfileID = <cfqueryparam value="#arguments.payProfileID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="callAuthorize" access="private" returntype="struct" output="no">
		<cfargument name="apiRequestBody" type="string" required="yes">
		<cfargument name="arrKeysToMask" type="array" required="no" default="#arrayNew(1)#">
	
		<cfset var local = StructNew()>
		<cfset local.returnStruct = { "success":false, "arrErrors":[], "rawAPIResponse":"", "strAPIResponse":{} }>

		<cfif application.objCommon.getCurrentRequestTimeout() lt variables.requesttimeout>
			<cfsetting requesttimeout="#variables.requesttimeout#">
		</cfif>

		<cfif variables.x_testmode is 1>
			<cfset local.AuthURL = "https://apitest.authorize.net/xml/v1/request.api">
		<cfelse>
			<cfset local.AuthURL = "https://api.authorize.net/xml/v1/request.api">
		</cfif>

		<cfset local.requestTimeout = (variables.requesttimeout-2)>
	
		<cftry>
			<cfhttp url="#local.AuthURL#" method="post" throwonerror="yes" result="local.APIResult" timeout="#local.requestTimeout#" charset="utf-8">
				<cfhttpparam type="header" name="Content-Type" value="application/json">
				<cfhttpparam type="body" value="#arguments.apiRequestBody#">
			</cfhttp>

			<cfset local.apiFileContent = local.APIResult.fileContent>
			<cfset local.apiFileContentPOS = find('{',local.apiFileContent)>
			<cfif local.apiFileContentPOS gt 0>
				<cfset local.apiFileContent = RemoveChars(local.apiFileContent,1,local.apiFileContentPOS-1)>
			</cfif>
			<cfset local.returnStruct.rawAPIResponse = local.apiFileContent>
			<cfset local.returnStruct.strAPIResponse = deserializeJSON(local.returnStruct.rawAPIResponse)>

			<cfset local.strLog = { 
				request = { 
					method="POST", 
					endpoint=local.AuthURL,
					bodycontent=maskRequestBody(apiRequestBody=arguments.apiRequestBody, arrKeysToMask=arguments.arrKeysToMask)
				}, response = {
					bodycontent=local.returnStruct.strAPIResponse,
					headers=local.APIResult.responseheader,
					statuscode=local.APIResult.status_code
				}}>
			<cfset logAPICall(strCall=local.strLog)>

			<cfif local.returnStruct.strAPIResponse.messages.resultCode EQ 'Error' AND isDefined("local.returnStruct.strAPIResponse.messages.message")>
				<cfloop array="#local.returnStruct.strAPIResponse.messages.message#" index="local.thisErr">
					<cfset local.returnStruct.arrErrors.append(local.thisErr)>
				</cfloop>
			</cfif>
			
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset local.returnStruct.arrErrors.append({ "text":cfcatch.message, "code":"callAuthorize_catch" })>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="parseAuthorizeResponse" access="private" output="false" returntype="struct">
		<cfargument name="strAuthorizeResponse" type="struct" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = StructNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="charge">
				<cfset local.response = { "responseCode"=3, "responseSubCode"=0, "responseReasonText"='', "publicResponseReasonText"='', 
					"responseReasonCode"=3, "authorizationCode"='', "avsResponse"='', "transactionid"=0, "description"='', "accountNumber"='', 
					"cardType"='' }>
			</cfcase>
			<cfdefaultcase>
				<cfset local.response = {}>
			</cfdefaultcase>
		</cfswitch>

		<cftry>
			<cfif arguments.strAuthorizeResponse.arrErrors.len()>
				<cfset local.response.responseCode = 3>
				<cfset local.response.publicResponseReasonText = "Unable to Complete Transaction">				
				<cfloop array="#arguments.strAuthorizeResponse.arrErrors#" index="local.thisErr">
					<cfset local.response.responseReasonText = local.response.responseReasonText & "[#local.thisErr.code#] #local.thisErr.text# ">
				</cfloop>
			<cfelse>

				<cfswitch expression="#arguments.mode#">
					<cfcase value="charge">
						<!--- if directResponse key exists, convert response to array. ensure 3rd argument is true to keep empty elements --->
						<cfif arguments.strAuthorizeResponse.strAPIResponse.keyExists("directResponse") AND len(arguments.strAuthorizeResponse.strAPIResponse.directResponse)>
							<cfscript>
							local.arrRawResponse = listToArray(arguments.strAuthorizeResponse.strAPIResponse.directResponse,'|',true);
			
							local.response.responseCode = local.arrRawResponse[1];	// 1 approved, 2 declined, 3 error, 4 held for review
							local.response.responseSubCode = local.arrRawResponse[2];
							local.response.responseReasonCode = local.arrRawResponse[3];
							local.response.responseReasonText = local.arrRawResponse[4];
							local.response.publicResponseReasonText = "";
							if (local.response.responseCode neq "1")
								local.response.publicResponseReasonText = "Unable to Complete Transaction";
							local.response.authorizationCode = local.arrRawResponse[5]; // The six-digit alphanumeric authorization or approval code.
							local.response.avsResponse = local.arrRawResponse[6];
							local.response.transactionID = local.arrRawResponse[7];
							local.response.description = local.arrRawResponse[9];
							local.response.accountNumber = 'XXXX' & right(local.arrRawResponse[51],4); // xxxx9999
							local.response.cardType = local.arrRawResponse[52]; // Visa, MasterCard, American Express, Discover, Diners Club, JCB
							</cfscript>
						<cfelse>
							<cfscript>
							local.response.responseCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.responseCode;
							local.response.responseSubCode = '';
							if (arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.keyExists("errors")) {
								local.response.responseReasonCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.errors[1].errorCode;
								local.response.responseReasonText = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.errors[1].errorText;
							} else if (arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.keyExists("messages")) {
								local.response.responseReasonCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.messages[1].code;
								local.response.responseReasonText = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.messages[1].description;
							}
							local.response.publicResponseReasonText = "";
							if (local.response.responseCode neq "1")
								local.response.publicResponseReasonText = "Unable to Complete Transaction";
							local.response.authorizationCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.authCode;
							local.response.avsResponse = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.avsResultCode;
							local.response.transactionID = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.transId;
							local.response.description = "";
							local.response.accountNumber = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.accountNumber; // xxxx9999
							local.response.cardType = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.accountType; // Visa, MasterCard, American Express, Discover, Diners Club, JCB
							</cfscript>
						</cfif>
					</cfcase>
					<cfdefaultcase>
						<cfset local.response.responseCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.responseCode>
					</cfdefaultcase>
				</cfswitch>

			</cfif>
		<cfcatch type="any">
			<cfset local.response.responseCode = 3>
			<cfset local.response.responseReasonText = cfcatch.message & " " & cfcatch.detail>
			<cfset local.response.publicResponseReasonText = "Unable to Complete Transaction">
		</cfcatch>
		</cftry>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="maskRequestBody" access="private" output="false" returntype="struct">
		<cfargument name="apiRequestBody" type="string" required="true">
		<cfargument name="arrKeysToMask" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.apiRequestBodyForLogging = deserializeJSON(arguments.apiRequestBody)>

		<cfif arrayLen(arguments.arrKeysToMask)>
			<cfloop array="#arguments.arrKeysToMask#" index="local.thisKeySet">
				<cfset local.thisKeyFullPath = "local.apiRequestBodyForLogging.#listDeleteAt(local.thisKeySet.key,listLen(local.thisKeySet.key,'.'),'.')#">
				<cfif isDefined("#local.thisKeyFullPath#")>
					<cfset structUpdate(structGet(local.thisKeyFullPath),listLast(local.thisKeySet.key,'.'),local.thisKeySet.replacement)>
				</cfif>
			</cfloop>
		</cfif>

		<cfreturn local.apiRequestBodyForLogging>
	</cffunction>

	<cffunction name="isValidCustomerProfile" access="private" returntype="boolean" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		
		<cfset var local = structNew()>

		<cfif NOT len(arguments.customerProfileID)>
			<cfreturn false>
		</cfif>

		<cfset local.qryGetCIMInfo = getCIMInfo(merchantOrgcode=arguments.merchantOrgcode)>

		<cfset local.validCustomerProfileId = "">
		
		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"getCustomerProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#local.qryGetCIMInfo.authUsername#", 
							"transactionKey": "#local.qryGetCIMInfo.authTransKey#" 
						},
						"customerProfileId": "#arguments.customerProfileID#",
						"includeIssuerInfo": false
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.arrKeysToMask = [
				{ key='getCustomerProfileRequest.merchantAuthentication.transactionKey', replacement='REDACTED' }
			]>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>
			
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<!--- need to catch if profile was not found --->
				<cfif findNoCase("E00040",local.strAuthorize.rawAPIResponse)>
					<!--- do nothing. --->
				<cfelse>
					<cfthrow message="Error retrieving customer profile.">
				</cfif>
			<cfelse>
				<cfset local.validCustomerProfileId = local.strAuthorize.strAPIResponse.profile.customerProfileId>
			</cfif>
		<cfcatch type="any">
			<cfset local.validCustomerProfileId = "">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
		
		<cfreturn len(local.validCustomerProfileId) GT 0>
	</cffunction>

	<cffunction name="getCIMInfo" access="private" output="false" returntype="query">
		<cfargument name="merchantOrgcode" type="string" required="true">

		<cfset var qryGetCIMInfo = "">

		<cfquery name="qryGetCIMInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select authUsername, authTransKey, authCIMGatewayAccountID, ApplePayEnabled, GooglePayEnabled, googlePayMerchantID
			from dbo.depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGetCIMInfo>
	</cffunction>

	<cffunction name="existsApplePayDomain" access="private" output="false" returntype="boolean">
		<cfargument name="merchantOrgCode" type="string" required="true">

		<cfset var qryApplePayDomain = "">

		<cfquery name="qryApplePayDomain" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(0,0,30,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @merchantIdentifier varchar(1024);
			
			SELECT @merchantIdentifier = LOWER(tier + '.#arguments.merchantOrgCode#.TSAuthorizeCIM.#session.mcstruct.siteCode#') 
			FROM dbo.fn_getServerSettings();

			SELECT hostName
			FROM dbo.tr_applePayDomains
			WHERE merchantIdentifier = @merchantIdentifier
			AND hostName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#getHostName()#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryApplePayDomain.recordCount EQ 1>
	</cffunction>

	<cffunction name="registerCurrentHostNameWithApplePay" access="private" output="false" returntype="boolean">
		<cfargument name="merchantOrgCode" type="string" required="true">

		<cfscript>
			var local = structnew();
			local.hostName = getHostName();
			local.applePayUtils = new model.system.platform.gateways.applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url);
			local.addDomainResult = local.applePayUtils.addDomainToMerchantForTS(merchantOrgCode=arguments.merchantOrgCode, siteCode=session.mcstruct.siteCode, domainName=local.hostName);
			if (NOT local.addDomainResult.success) {
				return false;
			}

			local.applePayMerchantIdentifier = local.applePayUtils.getDerivedApplePayMerchantIdentifierForTS(merchantOrgCode=arguments.merchantOrgCode, siteCode=session.mcstruct.siteCode);

			var qryAddApplePayDomain = queryExecute("EXEC dbo.tr_addApplePayDomains @merchantIdentifier = :merchantIdentifier, @hostNameList = :hostName;", 
				{ 
					merchantIdentifier = { value=local.applePayMerchantIdentifier, cfsqltype="CF_SQL_VARCHAR" },
					hostName = { value=local.hostName, cfsqltype="CF_SQL_VARCHAR" }
				},
				{ datasource="#application.dsn.membercentral.dsn#" }
			);

			return true;
		</cfscript>
	</cffunction>

	<cffunction name="getHostName" access="private" output="false" returntype="string">
		<cfreturn application.MCEnvironment eq "production" ? application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname : application.objPlatform.getCurrentHostname()>
	</cffunction>

	<cffunction name="getHostNameWithProtocol" access="private" output="false" returntype="string">
		<cfreturn (application.objPlatform.isRequestSecure() ? "https://" : "http://") & getHostName()>
	</cffunction>

	<cffunction name="logAPICall" output="false" access="private" returntype="void">
		<cfargument name="strCall" type="struct" required="true">

		<cfset var local = structnew()>

		<cftry>
			<cfset local.strRequest = {
				"c":"AuthorizeNet",
				"d": {
					"request": {
						"method":arguments.strCall.request.method,
						"endpoint":arguments.strCall.request.endpoint,
						"bodycontent":arguments.strCall.request.bodycontent
					},
					"response": {
						"bodycontent":arguments.strCall.response.bodycontent,
						"headers":arguments.strCall.response.headers,
						"statuscode":arguments.strCall.response.statuscode
					},
					"timestamp":now()
				}
			}>
	
			<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.membercentral.dsn#">
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
				VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="setDefaultChargeInfo" access="private" returntype="struct" output="no">
		<cfargument name="useChargeInfo" type="struct" required="false" default="#structNew()#">

		<cfset var strChargeInfo = {
			"amt": arguments.useChargeInfo.keyExists("amt") ? arguments.useChargeInfo.amt : 0,
			"acceptApplePay": arguments.useChargeInfo.keyExists("acceptApplePay") ? arguments.useChargeInfo.acceptApplePay : 0,
			"acceptGooglePay": arguments.useChargeInfo.keyExists("acceptGooglePay") ? arguments.useChargeInfo.acceptGooglePay : 0
		}>

		<cfreturn strChargeInfo>
	</cffunction>
	
</cfcomponent>