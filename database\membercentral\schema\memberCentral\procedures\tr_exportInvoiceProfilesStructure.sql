ALTER PROC dbo.tr_exportInvoiceProfilesStructure
@siteID int,
@exportPath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @orgCode varchar(10), @siteCode varchar(10), @cmd varchar(4000), @svrName varchar(40);

	SELECT @siteCode = s.siteCode, @orgID = o.orgID, @orgCode = o.orgCode
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	WHERE s.siteID = @siteID;
	
	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS VARCHAR(40));

	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfiles WHERE orgCode = @orgCode;
	DELETE FROM datatransfer.dbo.sync_tr_solicitationMessages WHERE siteCode = @siteCode;
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgCode = @orgCode;

	INSERT INTO datatransfer.dbo.sync_tr_invoiceProfiles (orgCode, orgID, profileID, profileName, [status], imageExt, 
		enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, numDaysDelinquent, enableProcessingFeeDonation, 
		processFeeDonationDefaultSelect, solicitationMessageID, orgIdentityOrgName)
	SELECT @orgCode, @orgID, ip.profileID, ip.profileName, 'A', ip.imageExt, ip.enableAutoPay, ip.enforcePayOldest, isnull(ip.notifyEmail,''), 
		ip.allowPartialPayment, isnull(ip.numDaysDelinquent,0), ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, 
		ip.solicitationMessageID, oi.organizationName
	FROM dbo.tr_invoiceProfiles AS ip
	INNER JOIN dbo.orgIdentities AS oi ON oi.orgID = @orgID
		AND oi.orgIdentityID = ip.orgIdentityID
	WHERE ip.orgID = @orgID
	AND ip.[status] = 'A'
	ORDER BY ip.profilename;

	INSERT INTO datatransfer.dbo.sync_tr_solicitationMessages (siteCode, siteID, messageID, title, message)
	SELECT @siteCode, @siteID, messageID, title, message
	FROM dbo.tr_solicitationMessages
	WHERE siteID = @siteID;

	INSERT INTO datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles (orgCode, orgID, invoiceProfileID, merchantProfileID, profileCode)
	select distinct @orgCode, @orgID, ipmp.invoiceProfileID, mp.profileID, mp.profileCode
	from dbo.tr_invoiceProfilesMerchantProfiles as ipmp
	inner join dbo.mp_profiles as mp on mp.profileID = ipmp.merchantProfileID
	inner join dbo.sites as s on s.siteID = mp.siteID
	where s.orgID = @orgID
	and mp.[status] = 'A';

	-- export to file
	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS INT) AS orgID, profileID, profileName, [status], imageExt, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID, orgIdentityOrgName, useOrgIdentityID, finalAction FROM datatransfer.dbo.sync_tr_invoiceProfiles WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_tr_invoiceProfiles.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp "SELECT siteCode, CAST(NULL AS INT) AS siteID, messageID, title, message, useMessageID, finalAction FROM datatransfer.dbo.sync_tr_solicitationMessages WHERE siteCode = ''' + @siteCode + '''" queryout "'+@exportPath+'sync_tr_solicitationMessages.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS INT) AS orgID, invoiceProfileID, merchantProfileID, profileCode, useInvoiceProfileID, useMerchantProfileID, finalAction FROM datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_tr_invoiceProfilesMerchantProfiles.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- clear sync tables
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfiles WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_tr_solicitationMessages WHERE siteID = @siteID;
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgID = @orgID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
