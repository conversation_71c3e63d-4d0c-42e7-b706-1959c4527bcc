ALTER PROC dbo.tr_getCreditBalances
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tblCredits') IS NOT NULL 
		DROP TABLE #tblCredits;
	CREATE TABLE #tblCredits (memberid int, memberName varchar(300), membernumber varchar(50), membercompany varchar(200), 
		totalCreditAmount decimal(18,2), creditAmount decimal(18,2), profileID int, profileName varchar(100));

	DECLARE @tblPayProfiles TABLE (profileID int);

	INSERT INTO @tblPayProfiles (profileID)
	select distinct mp.profileID
	from dbo.mp_profiles as mp
	inner join dbo.mp_gateways as g on mp.gatewayID = g.gatewayID
	inner join dbo.sites as s on s.siteID = mp.siteID
	where s.orgID = @orgID
	and mp.status = 'A'
	and g.isActive = 1
	and g.gatewayType <> 'PayLater';

	insert into #tblCredits (memberid, memberName, membernumber, membercompany, profileID, profileName, creditAmount)
	select m.memberid, 
		m.lastname 
		+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m.suffix,''),'') else '' end
		+ ', ' + m.firstname 
		+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m.middleName,''),'') else '' end
		as memberName, m.memberNumber, m.company, 
		mp.profileID, mp.profileName, cb.balance
	from dbo.tr_creditBalances as cb
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = cb.memberID
	inner join dbo.organizations as o on o.orgID = @orgID
	inner join dbo.mp_profiles as mp on mp.profileID = cb.profileID
	inner join @tblPayProfiles as mpList on mpList.profileID = mp.profileID
	where cb.orgID = @orgID;

	-- update totalCreditAmount by member
	update tmp
	set tmp.totalCreditAmount = tmp2.totalCreditAmount
	from #tblCredits as tmp
	inner join (
		select isnull(memberID,0) as memberID, sum(creditAmount) as totalCreditAmount
		from #tblCredits
		group by isnull(memberID,0)
	) as tmp2 on isnull(tmp2.memberID,0) = isnull(tmp.memberID,0);

	select memberid, memberName, membernumber, membercompany, profileName, creditAmount, totalCreditAmount
	from #tblCredits
	order by memberName, profileName;

	IF OBJECT_ID('tempdb..#tblCredits') IS NOT NULL 
		DROP TABLE #tblCredits;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
