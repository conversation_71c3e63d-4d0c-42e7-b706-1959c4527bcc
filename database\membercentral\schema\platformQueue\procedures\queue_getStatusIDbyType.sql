ALTER PROC dbo.queue_getStatusIDbyType
@queueType varchar(25),
@queueStatus varchar(30),
@queueStatusID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID INT;

	EXEC dbo.queue_getQueueTypeID @queueType=@queueType, @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus=@queueStatus, @queueStatusID=@queueStatusID OUTPUT;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
