ALTER PROC dbo.ams_processMemberGroupsFromQueue
@orgID int,
@itemGroupUID uniqueidentifier,
@runImmediately bit,
@optimizeQueue bit,
@dateTriggered datetime = NULL

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @itemGroupUID is null GOTO on_done;

	declare @totalID int, @publicGroupID int, @guestsGroupID int, @usersGroupID int, @xmlMessage xml, 
		@triggerLogID bigint, @runID bigint, @readyQueueStatusID int, 
		@memberSiteDefByOrgQueueStatusID int, @minSiteID int, @siteSRID int, @changeDate datetime, @dataXML xml,
		@thisMemberCount int, @allMemberCount int, @dateStarted DATETIME = getdate(), 
		@start datetime, @totalms int, @totalmsDequeueSection1 int, @totalmsDequeueSection2 int, 
		@totalmsDequeueSection3 int, @totalmsDequeueSection4 int;

	IF OBJECT_ID('tempdb..#tmpItemsPMG') IS NOT NULL 
		DROP TABLE #tmpItemsPMG;
	IF OBJECT_ID('tempdb..#cache_members_conditionsPMG') IS NOT NULL
		DROP TABLE #cache_members_conditionsPMG;
	IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleGroupsPMG') IS NOT NULL
		DROP TABLE #cache_ams_virtualGroupRuleGroupsPMG;
	IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleInfoPMG') IS NOT NULL
		DROP TABLE #cache_ams_virtualGroupRuleInfoPMG;
	IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditionsPMG') IS NOT NULL
		DROP TABLE #cache_ams_virtualGroupRuleConditionsPMG;
	IF OBJECT_ID('tempdb..#tblRGMPMG') IS NOT NULL
		DROP TABLE #tblRGMPMG;
	IF OBJECT_ID('tempdb..#tmpNewMemberGroupsPMG') IS NOT NULL
		DROP TABLE #tmpNewMemberGroupsPMG;
	IF OBJECT_ID('tempdb..#tmpNewMemberGroups2PMG') IS NOT NULL
		DROP TABLE #tmpNewMemberGroups2PMG;
	IF OBJECT_ID('tempdb..#tmpCurrentMemberGroupsPMG') IS NOT NULL
		DROP TABLE #tmpCurrentMemberGroupsPMG;
	IF OBJECT_ID('tempdb..#tmpDeletePMG') IS NOT NULL
		DROP TABLE #tmpDeletePMG;
	IF OBJECT_ID('tempdb..#tmpInsertPMG') IS NOT NULL
		DROP TABLE #tmpInsertPMG;
	IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditionSetsInfo') IS NOT NULL
		DROP TABLE #cache_ams_virtualGroupRuleConditionSetsInfo;
	IF OBJECT_ID('tempdb..#memberConditionSets') IS NOT NULL
		DROP TABLE #memberConditionSets;
	IF OBJECT_ID('tempdb..#conditionSetsToProcess') IS NOT NULL
		DROP TABLE #conditionSetsToProcess;

	CREATE TABLE #tmpItemsPMG (itemID bigint, orgID int, memberID int INDEX IX_tmpItemsPMG_memberID, 
		triggerLogID bigint, dateAdded datetime);
	CREATE TABLE #cache_members_conditionsPMG (memberID int, conditionID int, PRIMARY KEY (conditionID, memberid));
	CREATE TABLE #cache_ams_virtualGroupRuleGroupsPMG (ruleGroupID int PRIMARY KEY, ruleID int, groupID int, 
		INDEX cache_ams_virtualGroupRuleGroupsPMG_ruleID_groupID (ruleID, groupID));
	CREATE TABLE #cache_ams_virtualGroupRuleInfoPMG (ruleID int PRIMARY KEY, activeRuleVersionID int, 
		conditionCount int, usesOR bit, usesAND bit, usesExclude bit, runSection tinyint, ruleSQL varchar(max), 
		isSimple bit, 
		INDEX tmpcache_ams_virtualGroupRuleInfoPMG_runSection_conditionCount (runSection, conditionCount), 
		INDEX IX_ams_virtualGroupRuleInfo2 (usesOR,usesAND,usesExclude,isSimple));
	CREATE TABLE #cache_ams_virtualGroupRuleConditionSetsInfo (conditionSetID int PRIMARY KEY, 
		ruleID int INDEX IX_cache_ams_virtualGroupRuleConditionSetsInfo_ruleID, isAnd bit, isInclude bit, 
		parentConditionSetID int, conditionSetPath varchar(200), level int, isSetofSets bit, 
		conditionCount int NOT NULL default(0), childSetCount int NOT NULL default(0), 
		processed bit NOT NULL default(0));
	CREATE TABLE #conditionSetsToProcess (conditionSetID int, ruleID int, PRIMARY KEY (ruleID, conditionSetID));
	CREATE TABLE #memberConditionSets (memberID int, conditionSetID int, PRIMARY KEY (conditionSetID,memberID));
    CREATE TABLE #memberConditionSetsAllCombos (memberID int, conditionSetID int, PRIMARY KEY (conditionSetID,memberID));
	CREATE TABLE #cache_ams_virtualGroupRuleConditionsPMG (ruleID int, conditionID int, conditionSetID int, 
		PRIMARY KEY (ruleID, conditionSetID, conditionID));
	CREATE TABLE #tblRGMPMG (ruleID int, memberID int, groupID int INDEX IX_tblRGMPMG_groupID, PRIMARY KEY (ruleID, groupID, memberID));
	CREATE TABLE #tmpNewMemberGroupsPMG (orgID int, memberID int, groupID int, isManualDirect tinyint, 
		isManualIndirect tinyint, isVirtualDirect tinyint, isVirtualIndirect tinyint, 
		INDEX tmpNewMemberGroupsPMG_groupID_memberID (groupID, memberid));
	CREATE TABLE #tmpNewMemberGroups2PMG (autoid int IDENTITY(1,1), orgID int, memberID int, groupID int, 
		isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit, 
		INDEX tmpNewMemberGroups2PMG_groupID_memberID (groupID, memberid));
	CREATE TABLE #tmpCurrentMemberGroupsPMG (autoid int, orgid int, memberid int, groupid int, 
		isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit, 
		INDEX tmpCurrentMemberGroupsPMG_groupID_memberID (groupID, memberid));
	CREATE TABLE #tmpDeletePMG (autoid int PRIMARY KEY , memberid int, groupID int, 
		INDEX tmpDeletePMG_groupID_memberID (groupID, memberid));
	CREATE TABLE #tmpInsertPMG (tempID int IDENTITY(1,1) PRIMARY KEY, orgID int, memberID int, 
		groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit, 
		INDEX tmpInsertPMG_groupID_memberID (groupID, memberid));

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberGroups', @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='memberSiteDefByOrg', @queueStatus='readyToProcess', @queueStatusID=@memberSiteDefByOrgQueueStatusID OUTPUT;
	
	/* ************* */
	/* DEQUEUE ITEMS */
	/* ************* */
	SET @start = getdate();

	INSERT INTO #tmpItemsPMG (itemID, orgID, memberID, triggerLogID, dateAdded)
	select itemID, orgID, memberID, triggerLogID, dateAdded
	FROM platformQueue.dbo.queue_memberGroups
	WHERE orgID = @orgID
	AND itemGroupUID = @itemGroupUID
	AND statusID = @readyQueueStatusID;

	-- if no records just exit now
	IF @@ROWCOUNT = 0 GOTO on_done;

	SET @totalmsDequeueSection1 = datediff(ms,@start,getdate());

	-- get triggerLogID for this run
	SELECT TOP 1 @triggerLogID = triggerLogID, @changeDate = dateAdded
	FROM #tmpItemsPMG;

	-- grab other members from org if able to. if not, just grab same org/member entries
	IF @optimizeQueue = 1 BEGIN
		SET @start = getdate();

		UPDATE platformQueue.dbo.queue_memberGroups WITH (UPDLOCK, READPAST)
		SET itemGroupUID = @itemGroupUID,
			triggerLogID = @triggerLogID,
			dateUpdated = getdate()
			OUTPUT inserted.itemID, inserted.orgID, inserted.memberID
			INTO #tmpItemsPMG (itemID, orgID, memberID)
		WHERE orgID = @orgID
		AND statusID = @readyQueueStatusID
		AND itemGroupUID <> @itemGroupUID;

		SET @totalmsDequeueSection2 = datediff(ms,@start,getdate());
	END ELSE BEGIN
		SET @start = getdate();

		UPDATE platformQueue.dbo.queue_memberGroups WITH (UPDLOCK, READPAST)
		SET itemGroupUID = @itemGroupUID,
			triggerLogID = @triggerLogID,
			dateUpdated = getdate()
			OUTPUT inserted.itemID, inserted.orgID, inserted.memberID
			INTO #tmpItemsPMG (itemID, orgID, memberID)
		WHERE orgID = @orgID
		AND statusID = @readyQueueStatusID
		AND itemGroupUID <> @itemGroupUID;

		SET @totalmsDequeueSection3 = datediff(ms,@start,getdate());
	END

	-- get all member count for comparison
	SET @start = getdate();
	SELECT @allMemberCount = count(memberID) from dbo.ams_members where orgID = @orgID and [status] <> 'D';
	SET @totalmsDequeueSection4 = datediff(ms,@start,getdate());


	/* ************************************* */
	/* Log the run in the various log tables */
	/* ************************************* */
	SET @start = getdate();

	-- here until we release this proc; then can be removed once all messages are sending it 
	IF @dateTriggered IS NULL
		SELECT @dateTriggered = dateTriggered from platformstatsMC.dbo.cache_conditionsTriggerLog WHERE logID = @triggerLogID;

	SELECT @thisMemberCount = count(distinct memberID) from #tmpItemsPMG;

	-- log the groups run
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRun (runDate, triggerLogID, orgID, msQueued, memberCount) 
	VALUES (@dateStarted, @triggerLogID, @orgID, datediff(MILLISECOND,@dateTriggered,@dateStarted), @thisMemberCount);
		select @runID = SCOPE_IDENTITY();

	-- log initial run times captured above
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('dequeueSection1', @totalmsDequeueSection1, @runID);
	IF @totalmsDequeueSection2 IS NOT NULL
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('dequeueSection2', @totalmsDequeueSection2, @runID);
	IF @totalmsDequeueSection3 IS NOT NULL
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('dequeueSection3', @totalmsDequeueSection3, @runID);
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('getOrgMemberCount', @totalmsDequeueSection4, @runID);

	-- what members were we asked to run
	IF @thisMemberCount = @allMemberCount
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunMembers (runID, memberID)
		VALUES (@runID, 0);
	ELSE
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunMembers (runID, memberID)
		SELECT distinct @runID, memberID FROM #tmpItemsPMG;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('runLogging', @totalMS, @runID);


	/* *********************************** */
	/* get virtual assignments for members */
	/* *********************************** */
	-- populate temp table with filtered records for speed
	SET @start = getdate();

	insert into #cache_members_conditionsPMG (memberID, conditionID)
	select cmc.memberID, cmc.conditionID
	from #tmpItemsPMG as ti
	inner join dbo.cache_members_conditions as cmc on cmc.orgID = @orgID
		and cmc.memberID = ti.memberID
	group by cmc.memberID, cmc.conditionID;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('loadConditionsCache', @totalMS, @runID);


	-- get rules and groups for speed
	SET @start = getdate();

	insert into #cache_ams_virtualGroupRuleGroupsPMG (ruleGroupID, ruleID, groupID)
	select min(rg.ruleGroupID), rg.ruleID, rg.groupID
	from dbo.ams_virtualGroupRules as r 
	inner join dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
		and rv.ruleVersionID = r.activeVersionID
	inner join dbo.ams_virtualGroupRuleGroups as rg on rg.ruleID = r.ruleID
	INNER JOIN dbo.ams_groups as g on g.orgID = @orgID 
		and g.groupID = rg.groupid 
		and g.status = 'A'
	where r.orgID = @orgID
	and r.ruleTypeID = 1
	and r.isActive = 1
	and rv.ruleSQL <> ''
	and rv.ruleSQL <> '(  )'
	group by rg.ruleID, rg.groupID

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('loadRuleGroups', @totalMS, @runID);


	-- get rule info
	SET @start = getdate();

	insert into #cache_ams_virtualGroupRuleInfoPMG (ruleID, activeRuleVersionID, conditionCount, 
		usesOR, usesAND, usesExclude, runSection, isSimple)
	select r.ruleID, rv.ruleVersionID, rv.conditionCount, rv.usesOR, rv.usesAND, rv.usesExclude,
		case 
		when rv.conditionCount = 1 and rv.usesExclude = 0 then 1
		when rv.conditionCount > 1 and rv.usesOR = 0 and rv.usesAND = 1 and rv.usesExclude = 0 then 2
		when rv.conditionCount > 1 and rv.usesOR = 1 and rv.usesAND = 0 and rv.usesExclude = 0 then 3
		else 4
		end,
		rv.isSimple
	from (select distinct ruleID from #cache_ams_virtualGroupRuleGroupsPMG) as tmpR
	inner join dbo.ams_virtualGroupRules as r on r.orgID = @orgID and r.ruleID = tmpR.ruleID
	inner join dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
		and rv.ruleVersionID = r.activeVersionID;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('loadRuleInfo', @totalMS, @runID);


	-- get ruleSQL for runSection=4 rules
	SET @start = getdate();

	UPDATE tmp
	SET tmp.ruleSQL = rv.ruleSQL
	FROM #cache_ams_virtualGroupRuleInfoPMG as tmp
	INNER JOIN dbo.ams_virtualGroupRules as r on r.orgID = @orgID 
		and r.ruleID = tmp.ruleID
	INNER JOIN dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
		AND rv.ruleVersionID = r.activeVersionID
	WHERE tmp.runSection = 4;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('getRuleSQLSection4', @totalMS, @runID);


	-- expand rules and conditions
	SET @start = getdate();

	INSERT INTO #cache_ams_virtualGroupRuleConditionsPMG (ruleID, conditionID, conditionSetID)
	SELECT r.ruleID, rc.conditionID, rcs.conditionSetID
	FROM #cache_ams_virtualGroupRuleInfoPMG as tmp
	INNER JOIN dbo.ams_virtualGroupRules as r on r.orgID = @orgID 
		and r.ruleID = tmp.ruleID
	INNER JOIN dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
		AND rv.ruleVersionID = r.activeVersionID
	INNER JOIN dbo.ams_virtualGroupRuleConditionSets as rcs on rcs.ruleID = r.ruleID
		AND rcs.ruleVersionID = rv.ruleVersionID
	INNER JOIN dbo.ams_virtualGroupRuleConditions as rc on rc.conditionSetID = rcs.conditionSetID
	group by r.ruleID, rc.conditionID, rcs.conditionSetID;
	
	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('expandRules', @totalMS, @runID);


	-- get condition and set info	
	SET @start = getdate();

	insert into #cache_ams_virtualGroupRuleConditionSetsInfo (conditionSetID, ruleID, isAnd, isInclude,
		parentConditionSetID, conditionSetPath, level, isSetofSets, conditionCount)
	select cs.conditionSetID, temp.ruleID, cs.isAnd, cs.isInclude, cs.parentConditionSetID, 
		cs.conditionSetPath, level = 1+len(cs.conditionSetPath) - len(replace(cs.conditionSetPath,'.','')),
		isSetofSets = 
			case 
			when exists(select conditionSetID from ams_virtualGroupRuleConditionSets where parentConditionSetID=cs.conditionSetID) then 1
			else 0
			end,
		conditionCount = (select count(*) from #cache_ams_virtualGroupRuleConditionsPMG where ruleID=cs.ruleID and conditionSetID=cs.conditionSetID)
	from #cache_ams_virtualGroupRuleInfoPMG temp
	inner join dbo.ams_virtualGroupRuleConditionSets cs on cs.ruleVersionID = temp.activeRuleVersionID
		and (temp.usesExclude = 1 or (temp.usesAND = 1 and temp.usesOR=1 ));

	update si 
	set si.childSetCount = temp.numChildren
	from #cache_ams_virtualGroupRuleConditionSetsInfo si 
	inner join (
 		select parentConditionSetID, count(*) as numChildren
		from #cache_ams_virtualGroupRuleConditionSetsInfo
		where parentConditionSetID is not null
		group by parentConditionSetID
	) as temp on temp.parentConditionSetID = si.conditionSetID;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('getConditionSetInfo', @totalMS, @runID);

	/* ******************* */
	/* Expand Runsections  */
	/* ******************* */
    -- simple rules that use exclude: include AND; exclude AND
	-- single condition AND sets are processed as OR to skip unneeded aggregate
	SET @start = getdate();

	update ri 
	set ri.runSection = 5
	from #cache_ams_virtualGroupRuleInfoPMG ri 
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on ri.ruleID = iSet.ruleID
		and ri.runSection = 4
		and ri.isSimple = 1
		and iSet.isSetofSets = 0 
		and iSet.isAnd = 1 
		and iSet.isInclude = 1
		and iSet.conditionCount > 1
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on ri.ruleID = xSet.ruleID
		and xSet.isSetofSets = 0 
		and xSet.isAnd = 1 
		and xSet.isInclude = 0
		and xSet.conditionCount > 1;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('setRunSection5', @totalMS, @runID);


    -- simple rules that use exclude: include AND; exclude OR (+ single condition ANDs)
	-- single condition AND sets are processed as OR to skip aggregate
	SET @start = getdate();

	update ri 
	set ri.runSection = 6
	from #cache_ams_virtualGroupRuleInfoPMG ri 
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on ri.ruleID = iSet.ruleID
		and ri.runSection = 4
		and ri.isSimple = 1
		and iSet.isSetofSets = 0 
		and iSet.isAnd = 1 
		and iSet.isInclude = 1
		and iSet.conditionCount > 1
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on ri.ruleID = xSet.ruleID
		and xSet.isSetofSets = 0 
		and (xSet.isAnd=0 or xSet.conditionCount=1)
		and xSet.isInclude = 0
		and xSet.conditionCount > 0;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('setRunSection6', @totalMS, @runID);


	-- simple rules that use exclude: include OR (+ single condition ANDs); exclude AND
	-- single condition AND sets are processed as OR to skip aggregate
	SET @start = getdate();

	update ri 
	set ri.runSection = 7
	from #cache_ams_virtualGroupRuleInfoPMG ri 
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on ri.ruleID = iSet.ruleID
		and ri.runSection = 4
		and ri.isSimple = 1
		and iSet.isSetofSets = 0 
		and (iSet.isAnd=0 or iSet.conditionCount=1) 
		and iSet.isInclude = 1
		and iSet.conditionCount > 0
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on ri.ruleID = xSet.ruleID
		and xSet.isSetofSets = 0 
		and xSet.isAnd = 1 
		and xSet.isInclude = 0
		and xSet.conditionCount > 1;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('setRunSection7', @totalMS, @runID);


	-- simple rules that use exclude: include OR (+ single condition ANDs); exclude OR (+ single condition ANDs)
	-- single condition AND sets are processed as OR to skip aggregate
	SET @start = getdate();

	update ri 
	set ri.runSection = 8
	from #cache_ams_virtualGroupRuleInfoPMG ri 
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on ri.ruleID = iSet.ruleID
		and ri.runSection = 4
		and ri.isSimple = 1
		and iSet.isSetofSets = 0 
		and (iSet.isAnd=0 or iSet.conditionCount=1) 
		and iSet.isInclude = 1
		and iSet.conditionCount > 0
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on ri.ruleID = xSet.ruleID
		and xSet.isSetofSets = 0 
		and (xSet.isAnd=0 or xSet.conditionCount=1) 
		and xSet.isInclude = 0
		and xSet.conditionCount > 0;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('setRunSection8', @totalMS, @runID);


	-- simple rules that use exclude: no include; exclude AND
	-- single condition AND sets are processed as OR to skip aggregate
	SET @start = getdate();

	update ri 
	set ri.runSection = 9
	from #cache_ams_virtualGroupRuleInfoPMG ri 
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on ri.ruleID = xSet.ruleID
		and ri.runSection = 4
		and ri.isSimple = 1
		and xSet.isSetofSets = 0 
		and xSet.isAnd = 1 
		and xSet.isInclude = 0
		and xSet.conditionCount > 1
	left outer join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on ri.ruleID = iSet.ruleID
		and iSet.isSetofSets = 0 
		and iSet.isInclude = 1
		and iSet.conditionCount > 0
	where iSet.ruleID is null;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('setRunSection9', @totalMS, @runID);


	-- simple rules that use exclude: no include; exclude OR (+ single condition ANDs)
	SET @start = getdate();

	update ri 
	set ri.runSection = 10
	from #cache_ams_virtualGroupRuleInfoPMG ri 
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on ri.ruleID = xSet.ruleID
		and ri.runSection = 4
		and ri.isSimple = 1
		and xSet.isSetofSets = 0 
		and (xSet.isAnd=0 or xSet.conditionCount=1)  
		and xSet.isInclude = 0
		and xSet.conditionCount > 0
	left outer join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on ri.ruleID = iSet.ruleID
		and iSet.isSetofSets = 0 
		and iSet.isInclude = 1
		and iSet.conditionCount > 0
	where iSet.ruleID is null;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('setRunSection10', @totalMS, @runID);


	-- non-simple rules
	SET @start = getdate();

	update vgri 
	set vgri.runSection = 11
	from #cache_ams_virtualGroupRuleInfoPMG as vgri
	inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID=csi.ruleID
		and vgri.isSimple = 0
		and vgri.runSection = 4;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('setRunSection11', @totalMS, @runID);


	-- process all one-condition include rules
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 1;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select distinct rg.ruleID, cmc.memberID, rg.groupID
		from #cache_ams_virtualGroupRuleInfoPMG as vgri
		inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID
		inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
		inner join #cache_ams_virtualGroupRuleGroupsPMG as rg on rg.ruleID = vgri.ruleID
		where vgri.runSection = 1;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection1', @totalMS, @runID);
	END


	-- run all AND multi condition include rules
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 2;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select rg.ruleID, cmc.memberID, rg.groupID
		from #cache_ams_virtualGroupRuleInfoPMG as vgri
		inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID
		inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
		inner join #cache_ams_virtualGroupRuleGroupsPMG as rg on rg.ruleID = vgri.ruleID
		WHERE vgri.runSection = 2
		group by rg.ruleID, vgri.ruleID, cmc.memberID, vgri.conditionCount, rg.groupID
		having count(*) = vgri.conditionCount;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection2', @totalMS, @runID);
	END


	-- run all OR multi condition include rules
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 3;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select distinct rg.ruleID, cmc.memberID, rg.groupID
		from #cache_ams_virtualGroupRuleInfoPMG as vgri
		inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID
		inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
		inner join #cache_ams_virtualGroupRuleGroupsPMG as rg on rg.ruleID = vgri.ruleID
		WHERE vgri.runSection = 3;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection3', @totalMS, @runID);
	END


	-- run simple rules that use include AND; exclude AND
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 5;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select rg.ruleID, ruleMembers.memberid, rg.groupID
		from #cache_ams_virtualGroupRuleGroupsPMG as rg 
		inner join (
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on vgri.ruleID = iSet.ruleID
				and vgri.runSection = 5
				and iSet.isSetofSets = 0 
				and iSet.isInclude = 1
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = iSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, iSet.conditionCount
			having count(*) = iSet.conditionCount
				except
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on vgri.ruleID = xSet.ruleID
				and vgri.runSection = 5
				and xSet.isSetofSets = 0 
				and xSet.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = xSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, xSet.conditionCount
			having count(*) = xSet.conditionCount
		) as ruleMembers on ruleMembers.ruleID = rg.ruleID
		group by rg.ruleID, ruleMembers.memberid, rg.groupID;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection5', @totalMS, @runID);
	END


	-- run simple rules that use include AND; exclude OR
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 6;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select rg.ruleID, ruleMembers.memberid, rg.groupID
		from #cache_ams_virtualGroupRuleGroupsPMG as rg 
		inner join (
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on vgri.ruleID = iSet.ruleID
				and vgri.runSection = 6
				and iSet.isSetofSets = 0 
				and iSet.isInclude = 1
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = iSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, iSet.conditionCount
			having count(*) = iSet.conditionCount
				except
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on vgri.ruleID = xSet.ruleID
				and vgri.runSection = 6
				and xSet.isSetofSets = 0 
				and xSet.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = xSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, xSet.conditionCount
		) as ruleMembers on ruleMembers.ruleID = rg.ruleID
		group by rg.ruleID, ruleMembers.memberid, rg.groupID;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection6', @totalMS, @runID);
	END


	-- run simple rules that use include OR; exclude AND
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 7;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select rg.ruleID, ruleMembers.memberid, rg.groupID
		from #cache_ams_virtualGroupRuleGroupsPMG as rg 
		inner join (
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on vgri.ruleID = iSet.ruleID
				and vgri.runSection = 7
				and iSet.isSetofSets = 0 
				and iSet.isInclude = 1
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = iSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, iSet.conditionCount
				except
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on vgri.ruleID = xSet.ruleID
				and vgri.runSection = 7
				and xSet.isSetofSets = 0 
				and xSet.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = xSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, xSet.conditionCount
			having count(*) = xSet.conditionCount
		) as ruleMembers on ruleMembers.ruleID = rg.ruleID
		group by rg.ruleID, ruleMembers.memberid, rg.groupID;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection7', @totalMS, @runID);
    END


	-- run simple rules that use include OR; exclude OR
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 8;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select rg.ruleID, ruleMembers.memberid, rg.groupID
		from #cache_ams_virtualGroupRuleGroupsPMG as rg 
		inner join (
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo iSet on vgri.ruleID = iSet.ruleID
				and vgri.runSection = 8
				and iSet.isSetofSets = 0 
				and iSet.isInclude = 1
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = iSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, iSet.conditionCount
				except
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on vgri.ruleID = xSet.ruleID
				and vgri.runSection = 8
				and xSet.isSetofSets = 0 
				and xSet.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = xSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, xSet.conditionCount
		) as ruleMembers on ruleMembers.ruleID = rg.ruleID
		group by rg.ruleID, ruleMembers.memberid, rg.groupID;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection8', @totalMS, @runID);
    END


	-- run simple rules with NO include; exclude AND
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 9;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select rg.ruleID,ruleMembers.memberid, rg.groupID
		from #cache_ams_virtualGroupRuleGroupsPMG as rg 
		inner join (
			select m.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG vgri 
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.runSection = 9
				and vgri.ruleID = csi.ruleID
			cross join #tmpItemsPMG m
			group by m.memberID, vgri.ruleID
				except
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on vgri.ruleID = xSet.ruleID
				and vgri.runSection = 9
				and xSet.isSetofSets = 0 
				and xSet.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = xSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, xSet.conditionCount
			having count(*) = xSet.conditionCount
		) as ruleMembers on ruleMembers.ruleID = rg.ruleID
		left outer join #tmpItemsPMG m on ruleMembers.memberID = m.memberID
		group by rg.ruleID, ruleMembers.memberid, rg.groupID;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection9', @totalMS, @runID);
    END


	-- run simple rules with NO include; exclude OR
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 10;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
		select rg.ruleID, ruleMembers.memberid, rg.groupID
		from #cache_ams_virtualGroupRuleGroupsPMG as rg 
		inner join (
			select m.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG vgri 
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.runSection = 10
				and vgri.ruleID = csi.ruleID
			cross join #tmpItemsPMG m
			group by m.memberID, vgri.ruleID
				except
			select cmc.memberID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo xSet on vgri.ruleID = xSet.ruleID
				and vgri.runSection = 10
				and xSet.isSetofSets = 0 
				and xSet.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and rc.conditionSetID = xSet.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, vgri.ruleID, xSet.conditionCount
		) as ruleMembers on ruleMembers.ruleID = rg.ruleID
		left outer join #tmpItemsPMG m on ruleMembers.memberID = m.memberID
		group by rg.ruleID, ruleMembers.memberid, rg.groupID;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection10', @totalMS, @runID);
    END


	/* ************************************************************************************************************ */
	/* process sets that contain conditions and not sets, no matter where they are in complex rules */
	/* ************************************************************************************************************ */
	-- check for non-simple rules that don't use exclude
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 11;
	IF @totalID > 0 BEGIN

		-- run all AND multi condition include sets
		set @totalID = 0;
		select @totalID = count(*)
		from #cache_ams_virtualGroupRuleInfoPMG as vgri
		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
			and vgri.runSection = 11
			and csi.isSetofSets = 0
			and csi.isAnd = 1 
			and csi.isInclude = 1;
		IF @totalID > 0	BEGIN
			SET @start = getdate();

			insert into #memberConditionSets (memberid, conditionSetID)
			select cmc.memberID, csi.conditionSetID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
    		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 1 
				and csi.isInclude = 1
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and csi.conditionSetID = rc.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, csi.conditionSetID,csi.conditionCount
			having count(*) = csi.conditionCount;

			update csi 
			set csi.processed = 1
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 1 
				and csi.isInclude = 1;

			SET @totalMS = datediff(ms,@start,getdate());
			INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11a', @totalMS, @runID);
		END

		-- run all OR multi condition include sets
		set @totalID = 0;
		select @totalID = count(*)
		from #cache_ams_virtualGroupRuleInfoPMG as vgri
		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID=csi.ruleID
			and vgri.runSection = 11
			and csi.isSetofSets = 0
			and csi.isAnd = 0 
			and csi.isInclude = 1;
		IF @totalID > 0 BEGIN
			SET @start = getdate();

			insert into #memberConditionSets (memberid, conditionSetID)
			select cmc.memberID, csi.conditionSetID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
    		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 0 
				and csi.isInclude = 1
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and csi.conditionSetID = rc.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, csi.conditionSetID;

			update csi 
			set csi.processed = 1
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 0 
				and csi.isInclude = 1;

			SET @totalMS = datediff(ms,@start,getdate());
			INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11b', @totalMS, @runID);
		END

		-- run all AND multi condition exclude sets
		set @totalID = 0;
		select @totalID = count(*)
		from #cache_ams_virtualGroupRuleInfoPMG as vgri
		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
			and vgri.runSection = 11
			and csi.isSetofSets = 0
			and csi.isAnd = 1 
			and csi.isInclude = 0;
		IF @totalID > 0 BEGIN
			SET @start = getdate();

			insert into #memberConditionSets (memberid, conditionSetID)
            select m.memberID, csi.conditionSetID
            from #cache_ams_virtualGroupRuleInfoPMG vgri 
    		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID=csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 1 
				and csi.isInclude = 0
            cross join #tmpItemsPMG m
            group by m.memberID, csi.conditionSetID
				except
			select cmc.memberID, csi.conditionSetID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
    		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 1 
				and csi.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and csi.conditionSetID = rc.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, csi.conditionSetID, csi.conditionCount
			having count(*) = csi.conditionCount;

			update csi 
			set csi.processed = 1
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 1 
				and csi.isInclude = 0;

			SET @totalMS = datediff(ms,@start,getdate());
			INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11c', @totalMS, @runID);
		END

		-- run all OR multi condition exclude sets
		set @totalID = 0;
		select @totalID = count(*)
		from #cache_ams_virtualGroupRuleInfoPMG as vgri
		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
			and vgri.runSection = 11
			and csi.isSetofSets = 0
			and csi.isAnd = 0 
			and csi.isInclude = 0;
		IF @totalID > 0 BEGIN
			SET @start = getdate();

			insert into #memberConditionSets (memberid, conditionSetID)
			select m.memberID, csi.conditionSetID
			from #cache_ams_virtualGroupRuleInfoPMG vgri 
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 0 
				and csi.isInclude = 0
			cross join #tmpItemsPMG m
			group by m.memberID, csi.conditionSetID
				except
			select cmc.memberID, csi.conditionSetID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 0 
				and csi.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionsPMG as rc on rc.ruleID = vgri.ruleID 
				and csi.conditionSetID = rc.conditionSetID
			inner join #cache_members_conditionsPMG as cmc on cmc.conditionID = rc.conditionID
			group by cmc.memberID, csi.conditionSetID;

			update csi 
			set csi.processed = 1
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 0
				and csi.isAnd = 0 
				and csi.isInclude = 0;

			SET @totalMS = datediff(ms,@start,getdate());
			INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11d', @totalMS, @runID);
		END

		-- keep processing sets of sets until all are done
		WHILE 1=1 BEGIN
			SET @start = getdate();

			delete from #conditionSetsToProcess;

			insert into #conditionSetsToProcess (conditionSetID, ruleID)
			select csi.conditionSetID, vgri.ruleID
			from #cache_ams_virtualGroupRuleInfoPMG as vgri
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and vgri.runSection = 11
				and csi.isSetofSets = 1
				and csi.processed = 0
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
				and csi.conditionSetID = csi_child.parentConditionSetID
				and csi_child.processed = 1
			group by vgri.ruleID, csi.conditionSetID, csi.childSetCount
			having count(*) = csi.childSetCount;

			select @totalID = @@ROWCOUNT;

			SET @totalMS = datediff(ms,@start,getdate());
			INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11e', @totalMS, @runID);

			IF @totalID = 0 
				BREAK;

			-- run all AND multi condition set include rules
			set @totalID = 0;
			select @totalID = count(distinct cstp.conditionSetID)
			from #conditionSetsToProcess cstp
			inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and csi.conditionSetID = cstp.conditionSetID
				and csi.isAnd = 1 
				and csi.isInclude = 1
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
				and csi.conditionSetID = csi_child.parentConditionSetID
				and csi_child.processed = 1;
			IF @totalID > 0 BEGIN
				SET @start = getdate();

				insert into #memberConditionSets (memberid, conditionSetID)
				select mcs.memberID, csi.conditionSetID
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID=csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 1 
					and csi.isInclude = 1
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
					and csi.conditionSetID = csi_child.parentConditionSetID
					and csi_child.processed = 1
				inner join #memberConditionSets as mcs on mcs.conditionSetID = csi_child.conditionSetID
				group by mcs.memberID, csi.conditionSetID,csi.childSetCount
				having count(*) = csi.childSetCount;

				update csi 
				set csi.processed = 1
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 1 
					and csi.isInclude = 1
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
					and csi.conditionSetID = csi_child.parentConditionSetID
					and csi_child.processed = 1;

				SET @totalMS = datediff(ms,@start,getdate());
				INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11f', @totalMS, @runID);
			END

			-- run all OR multi condition include rules
			set @totalID = 0;
			select @totalID = count(distinct cstp.conditionSetID)
			from #conditionSetsToProcess cstp
			inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and csi.conditionSetID = cstp.conditionSetID
				and csi.isAnd = 0 
				and csi.isInclude = 1
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
				and csi.conditionSetID = csi_child.parentConditionSetID
				and csi_child.processed = 1;
			IF @totalID > 0 BEGIN
				SET @start = getdate();

				insert into #memberConditionSets (memberid, conditionSetID)
				select mcs.memberID, csi.conditionSetID
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 0 
					and csi.isInclude = 1
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
					and csi.conditionSetID = csi_child.parentConditionSetID
					and csi_child.processed = 1
				inner join #memberConditionSets as mcs on mcs.conditionSetID = csi_child.conditionSetID
				group by mcs.memberID, csi.conditionSetID;

				update csi 
				set csi.processed = 1
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 0 
					and csi.isInclude = 1
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
					and csi.conditionSetID = csi_child.parentConditionSetID
					and csi_child.processed = 1;

				SET @totalMS = datediff(ms,@start,getdate());
				INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11g', @totalMS, @runID);
			END

			-- run all AND multi condition exclude rules
			set @totalID = 0;
			select @totalID = count(distinct cstp.conditionSetID)
			from #conditionSetsToProcess cstp
			inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and csi.conditionSetID = cstp.conditionSetID
				and csi.isAnd = 1 
				and csi.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
				and csi.conditionSetID = csi_child.parentConditionSetID
				and csi_child.processed = 1;
			IF @totalID > 0 BEGIN
				SET @start = getdate();

				insert into #memberConditionSets (memberid, conditionSetID)
				select m.memberID, csi.conditionSetID
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 1 
					and csi.isInclude = 0
				cross join #tmpItemsPMG m
				group by m.memberID, csi.conditionSetID
					except
				select mcs.memberID, csi.conditionSetID
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 1 
					and csi.isInclude = 0
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
					and csi.conditionSetID = csi_child.parentConditionSetID
					and csi_child.processed = 1
				inner join #memberConditionSets as mcs on mcs.conditionSetID = csi_child.conditionSetID
				group by mcs.memberID, csi.conditionSetID,csi.childSetCount
				having count(*) = csi.childSetCount;

				update csi 
				set csi.processed = 1
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 1 
					and csi.isInclude = 0
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
					and csi.conditionSetID = csi_child.parentConditionSetID
					and csi_child.processed = 1;

				SET @totalMS = datediff(ms,@start,getdate());
				INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11h', @totalMS, @runID);
			END

			-- run all OR multi condition exclude rules
			set @totalID = 0;
			select @totalID = count(distinct cstp.conditionSetID)
			from #conditionSetsToProcess cstp
			inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
				and csi.conditionSetID = cstp.conditionSetID
				and csi.isAnd = 0 
				and csi.isInclude = 0
			inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
				and csi.conditionSetID = csi_child.parentConditionSetID
				and csi_child.processed = 1;

			IF @totalID > 0 BEGIN
				SET @start = getdate();

				insert into #memberConditionSets (memberid, conditionSetID)
				select m.memberID, csi.conditionSetID
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 0 
					and csi.isInclude = 0
				cross join #tmpItemsPMG m
				group by m.memberID, csi.conditionSetID
					except
				select mcs.memberID, csi.conditionSetID
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 0 
					and csi.isInclude = 0
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
					and csi.conditionSetID = csi_child.parentConditionSetID
					and csi_child.processed = 1
				inner join #memberConditionSets as mcs on mcs.conditionSetID = csi_child.conditionSetID
				group by mcs.memberID, csi.conditionSetID;

				update csi 
				set csi.processed = 1
				from #conditionSetsToProcess cstp
				inner join #cache_ams_virtualGroupRuleInfoPMG as vgri on cstp.ruleID = vgri.ruleID
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
					and csi.conditionSetID = cstp.conditionSetID
					and csi.isAnd = 0 
					and csi.isInclude = 0
				inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi_child on csi.ruleID = csi_child.ruleID
					and csi.conditionSetID = csi_child.parentConditionSetID
					and csi_child.processed = 1;

				SET @totalMS = datediff(ms,@start,getdate());
				INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11i', @totalMS, @runID);
			END
		END


		SET @start = getdate();

		insert into #tblRGMPMG (ruleID, memberid, groupID)
        select rg.ruleID,mcs.memberid, rg.groupID
        from #cache_ams_virtualGroupRuleInfoPMG as vgri
		inner join #cache_ams_virtualGroupRuleConditionSetsInfo csi on vgri.ruleID = csi.ruleID
			and vgri.runSection = 11
			and csi.parentConditionSetID is null
		inner join #cache_ams_virtualGroupRuleGroupsPMG as rg on rg.ruleID = vgri.ruleID
		inner join #memberConditionSets mcs on csi.conditionSetID = mcs.conditionSetID
		group by rg.ruleID, mcs.memberid, rg.groupID;

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processSection11j', @totalMS, @runID);
	END


	-- loop over remaining multi-condition rules 
	-- 10/2014 - we found using the #cache_members_conditionsPMG table in the ruleSQL was slower than using the real table.
	set @totalID = 0;
	select @totalID = count(*) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 4;
	IF @totalID > 0 BEGIN
		SET @start = getdate();

		declare @minRuleID int, @ruleSQL varchar(max), @SQLString nvarchar(max), @ParamDefinition nvarchar(100);
		set @ParamDefinition = N'@ruleID int';

		select @minRuleID = min(ruleID) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 4;
		while @minRuleID is not null BEGIN
			select @ruleSQL = ruleSQL from #cache_ams_virtualGroupRuleInfoPMG where ruleID = @minRuleID;

			set @SQLString = N'use membercentral; '
			set @SQLString = @SQLString + 'SET TRANSACTION ISOLATION LEVEL SNAPSHOT; ';
			set @SQLString = @SQLString + 'insert into #tblRGMPMG (ruleID, memberid, groupID) ';
			set @SQLString = @SQLString + 'select rg.ruleID, m.memberID, rg.groupID ';
			set @SQLString = @SQLString + 'from #tmpItemsPMG as m ';
			set @SQLString = @SQLString + 'inner join #cache_ams_virtualGroupRuleGroupsPMG as rg on rg.ruleID = @ruleID ';
			set @SQLString = @SQLString + 'where (' + @ruleSQL + ') ';
			set @SQLString = @SQLString + 'group by rg.ruleID, m.memberID, rg.groupID ';
			set @SQLString = @SQLString + 'SET TRANSACTION ISOLATION LEVEL READ COMMITTED; ';
			exec sp_executesql @SQLString, @ParamDefinition, @ruleID=@minRuleID;

			select @minRuleID = min(ruleID) from #cache_ams_virtualGroupRuleInfoPMG where runSection = 4 and ruleID > @minRuleID;
		END

		SET @totalMS = datediff(ms,@start,getdate());
		INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('processRuleByRule', @totalMS, @runID);
	END


	-- expand groups
	SET @start = getdate();

	INSERT INTO #tmpNewMemberGroupsPMG (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
	select DISTINCT rg.orgID, dg.memberID, rg.groupID, 0, 0, isVirtualDirect = rg.isBaseGroup, isVirtualIndirect = (~rg.isBaseGroup)
	FROM #tblRGMPMG as dg
	INNER JOIN dbo.cache_ams_recursiveGroups as rg ON rg.orgID = @orgID and dg.groupid = rg.startGroupid;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('expandGroups', @totalMS, @runID);


	/* ********************************** */
	/* get manual assignments for members */
	/* ********************************** */
	-- you could be directly assigned and indirectly assigned to the same group.
	SET @start = getdate();

	INSERT INTO #tmpNewMemberGroupsPMG (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
	SELECT DISTINCT @orgID, tmp.memberID, g.groupID, isManualDirect = rg.isBaseGroup, isManualIndirect = (~rg.isBaseGroup), 0, 0
	FROM #tmpItemsPMG as tmp
	INNER JOIN dbo.ams_memberGroups AS mg on mg.orgID = @orgID and mg.memberID = tmp.memberID
	INNER JOIN dbo.cache_ams_recursiveGroups as rg ON rg.orgID = @orgID and mg.groupid = rg.startGroupid
	INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID and g.groupID = rg.groupID and g.status = 'A'
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID 
		and m.memberid = mg.memberID 
		AND m.status <> 'D'
		and m.isProtected = 0;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('getManualAssign', @totalMS, @runID);

	-- public group for all members
	SET @start = getdate();
	select @publicGroupID = groupID from dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Public' AND [status] = 'A';
	select @guestsGroupID = groupID from dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Guests' AND [status] = 'A';
	select @usersGroupID = groupID from dbo.ams_groups where orgID = @orgID and isSystemGroup = 1 AND groupName = 'Users' AND [status] = 'A';
	
	INSERT INTO #tmpNewMemberGroupsPMG (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
	SELECT distinct @orgID, tmp.memberID, @publicGroupID, 0, 0, 0, 1
	FROM #tmpItemsPMG as tmp
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberID AND m.status <> 'D'
		union all
	SELECT @orgID, 0, @publicGroupID, 0, 0, 0, 1;

	-- guest group for all guests
	INSERT INTO #tmpNewMemberGroupsPMG (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
	SELECT distinct @orgID, tmp.memberID, @guestsGroupID, 0, 0, 0, 1
	FROM #tmpItemsPMG as tmp
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberID and m.status <> 'D' and m.memberTypeID = 1;

	-- users group for all users
	INSERT INTO #tmpNewMemberGroupsPMG (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
	SELECT distinct @orgID, tmp.memberID, @usersGroupID, 0, 0, 0, 1
	FROM #tmpItemsPMG as tmp
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberID and m.status <> 'D' and m.memberTypeID = 2;

	-- new member groups compacted
	INSERT INTO #tmpNewMemberGroups2PMG (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
	select orgID, memberID, groupID, 
		max(isManualDirect) as isManualDirect, 
		max(isManualIndirect) as isManualIndirect, 
		max(isVirtualDirect) as isVirtualDirect, 
		max(isVirtualIndirect) as isVirtualIndirect
	from #tmpNewMemberGroupsPMG
	group by orgID, memberID, groupID;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('getSystemGroupAssignments', @totalMS, @runID);


	/* ******************** */
	/* Modify member groups */
	/* ******************** */
	SET @start = getdate();

	-- current member groups
	INSERT INTO #tmpCurrentMemberGroupsPMG (autoid, orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
	select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
	from #tmpItemsPMG as tmp
	inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mg.memberID = tmp.memberID
		union
	select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
	from #tmpItemsPMG as tmp
	inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mg.memberID = 0;

	-- rows to be deleted: #tmpCurrentMemberGroupsPMG but not in #tmpNewMemberGroups2PMG
	-- also delete rows where memberID is no longer the activeMemberID (for cleanup)
	INSERT INTO #tmpDeletePMG (autoID, memberID, groupID)
	SELECT curr.autoid, curr.memberID, curr.groupID
	from #tmpCurrentMemberGroupsPMG as curr
	where not exists (
		select autoid 
		from #tmpNewMemberGroups2PMG
		where orgID = curr.orgID
		and memberID = curr.memberID
		and groupID = curr.groupID
		and isManualDirect = curr.isManualDirect
		and isManualIndirect = curr.isManualIndirect
		and isVirtualDirect = curr.isVirtualDirect
		and isVirtualIndirect = curr.isVirtualIndirect
	)
		union
	select mg.autoid, mg.memberID, mg.groupID
	from #tmpItemsPMG as tmp
	inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mg.memberID = tmp.memberID
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = mg.memberID
	where m.memberID <> m.activeMemberID
	or m.status = 'D';

	-- rows to be added: mem/grp in #tmpNewMemberGroups2PMG but not in #tmpCurrentMemberGroupsPMG
	INSERT INTO #tmpInsertPMG (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
	SELECT distinct new.orgid, new.memberid, new.groupid, new.isManualDirect, new.isManualIndirect, new.isVirtualDirect, new.isVirtualIndirect
	from #tmpNewMemberGroups2PMG as new
	where not exists (
		select autoid
		from #tmpCurrentMemberGroupsPMG
		where orgID = new.orgID
		and memberID = new.memberID
		and groupID = new.groupID
		and isManualDirect = new.isManualDirect
		and isManualIndirect = new.isManualIndirect
		and isVirtualDirect = new.isVirtualDirect
		and isVirtualIndirect = new.isVirtualIndirect
	);

	BEGIN TRAN;
		-- delete rows that shouldnt be there anymore
		DELETE vw
			OUTPUT @runID, deleted.memberID, deleted.groupID, deleted.isManualDirect, deleted.isManualIndirect, deleted.isVirtualDirect, deleted.isVirtualIndirect, 0
			INTO platformStatsMC.dbo.cache_groupsLogRunChanges (runid, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect, isAdded)
		FROM dbo.cache_members_groups as vw
		INNER JOIN #tmpDeletePMG as del on del.autoID = vw.autoID
		WHERE vw.orgID = @orgID;

		-- add rows that should be there now
		INSERT INTO dbo.cache_members_groups (orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
			OUTPUT @runID, inserted.memberID, inserted.groupid, inserted.isManualDirect, inserted.isManualIndirect, inserted.isVirtualDirect, inserted.isVirtualIndirect, 1
			INTO platformStatsMC.dbo.cache_groupsLogRunChanges (runid, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect, isAdded)
		SELECT distinct orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect
		FROM #tmpInsertPMG as tmp
		WHERE NOT EXISTS (select autoid from dbo.cache_members_groups where orgID = @orgID and memberid = tmp.memberid and groupID = tmp.groupID);
	COMMIT TRAN;

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('modifyMemberGroups', @totalMS, @runID);


	-- run the memberGroupChanged hook for each site in the org
	SET @start = getdate();

	declare @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));
	select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID;
	while @minSiteID is not null begin
		set @siteSRID = null;
		
		SELECT @siteSRID = siteResourceID from dbo.sites where siteID = @minSiteID;
		
		SELECT @dataXML = (
			SELECT @runID AS runid, @changeDate AS changedate
			FOR XML PATH ('data'));
		
		INSERT INTO @tblHookListeners (executionType, objectPath)
		exec dbo.hooks_runHook @event='memberGroupChanged', @siteResourceID=@siteSRID, @dataXML=@dataXML;

		select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID and siteID > @minSiteID;
	end

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('runWebHooks', @totalMS, @runID);


	/* ******************* */
	/* Modify group prints */
	/* ******************* */
	SET @start = getdate();

	declare @MGPqueueStatusID int, @GPitemGroupUID uniqueidentifier = NEWID(), @dateAdded datetime = GETDATE();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='MemberGroupPrints', @queueStatus='readyToProcess', @queueStatusID=@MGPqueueStatusID OUTPUT;

	INSERT INTO platformQueue.dbo.queue_memberGroupPrints (itemGroupUID, orgID, memberID, statusID, dateAdded, dateUpdated)
	select distinct @GPitemGroupUID, @orgID, d.memberID, @MGPqueueStatusID, @dateAdded, @dateAdded 
	from #tmpDeletePMG as d
	left outer join #tmpInsertPMG as i on i.memberid = d.memberid and i.groupid = d.groupID
	where i.tempID is null
		union 
	select @GPitemGroupUID, @orgID, i.memberID, @MGPqueueStatusID, @dateAdded, @dateAdded 
	from #tmpInsertPMG as i
	left outer join #tmpDeletePMG as d on i.memberid = d.memberid and i.groupid = d.groupID
	where d.autoID is null;

	IF @@ROWCOUNT > 0 BEGIN
		if @runImmediately = 1
			exec dbo.cache_perms_updateGroupPrintsForMembersBulk @itemGroupUID=@GPitemGroupUID;
		else begin
			SELECT @xmlMessage = (select @GPitemGroupUID as [itemGroupUID] for xml path(''), type);
			EXEC platformQueue.dbo.queue_MemberGroupPrints_sendMessage @xmlMessage=@xmlMessage;
		end

		INSERT INTO platformQueue.dbo.queue_memberSiteDefByOrg (orgID, dateAdded, dateUpdated, statusID)
		VALUES (@orgID, @dateAdded, @dateAdded, @memberSiteDefByOrgQueueStatusID);
	END

	SET @totalMS = datediff(ms,@start,getdate());
	INSERT INTO platformStatsMC.dbo.cache_groupsLogRunSteps (stepCode, timeMS, runID) VALUES ('modifyGroupPrints', @totalMS, @runID);


	-- Update the log entry with details
	UPDATE platformStatsMC.dbo.cache_groupsLogRun
	SET finishDate = getdate(),
		msProcessing = datediff(MILLISECOND,@dateStarted,getdate())
	WHERE runID = @runID
	AND orgID = @orgID;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpItemsPMG') IS NOT NULL 
		DROP TABLE #tmpItemsPMG;
	IF OBJECT_ID('tempdb..#cache_members_conditionsPMG') IS NOT NULL
		DROP TABLE #cache_members_conditionsPMG;
	IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleGroupsPMG') IS NOT NULL
		DROP TABLE #cache_ams_virtualGroupRuleGroupsPMG;
	IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleInfoPMG') IS NOT NULL
		DROP TABLE #cache_ams_virtualGroupRuleInfoPMG;
	IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditionsPMG') IS NOT NULL
		DROP TABLE #cache_ams_virtualGroupRuleConditionsPMG;
	IF OBJECT_ID('tempdb..#tblRGMPMG') IS NOT NULL
		DROP TABLE #tblRGMPMG;
	IF OBJECT_ID('tempdb..#tmpNewMemberGroupsPMG') IS NOT NULL
		DROP TABLE #tmpNewMemberGroupsPMG;
	IF OBJECT_ID('tempdb..#tmpNewMemberGroups2PMG') IS NOT NULL
		DROP TABLE #tmpNewMemberGroups2PMG;
	IF OBJECT_ID('tempdb..#tmpCurrentMemberGroupsPMG') IS NOT NULL
		DROP TABLE #tmpCurrentMemberGroupsPMG;
	IF OBJECT_ID('tempdb..#tmpDeletePMG') IS NOT NULL
		DROP TABLE #tmpDeletePMG;
	IF OBJECT_ID('tempdb..#tmpInsertPMG') IS NOT NULL
		DROP TABLE #tmpInsertPMG;
	IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditionSetsInfo') IS NOT NULL
		DROP TABLE #cache_ams_virtualGroupRuleConditionSetsInfo;
	IF OBJECT_ID('tempdb..#memberConditionSets') IS NOT NULL
		DROP TABLE #memberConditionSets;
	IF OBJECT_ID('tempdb..#conditionSetsToProcess') IS NOT NULL
		DROP TABLE #conditionSetsToProcess;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
