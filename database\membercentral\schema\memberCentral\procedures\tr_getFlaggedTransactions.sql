ALTER PROC dbo.tr_getFlaggedTransactions
@orgID int,
@limitRowsCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @totalCount int;

	IF OBJECT_ID('tempdb..#tmpTranAlerts') IS NOT NULL
		DROP TABLE #tmpTranAlerts;
	CREATE TABLE #tmpTranAlerts (transactionAlertID int PRIMARY KEY);

	INSERT INTO #tmpTranAlerts (transactionAlertID)
	SELECT transactionAlertID
	FROM dbo.tr_transactionAlerts AS ta
	WHERE ta.orgID = @orgID;

	SET @totalCount = @@ROWCOUNT;

	IF @totalCount > 0
		SELECT TOP (@limitRowsCount) tmp.transactionAlertID, t.dateRecorded, ta.[message], m2.memberid, m2.company,
			m2.lastname + ', ' + m2.firstname + ' (' + m2.membernumber + ')' AS memberName,
			@totalCount AS totalCount
		from #tmpTranAlerts AS tmp
		INNER JOIN dbo.tr_transactionAlerts AS ta ON ta.orgID = @orgID
			AND ta.transactionAlertID = tmp.transactionAlertID
		INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = @orgID
			AND t.transactionID = ta.transactionID
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
			AND m.memberid = t.assignedToMemberID
		INNER JOIN dbo.ams_members AS m2 ON m2.orgID = @orgID
			AND m2.memberid = m.activeMemberID
		ORDER BY t.dateRecorded;
	ELSE
		SELECT transactionAlertID
		FROM #tmpTranAlerts;

	IF OBJECT_ID('tempdb..#tmpTranAlerts') IS NOT NULL
		DROP TABLE #tmpTranAlerts;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
