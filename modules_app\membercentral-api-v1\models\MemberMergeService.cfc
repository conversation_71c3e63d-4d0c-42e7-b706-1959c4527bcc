component cache="true" {
	
	property name="memberService" inject="MemberService@mcapiV1";
	property name="memberUpdateService" inject="MemberUpdateService@mcapiV1";
	property name="RAIDUSERASSETROOT_PATH" type="string" inject="coldbox:setting:RAIDUSERASSETROOT_PATH";
	property name="exceptionEmail" type="string" inject="coldbox:setting:exceptionEmail";

	struct function mergeMembers (required numeric orgid, required numeric siteid, required string orgcode, required string membernumber, required string mergemembernumbers, required struct memberdata, required numeric recordedbymemberid) {
		var memberID = memberService.getMemberIDByMemberNumber(orgID=arguments.orgID, membernumber=arguments.membernumber, hideprotected=true);
		var strResult = { status="", message=[] };

		// invalid member
		if (memberID == 0)
			return { status="invalidMember", message=['Member not found.'] };

		// check merge membernumbers
		var orgID = arguments.orgid;
		var mergeMemberIDs = "";
		arguments.mergemembernumbers.listEach(function(memnum) {
			var thisMemberID = memberService.getMemberIDByMemberNumber(orgID=orgID, membernumber=arguments.memnum, hideprotected=true);
			if (thisMemberID > 0)
				mergeMemberIDs = listAppend(mergeMemberIDs,thisMemberID);
		});

		// invalid merge membernumbers
		if (listLen(mergeMemberIDs) NEQ listLen(arguments.mergemembernumbers))
			return { status="invalidMergeMember", message=['Invalid merge membernumbers.'] };

		var qryOrgMemberFields = getOrgMemberFields(orgID=arguments.orgid);
		var qryOrgAddresses = getOrgAddressTypes(orgID=arguments.orgid);
		var qryOrgAddressTags = getOrgAddressTagTypes(orgID=arguments.orgid);
		var qryOrgPhones = getOrgPhoneTypes(orgID=arguments.orgid);
		var qryOrgDistrictTypes = getOrgDistrictTypes(orgID=arguments.orgid);
		var qryOrgEmails = getOrgEmailTypes(orgID=arguments.orgid);
		var qryOrgEmailTags = getOrgEmailTagTypes(orgID=arguments.orgid);
		var qryOrgWebsites = getOrgWebsiteTypes(orgID=arguments.orgid);
		var qryOrgProfessionalLicenses = getOrgProfessionalLicenseTypes(orgID=arguments.orgid);
		var xmlAdditionalData = getOrgXMLAdditionalData(orgID=arguments.orgid);
		var qryDocumentColumns = getOrgDocColumns(orgID=arguments.orgid);

		var qryAllMembers = getAllMergeMembers(memberID=memberID, orgid=arguments.orgid, mergeMemberIDs=mergeMemberIDs);
		var allMemberIDs = valueList(qryAllMembers.memberID);

		// invalid merge membernumbers
		if (NOT listLen(allMemberIDs))
			return { status="invalidMergeMember", message=['Invalid merge membernumbers.'] };

		var strMember = memberService.getMember(orgid=arguments.orgid, siteid=arguments.siteid, membernumber=arguments.membernumber).member;
		var arrPayloadIgnored = [];
		var sqlParams = {
			orgID = { value=arguments.orgid, cfsqltype="CF_SQL_INTEGER" },
			siteID = { value=arguments.siteid, cfsqltype="CF_SQL_INTEGER" },
			chosenMembernumber = { value=arguments.membernumber, cfsqltype="CF_SQL_VARCHAR" },
			performedByMemberID = { value=arguments.recordedbymemberid, cfsqltype="CF_SQL_INTEGER" },
			mnpMemberID = { value=0, cfsqltype="CF_SQL_INTEGER" }
		};
		var sqlParamKey = ""
		var cfsqltype = "";
		var keyValue = "";

		// verify keys in memberdata are simple values. 
		var strFields = memberUpdateService.getFields(orgid=arguments.orgid, mode='membermerge');
		var lastRowID = StructCount(strFields);

		/* append custom field document cols and address district cols */
		cfloop(query="qryDocumentColumns") {
			lastRowID++;
			strFields.insert(lcase(qryDocumentColumns.columnName),{ colID=lastRowID, dataType=qryDocumentColumns.columnDataType, allowMultiple=qryDocumentColumns.allowMultiple, area=qryDocumentColumns.area });
		}
		cfloop(query='qryOrgAddresses') {
			cfloop(query='qryOrgDistrictTypes') {
				lastRowID++;
				strFields.insert(lcase("#qryOrgAddresses.addressType#_#qryOrgDistrictTypes.districtType#"),{ colID=lastRowID, dataType='varchar', allowMultiple=0, area='Addresses' });
			}
		}

		// update mnpMemberID in sqlParams if it is passed along
		var memnumforloginkey = 'MemberNumberForLoginData';
		if (arguments.memberdata.keyExists(memnumforloginkey) and len(arguments.memberdata[memnumforloginkey])){
			sqlParams.mnpMemberID.value = memberService.getMemberIDByMemberNumber(orgID=orgID, membernumber=arguments.memberdata[memnumforloginkey], hideprotected=true);
		}

		var arrFields = strFields.keyArray();
		for (var key in arguments.memberdata) {
			if (NOT isSimpleValue(arguments.memberdata[key]) OR NOT arrFields.findNoCase(key)) {
				arrPayloadIgnored.append(key);
				arguments.memberdata.delete(key);
			} elseif (key == "mcaccountstatus" and NOT listFindNoCase("active,inactive",arguments.memberdata[key])) {
				arrPayloadIgnored.append(key);
				arguments.memberdata.delete(key);
			}
		}

		// get all member data for membernumber
		for (var key in strMember) {
			// override data passed in
			if (arguments.memberdata.keyExists(key))
				strMember[key] = arguments.memberdata[key];

			if (NOT strFields.keyExists(key)) {
				lastRowID++;
				strFields.insert(lcase(key),{ colID=lastRowID, dataType='varchar', allowMultiple=0, area='Additional' });
			}

			sqlParamKey = "mcmerge_#getMergeFieldID(columnName=key, strFields=strFields)#";
			keyValue = strMember[key];
			cfsqltype = "CF_SQL_VARCHAR";
			
			if (key == "mcaccountstatus") {
				keyValue = strMember[key] EQ 'Active' ? 'A' : 'I';	
			} else if (strFields.keyExists(key)) {
				if (strFields[key].area EQ 'Custom Fields' AND (strFields[key].dataType == "varchar" OR strFields[key].allowMultiple EQ 1)) {
					cfsqltype = "CF_SQL_LONGVARCHAR";
				} else if (listFindNoCase("datetime,date",strFields[key].dataType)) {
					cfsqltype = "CF_SQL_DATE";
				} elseif (strFields[key].dataType == "decimal") {
					cfsqltype = "CF_SQL_DECIMAL";
				} elseif (strFields[key].dataType == "int") {
					cfsqltype = "CF_SQL_INTEGER";
				} elseif (strFields[key].dataType == "bit") {
					cfsqltype = "CF_SQL_BIT";
				} else {
					cfsqltype = "CF_SQL_VARCHAR";
				}
			}

			structInsert(sqlParams,sqlParamKey,{ value="#keyValue#", cfsqltype="#cfsqltype#" });
		}

		// generate merge message for all accounts being merged in
		cfloop (query="qryAllMembers") {
			if (strMember['membernumber'] NEQ qryAllMembers.membernumber) {
				structInsert(
					sqlParams, 
					'memNumChangeMsg_#qryAllMembers.memberID#', 
					{ value="Record #qryAllMembers.lastName#, #qryAllMembers.firstName# (#qryAllMembers.memberNumber#) merged into #strMember['lastName']#, #strMember['firstName']# (#strMember['membernumber']#)", 
						cfsqltype="CF_SQL_VARCHAR" 
					}
				);
			}
		}

		// get manual group assignments for all members (what it will be after merge)
		var qryNewManualGroups = getManualGroupsAllMembers(orgID=arguments.orgID, allMemberIDs=allMemberIDs);

		/* get any duplicate event registrations (duplicates after merge) */
		var qryNewDupeRegistrations = getDupeEventRegAllMembers(allMemberIDs=allMemberIDs);


		var sqlMerge = "";
		savecontent variable="sqlMerge" {
			writeOutput("SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

				IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
					DROP TABLE ##tmpAuditLog;
				CREATE TABLE ##tmpAuditLog (auditCode varchar(10), msg varchar(max));
			
				DECLARE @defaultSiteID int, @orgCode varchar(10), @newMemberID int, @memberTypeID int, 
					@recordTypeID int, @emailTypeID int, @addressTypeID int, @addressID int, @stateID int, @countryID int, 
					@stateCode VARCHAR(4), @stateName VARCHAR(40), @countryName VARCHAR(100),
					@districtValueID int, @PLStatusID int, @columnValue varchar(max), @contentID int, @columnValueSiteResourceID int, 
					@mergesID int, @memNumCheck bit, @newMemberNumber varchar(50), @oldStatus char(1), @maxSA datetime, @maxLogin datetime, 
					@earliestDateCreated datetime = getdate(), @email varchar(255), @website varchar(400), @payProfileID int, @minSiteID int, 
					@memberAdminSRID int, @dataXML xml, @profileID INT, @passwordSalt uniqueidentifier, @passwordHash binary(64),
					@userName varchar(75), @isValidCredentials bit, @MFAPhoneNumber varchar(50), @MFATOTPIdentity uniqueidentifier,
					@MFATOTPSecret varchar(50), @MFATOTPSid varchar(34), @mnpID int, @mnpSiteID int, @payProfileIDList varchar(max);
			");
			for (var key in sqlParams) {
				switch(sqlParams[key].cfsqltype) {
					case "CF_SQL_VARCHAR": case "CF_SQL_LONGVARCHAR":
						writeOutput("DECLARE @#key# varchar(max) = :#key#;");
						break;
					case "CF_SQL_DATE":
						if (len(sqlParams[key].value))
							writeOutput("DECLARE @#key# date = :#key#;");
						else 
							writeOutput("DECLARE @#key# date;");
						break;
					case "CF_SQL_DECIMAL":
						if (len(sqlParams[key].value))
							writeOutput("DECLARE @#key# decimal(14,2) = :#key#;");
						else 
							writeOutput("DECLARE @#key# decimal(14,2);");
						break;
					case "CF_SQL_INTEGER":
						if (len(sqlParams[key].value))
							writeOutput("DECLARE @#key# int = :#key#;");
						else 
							writeOutput("DECLARE @#key# int;");
						break;
					case "CF_SQL_BIT":
						if (len(sqlParams[key].value))
							writeOutput("DECLARE @#key# bit = :#key#;");
						else 
							writeOutput("DECLARE @#key# bit;");
						break;
				}
			}
			cfloop(query="qryAllMembers") {
				writeOutput("DECLARE @mergeID#qryAllMembers.memberid# int;");
			}
			writeOutput("DECLARE @tblDeletedMembers TABLE (memberID int, memberNumber varchar(50));
				DECLARE @tblDepoIDs TABLE (mergeID int, profileID int, depomemberdataid int);
				DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT @defaultSiteID = dbo.fn_getDefaultSiteIDFromOrgID(@orgID);

				SELECT @orgCode = orgCode
				FROM dbo.organizations
				WHERE orgID = @orgID;

				SELECT @memberTypeID = memberTypeID 
				from dbo.ams_memberTypes 
				where memberType = @mcmerge_#getMergeFieldID(columnName="mcaccounttype", strFields=strFields)#;
				
				SELECT @recordTypeID = recordTypeID 
				from dbo.ams_recordTypes 
				where orgID = @orgID
				and recordTypeName = @mcmerge_#getMergeFieldID(columnName="mcrecordtype", strFields=strFields)#;

				-- get the active MNP into a temp table for later reference
				IF OBJECT_ID('tempdb..##tmpMNP') IS NOT NULL 
					DROP TABLE ##tmpMNP;
				CREATE TABLE ##tmpMNP (mnpID int, memberID int, siteID int, profileID int, userName varchar(75), PasswordHash binary(64), PasswordSalt uniqueidentifier, 
					isValidCredentials bit, dateSiteAgreement datetime, dateLastLogin datetime, MFAPhoneNumber varchar(50), MFATOTPIdentity uniqueidentifier,
					MFATOTPSecret varchar(50), MFATOTPSid varchar(34));

				INSERT INTO ##tmpMNP (mnpid, memberID, siteID, profileID, userName, PasswordHash, PasswordSalt, isValidCredentials, dateSiteAgreement, dateLastLogin, 
					MFAPhoneNumber, MFATOTPIdentity, MFATOTPSecret, MFATOTPSid)
				SELECT mnp.mnpID, mnp.memberID, mnp.siteID, mnp.profileID, mnp.userName, mnp.PasswordHash, mnp.PasswordSalt, mnp.isValidCredentials, mnp.dateSiteAgreement, mnp.dateLastLogin,
					mnp.MFAPhoneNumber, mnp.MFATOTPIdentity, mnp.MFATOTPSecret, mnp.MFATOTPSid
				FROM dbo.ams_memberNetworkProfiles AS mnp
				INNER JOIN dbo.sites AS s ON s.siteID = mnp.siteID
					AND s.orgID = @orgID
				WHERE mnp.memberID IN (#valuelist(qryAllMembers.memberid)#)
				AND mnp.[status] = 'A';

				BEGIN TRAN;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

					-- get a new mergesID, we'll update the newMemberID after it is created
					INSERT INTO dbo.ams_merges(performedByMemberID)
					VALUES (@performedByMemberID);
						SELECT @mergesID = SCOPE_IDENTITY();
			");

			/* make the members deleted so the membernumber constraint wont cause problems when the new member is created. */
			cfloop(query='qryAllMembers') {
				writeOutput("
					INSERT INTO dbo.ams_mergeMembers (mergeID, memberID, [status])
					SELECT @mergesID, memberID, [status] 
					FROM dbo.ams_members 
					WHERE memberID = #qryAllMembers.memberid#;
					
					SELECT @mergeID#qryAllMembers.memberid# = SCOPE_IDENTITY();
					
					UPDATE dbo.ams_members 
					SET [status] = 'D', 
						dateLastUpdated = GETDATE() 
						OUTPUT inserted.memberID, inserted.memberNumber INTO @tblDeletedMembers
					WHERE memberID = #qryAllMembers.memberid#;

					/* which active network profiles are involved with these members */
					INSERT INTO @tblDepoIDs (mergeid, profileid, depomemberdataid)
					SELECT distinct @mergesID, np.profileid, np.depomemberdataid
					FROM dbo.ams_memberNetworkProfiles as mnp
					INNER JOIN dbo.ams_networkProfiles as np on np.profileID = mnp.profileID AND np.status = 'A'
					WHERE mnp.memberid = #qryAllMembers.memberid#
					AND mnp.status <> 'D';

					INSERT INTO dbo.ams_mergeMemberNetworkProfile (mergeMemberID, mnpid, status)
					SELECT @mergeID#qryAllMembers.memberid#, mnpID, [status] FROM dbo.ams_memberNetworkProfiles WHERE memberid = #qryAllMembers.memberid#;

					UPDATE dbo.ams_memberNetworkProfiles SET [status] = 'D' WHERE memberid = #qryAllMembers.memberid#;

					INSERT INTO dbo.ams_mergeSiteDefaults (mergeMemberID, defaultID, status)
					SELECT @mergeID#qryAllMembers.memberid#, msd.defaultID, msd.[status] 
					FROM dbo.ams_memberSiteDefaults as msd
					INNER JOIN dbo.sites as s on s.siteID = msd.siteID and s.orgID = @orgID
					WHERE msd.memberid = #qryAllMembers.memberid#;

					UPDATE msd
					SET msd.[status] = 'D' 
					FROM dbo.ams_memberSiteDefaults as msd
					INNER JOIN dbo.ams_mergeSiteDefaults as mm on mm.defaultID = msd.defaultID
					WHERE mm.mergeMemberID = @mergeID#qryAllMembers.memberid#;
				");
			}
				
			writeOutput("
				select @earliestDateCreated = min(earliestDateCreated)
				from dbo.ams_members
				where orgID = @orgID
				and memberID in (#allMemberIDs#);

				EXEC dbo.ams_createMember @orgid=@orgID, @memberTypeID=@memberTypeID, @recordTypeID=@recordTypeID,
					@prefix=#qryOrgMemberFields.hasprefix is 1 ? '@mcmerge_#getMergeFieldID(columnName="prefix", strFields=strFields)#' : ''''''#,
					@firstname=@mcmerge_#getMergeFieldID(columnName="firstname", strFields=strFields)#,
					@middlename=#qryOrgMemberFields.hasmiddlename is 1 ? '@mcmerge_#getMergeFieldID(columnName="middlename", strFields=strFields)#' : ''''''#,
					@lastname=@mcmerge_#getMergeFieldID(columnName="lastname", strFields=strFields)#,
					@suffix=#qryOrgMemberFields.hassuffix is 1 ? '@mcmerge_#getMergeFieldID(columnName="suffix", strFields=strFields)#' : ''''''#,
					@professionalsuffix=#qryOrgMemberFields.hasprofessionalsuffix is 1 ? '@mcmerge_#getMergeFieldID(columnName="professionalsuffix", strFields=strFields)#' : ''''''#,
					@company=@mcmerge_#getMergeFieldID(columnName="company", strFields=strFields)#, 
					@memberNumber=@chosenMembernumber, 
					@status=@mcmerge_#getMergeFieldID(columnName="mcaccountstatus", strFields=strFields)#, 
					@bypassQueue=1, @bypassHook=1, @memberID=@newMemberID OUTPUT, @newMemberNumber=@newMemberNumber OUTPUT;

				UPDATE dbo.ams_members
				SET earliestDateCreated = @earliestDateCreated
				WHERE memberID = @newMemberID;

				-- remove any default values since we will be setting them below
				delete from dbo.ams_memberData
				where memberID = @newMemberID;

				UPDATE dbo.ams_merges
				set newMemberID = @newMemberID
				where mergeID = @mergesID;

				-- create defaults on each site in the org
				SELECT @minSiteID = MIN(siteID) FROM dbo.sites WHERE orgID = @orgID;
				WHILE @minSiteID IS NOT NULL BEGIN
					EXEC dbo.ams_createMemberSiteDefault @memberID=@newMemberID, @siteID=@minSiteID, @defaultUsername=null, @defaultPassword=null;
					SELECT @minSiteID = MIN(siteID) FROM dbo.sites WHERE orgID = @orgID AND siteID > @minSiteID;
				END
			");

			// copy emails
			cfloop(query='qryOrgEmails') {
				writeOutput("
					update dbo.ams_memberEmails
					set email = @mcmerge_#getMergeFieldID(columnName=qryOrgEmails.emailType, strFields=strFields)#
					where orgID = @orgID
					and memberID = @newMemberID
					and emailTypeID = #qryOrgEmails.emailTypeID#;
				");
			}

			/* copy email tags */
			cfloop(query='qryOrgEmailTags') {
				writeOutput("
					SET @emailTypeID = NULL;
					
					SELECT @emailTypeID = emailTypeID 
					FROM dbo.ams_memberEmailTypes 
					WHERE orgID = @orgID 
					AND emailType = @mcmerge_#getMergeFieldID(columnName="Designated #qryOrgEmailTags.emailTagType#_emailType", strFields=strFields)#;

					EXEC dbo.ams_setMemberEmailTag @memberID=@newMemberID, @orgID=@orgID, @emailTagType='#qryOrgEmailTags.emailTagType#', 
						@emailTypeID=@emailTypeID, @bypassQueue=1;
				");
			}
			
			/* copy websites */
			cfloop(query='qryOrgWebsites') {
				writeOutput("
					update dbo.ams_memberWebsites
					set website = @mcmerge_#getMergeFieldID(columnName=qryOrgWebsites.websiteType, strFields=strFields)#
					where orgID = @orgID
					and memberID = @newMemberID
					and websiteTypeID = #qryOrgWebsites.websiteTypeID#;
				");
			}

			/* copy address tags */
			cfloop(query='qryOrgAddressTags') {
				writeOutput("
					SET @addressTypeID = NULL;
					
					SELECT @addressTypeID = addressTypeID
					FROM dbo.ams_memberAddressTypes 
					WHERE orgID = @orgID 
					AND addressType = @mcmerge_#getMergeFieldID(columnName="Designated #qryOrgAddressTags.addressTagType#_addressType", strFields=strFields)#;

					EXEC dbo.ams_setMemberAddressTag @memberID=@newMemberID, @orgID=@orgID, @addressTagType='#qryOrgAddressTags.addressTagType#', 
						@addressTypeID=@addressTypeID, @bypassQueue=1;
				");
			}

			/* copy addresses */
			var hasAddress = false;
			cfloop(query='qryOrgAddresses') {
				if (qryOrgAddresses.hasAttn AND len(strMember["#qryOrgAddresses.addressType#_attn"])) hasAddress = true;
				else if (len(strMember["#qryOrgAddresses.addressType#_address1"])) hasAddress = true;
				else if (qryOrgAddresses.hasAddress2 AND len(strMember["#qryOrgAddresses.addressType#_address2"])) hasAddress = true;
				else if (qryOrgAddresses.hasAddress3 AND len(strMember["#qryOrgAddresses.addressType#_address3"])) hasAddress = true;
				else if (len(strMember["#qryOrgAddresses.addressType#_city"])) hasAddress = true;
				else if (len(strMember["#qryOrgAddresses.addressType#_stateprov"])) hasAddress = true;
				else if (len(strMember["#qryOrgAddresses.addressType#_postalCode"])) hasAddress = true;
				else if (qryOrgAddresses.hasCounty AND len(strMember["#qryOrgAddresses.addressType#_county"])) hasAddress = true;
				else if (len(strMember["#qryOrgAddresses.addressType#_country"])) hasAddress = true;

				if (hasAddress) {
					writeOutput("SELECT @addressID = NULL, @stateID = NULL, @stateCode = NULL, @stateName = NULL, @countryID = NULL, @countryName = NULL;");
					
					if (len(strMember["#qryOrgAddresses.addressType#_stateprov"])) {
						writeOutput("SELECT @stateID = s.stateID, @countryID = c.countryID
							FROM dbo.ams_states AS s
							INNER JOIN dbo.ams_countries AS c ON c.countryID = s.countryID
							WHERE c.country = @mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_country", strFields=strFields)#
							AND s.Code = @mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_stateprov", strFields=strFields)#;
						");
					}
					
					writeOutput("
						IF @stateID is not null
							select @countryID = countryID, @stateCode = Code, @stateName = Name from dbo.ams_states where stateID = @stateID;
						IF @countryID is not null
							select @countryName = country from dbo.ams_countries where countryID = @countryID;

						INSERT into dbo.ams_memberAddresses (orgID, memberID, addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, county, countryID, stateCode, stateName, countryName)
						VALUES (@orgID, @newMemberID, #qryOrgAddresses.addressTypeID#, 
							#qryOrgAddresses.hasAttn AND len(strMember["#qryOrgAddresses.addressType#_attn"]) ? '@mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_attn", strFields=strFields)#' : 'NULL'#, 
							@mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_address1", strFields=strFields)#, 
							#qryOrgAddresses.hasAddress2 AND len(strMember["#qryOrgAddresses.addressType#_address2"]) ? '@mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_address2", strFields=strFields)#' : 'NULL'#, 
							#qryOrgAddresses.hasAddress3 AND len(strMember["#qryOrgAddresses.addressType#_address3"]) ? '@mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_address3", strFields=strFields)#' : 'NULL'#, 
							@mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_city", strFields=strFields)#, 
							@stateID, 
							@mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_postalCode", strFields=strFields)#, 
							#qryOrgAddresses.hasCounty AND len(strMember["#qryOrgAddresses.addressType#_county"]) ? '@mcmerge_#getMergeFieldID(columnName="#qryOrgAddresses.addressType#_county", strFields=strFields)#' : 'NULL'#, 
							@countryID, @stateCode, @stateName, @countryName);

							SELECT @addressID = SCOPE_IDENTITY();
					");

					cfloop(query='qryOrgPhones') {
						if (len(strMember["#qryOrgAddresses.addressType#_#qryOrgPhones.phoneType#"])) {
							writeOutput("INSERT INTO dbo.ams_memberPhones (orgID, memberID, phoneTypeID, addressID, phone)
								VALUES (@orgID, @newMemberID, #qryOrgPhones.phoneTypeID#, @addressID, @mcmerge_#getMergeFieldID(columnName='#qryOrgAddresses.addressType#_#qryOrgPhones.phoneType#', strFields=strFields)#);");
						}
					}

					if (qryOrgAddresses.districtMatching) {
						cfloop(query='qryOrgDistrictTypes') {
							if (len(strMember["#qryOrgAddresses.addressType#_#qryOrgDistrictTypes.districtType#"])) {
								for (var districtValue in listToArray(strMember["#qryOrgAddresses.addressType#_#qryOrgDistrictTypes.districtType#"], "|")) { 
									writeOutput("
										SET @districtValueID = NULL;

										SELECT @districtValueID = valueID
										FROM dbo.ams_memberDistrictValues 
										WHERE districtTypeID = #qryOrgDistrictTypes.districtTypeID# 
										AND vendorValue = @mcmerge_#getMergeFieldID(columnName='#qryOrgAddresses.addressType#_#qryOrgDistrictTypes.districtType#', strFields=strFields)#;

										IF @districtValueID IS NOT NULL
											INSERT INTO dbo.ams_memberAddressData (orgID, addressID, valueID)
											VALUES (@orgID, @addressID, @districtValueID);
									"); 
								}
							}
						}
					}
				}
			}

			/* copy professional licences */
			cfloop(query='qryOrgProfessionalLicenses') {
				if (len(strMember['#qryOrgProfessionalLicenses.PLName#_status'])) {
					writeOutput("
						SET @PLStatusID = NULL;

						SELECT @PLStatusID = PLStatusID
						FROM dbo.ams_memberProfessionalLicenseStatuses
						WHERE orgID = @orgID
						AND statusName = @mcmerge_#getMergeFieldID(columnName="#qryOrgProfessionalLicenses.PLName#_status", strFields=strFields)#;

						INSERT INTO dbo.ams_memberProfessionalLicenses (memberID, PLTypeID, LicenseNumber, ActiveDate, PLStatusID)
						VALUES (@newMemberID, #qryOrgProfessionalLicenses.PLTypeID#, @mcmerge_#getMergeFieldID(columnName="#qryOrgProfessionalLicenses.PLName#_licenseNumber", strFields=strFields)#, 
							@mcmerge_#getMergeFieldID(columnName="#qryOrgProfessionalLicenses.PLName#_activeDate", strFields=strFields)#, @PLStatusID);
					");
				}
			}

			writeOutput("
				-- payment profiles
				IF OBJECT_ID('tempdb..##tmpMPPToCopy') IS NOT NULL 
					DROP TABLE ##tmpMPPToCopy;
				CREATE TABLE ##tmpMPPToCopy (oldPayProfileID int PRIMARY KEY, newPayProfileID int);

				-- copy all active profiles
				INSERT INTO ##tmpMPPToCopy (oldPayProfileID)
				select distinct mpp.payProfileID
				from dbo.ams_memberPaymentProfiles as mpp
				where mpp.memberID in (#allMemberIDs#)
				and mpp.status = 'A' 
				and not exists (
					select payProfileID
					from dbo.ams_memberPaymentProfiles
					where memberid = @newMemberID
					and customerProfileID = mpp.customerProfileID
					and paymentProfileID = mpp.paymentProfileID
				);

				INSERT INTO dbo.ams_memberPaymentProfiles (memberID, profileID, status, detail, nickname, customerProfileID, paymentProfileID, cardTypeID, dateAdded, 
					addedStatsSessionID, oldPayProfileIDForMerge, otherFields, addedByMemberID, checkedForZIP, hasZIP, expiration, surchargeEligible)
				select @newMemberID, mpp.profileID, mpp.status, mpp.detail, mpp.nickname, mpp.customerProfileID, mpp.paymentProfileID, mpp.cardTypeID, mpp.dateAdded, 
					mpp.addedStatsSessionID, mpp.payProfileID, cast(mpp.otherFields as varchar(max)), mpp.addedByMemberID, mpp.checkedForZIP, mpp.hasZIP, mpp.expiration,
					mpp.surchargeEligible
				from ##tmpMPPToCopy as tmp
				INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = tmp.oldPayProfileID;

				-- get the new mpp tied to the old one
				UPDATE tmp
				SET tmp.newPayProfileID = mpp.payProfileID
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.oldPayProfileIDForMerge = tmp.oldPayProfileID;

				-- copy bank accounts
				INSERT INTO dbo.tr_bankAccounts (orgID, siteID, MPPPayProfileID, routingNumber, accountNumber, acctType)
				SELECT b.orgID, b.siteID, tmp.newPayProfileID, b.routingNumber, b.accountNumber, b.acctType
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.tr_bankAccounts as b on b.MPPPayProfileID = tmp.oldPayProfileID;

				-- copy any bin data
				INSERT INTO dbo.ams_memberPaymentProfilesBinData (payProfileID, tokenExJSON, type, fundingSource, prepaid)
				SELECT tmp.newPayProfileID, bd.tokenExJSON, bd.type, bd.fundingSource, bd.prepaid
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.ams_memberPaymentProfilesBinData as bd on bd.payProfileID = tmp.oldPayProfileID;

				-- reprocess cc exp conditions if any added pay profiles for new memberID
				SELECT @payProfileIDList = STRING_AGG(newPayProfileID,',')
				FROM ##tmpMPPToCopy;

				IF @payProfileIDList IS NOT NULL
					EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @payProfileIDList=@payProfileIDList, @lookupMode='ccprofile';

				-- subs
				INSERT INTO ##tmpAuditLog (auditCode, msg)
				SELECT 'SUBS', 'Pay Profile updated in member merge for Subscription [' + ss.subscriptionName + '] (SubscriberID ' + CAST(s.subscriberID AS varchar(10)) + ')'
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.sub_subscribers as s on s.orgID = @orgID AND s.payProfileID = tmp.oldPayProfileID
				INNER JOIN dbo.sub_subscriptions AS ss ON ss.orgID = @orgID AND ss.subscriptionID = s.subscriptionID;

				UPDATE s
				SET s.payProfileID = tmp.newPayProfileID
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.sub_subscribers as s on s.orgID = @orgID AND s.payProfileID = tmp.oldPayProfileID;

				-- inv
				INSERT INTO ##tmpAuditLog (auditCode, msg)
				SELECT 'INV', 'Pay Profile updated in member merge for Invoice ' + i.fullInvoiceNumber
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID AND i.payProfileID = tmp.oldPayProfileID
				INNER JOIN dbo.organizations as o on o.orgID = @orgID;

				UPDATE i
				SET i.payProfileID = tmp.newPayProfileID
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID AND i.payProfileID = tmp.oldPayProfileID;

				-- cp
				INSERT INTO ##tmpAuditLog (auditCode, msg)
				SELECT 'CP', 'Pay Profile updated in member merge for Contribution Program [' 
					+ cp.programName + ISNULL(' - ' + cpc.campaignName,'') + '] (ContributionID ' + CAST(c.contributionID AS varchar(10)) + ')'
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.cp_contributionPayProfiles as cpp on cpp.payProfileID = tmp.oldPayProfileID
				INNER JOIN dbo.cp_contributions AS c ON c.contributionID = cpp.contributionID
				INNER JOIN dbo.cp_programs AS cp on cp.programID = c.programID
				LEFT OUTER JOIN dbo.cp_campaigns AS cpc ON cpc.campaignID = c.campaignID;

				UPDATE cpp
				SET cpp.payProfileID = tmp.newPayProfileID
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.cp_contributionPayProfiles as cpp on cpp.payProfileID = tmp.oldPayProfileID;

				-- tasks
				UPDATE t
				SET t.payProfileID = tmp.newPayProfileID
				FROM ##tmpMPPToCopy as tmp
				INNER JOIN dbo.tasks_tasks as t on t.payProfileID = tmp.oldPayProfileID;


				-- after updating all associated records with new payProfileIDs, mark all active profiles D for merged members
				-- trg_ams_memberPaymentProfiles will be triggered from within the proc for each member, which has been updated to ignore deleted members to prevent condition processing for the merged memberIDs
				SELECT @payProfileID = MIN(oldPayProfileID) FROM ##tmpMPPToCopy;
				WHILE @payProfileID IS NOT NULL BEGIN
					EXEC dbo.ams_deleteCardOnFile @payProfileID=@payProfileID, @recordedByMemberID=@performedByMemberID;
					SELECT @payProfileID = MIN(oldPayProfileID) FROM ##tmpMPPToCopy WHERE oldPayProfileID > @payProfileID;
				END

				IF OBJECT_ID('tempdb..##tmpMPPToCopy') IS NOT NULL 
					DROP TABLE ##tmpMPPToCopy;

				-- this will trigger the trg_ams_memberPaymentProfiles trigger but nothing will happen because the update field is not in play
				UPDATE dbo.ams_memberPaymentProfiles
				SET oldPayProfileIDForMerge = null
				where memberID in (@newMemberID,#allMemberIDs#);
			");
		
			/* copy custom fields */
			if (isXMLDoc(xmlAdditionalData) and arrayLen(xmlAdditionalData.XmlRoot.XmlChildren)) {
				xmlAdditionalData.XmlRoot.XmlChildren.each(function(column, index) {
					var columnID = column.xmlattributes.columnID;
					var columnName = column.xmlAttributes.columnName;

					writeOutput("select @columnValue = null;");

					switch(column.xmlAttributes.displayTypeCode) {
						case "DATE": case "TEXTBOX":
							writeOutput("
								EXEC dbo.ams_setMemberData @memberID=@newMemberID, @orgID=@orgID, @columnName='#columnName#', @columnValue=@mcmerge_#getMergeFieldID(columnName=columnName, strFields=strFields)#, @recordedByMemberID=@performedByMemberID, @byPassQueue=1;
							");
							break; 
						case "RADIO": case "SELECT": case "CHECKBOX":
							writeOutput("
								set @columnValue = @mcmerge_#getMergeFieldID(columnName=columnName, strFields=strFields)#;
							");
						
							if (column.xmlattributes.allowMultiple EQ 1) {
								var colCheck = 'mdcv2.columnValueString';
								switch(column.xmlAttributes.displayTypeCode) {
									case "Integer":
										colCheck = 'mdcv2.columnValueInteger';
										break; 
									case "Decimal2":
										colCheck = 'mdcv2.columnValueDecimal2';
										break; 
									case "Date":
										colCheck = 'convert(varchar(10),mdcv2.columnValueDate,101)';
										break; 
									case "Bit":
										colCheck = "case when mdcv2.columnValueBit = 1 then '1' when mdcv2.columnValueBit = 0 then '0' else '' end";
										break; 
								}

								writeOutput("
									select @columnValue = STRING_AGG(valueID,',')
									from (
										select distinct mdcv2.valueID
										from dbo.ams_memberDataColumns mdc
										inner join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID 
										inner join dbo.fn_varcharListToTable(@columnValue,'|') as tmp on tmp.listItem = #colCheck#
										where mdc.columnID = #columnID#
										and mdc.orgID = @orgID
									) as tmp;
								");
							}
						
							writeOutput("EXEC dbo.ams_setMemberData @memberID=@newMemberID, @orgID=@orgID, @columnName='#columnName#', @columnValue=@columnValue, @recordedByMemberID=@performedByMemberID, @byPassQueue=1;");
							break; 
						case "DOCUMENT":
							writeOutput("
								select @columnValue = d.siteResourceID
								from dbo.ams_members as mco
								inner join dbo.ams_memberData as md on md.memberID = mco.memberID
								inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
								inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID and mdc.orgID = @orgID
								inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = 'DOCUMENTOBJ'
								inner join dbo.cms_documents as d on d.siteResourceID = mdcv.columnValueSiteResourceID
								inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
								inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
								where mco.orgID = @orgID
								and mco.memberID in (#allMemberIDs#)
								and dv.filename = @mcmerge_#getMergeFieldID(columnName=columnName, strFields=strFields)#;

								IF @columnValue IS NOT NULL
									EXEC dbo.ams_setMemberData @memberID=@newMemberID, @orgID=@orgID, @columnName='#columnName#', @columnValue=@columnValue, @recordedByMemberID=@performedByMemberID, @byPassQueue=1;
							");
							break; 
						case "TEXTAREA": case "HTMLCONTENT":
							writeOutput("
								EXEC dbo.cms_createContentField @siteID=@defaultSiteID, @isHTML=#column.xmlAttributes.displayTypeCode EQ 'HTMLCONTENT' ? '1': '0'#, @isActive=1, 
									@languageID=1, @contentTitle='', @contentDesc='', @rawContent=@mcmerge_#getMergeFieldID(columnName=columnName, strFields=strFields)#, 
									@contentID=@contentID OUTPUT, @contentSiteResourceID=@columnValueSiteResourceID OUTPUT;
								EXEC dbo.ams_setMemberData @memberID=@newMemberID, @orgID=@orgID, @columnName='#columnName#', @columnValue=@columnValueSiteResourceID, @recordedByMemberID=@performedByMemberID, @byPassQueue=1;
							");
							break; 
					 }
				});
			}

			writeOutput("
					-- copy all manually assigned groups
					-- cleanup remove from cache_members_groups
					INSERT INTO dbo.ams_memberGroups (orgID, memberID, groupID, addedByMemberID, dateAdded)
					select distinct @orgID, @newMemberID, mg.groupID, mg.addedByMemberID, mg.dateAdded
					from dbo.ams_memberGroups mg
					left outer join dbo.ams_memberGroups mg2 on mg2.orgID = @orgID
						and mg2.groupID = mg.groupID 
						and mg2.memberID = @newMemberID
					where mg.orgID = @orgID
					and mg.memberID in (#allMemberIDs#) 
					and mg2.memberGroupID is NULL;
			
					DELETE FROM platformQueue.dbo.queue_memberConditions WITH (READPAST)
					WHERE orgID = @orgID
					AND memberID in (#allMemberIDs#);

					DELETE FROM platformQueue.dbo.queue_memberGroups WITH (READPAST)
					WHERE orgID = @orgID
					AND memberID in (#allMemberIDs#);

					DELETE FROM dbo.cache_members_groups
					WHERE orgID = @orgID
					AND memberID in (#allMemberIDs#);

					DELETE FROM dbo.cache_members_conditions
					where memberID in (#allMemberIDs#);
				
					DELETE FROM platformQueue.dbo.queue_memberDelete WITH (READPAST)
					WHERE memberID in (#allMemberIDs#);

					DELETE FROM platformQueue.dbo.queue_memberGroupPrints WITH (READPAST)
					WHERE memberID in (#allMemberIDs#);

					DELETE FROM platformQueue.dbo.queue_memberMergeMatch WITH (READPAST)
					WHERE memberID in (#allMemberIDs#);

					DELETE FROM platformQueue.dbo.queue_memberPhotoThumb WITH (READPAST)
					WHERE memberID in (#allMemberIDs#);

					UPDATE dbo.ams_members
					SET groupPrintID = null
					where memberid in (#allMemberIDs#);
			
					/* Update the activeMemberID for the merging accounts. Update all previous merges as well. */
					UPDATE dbo.ams_members 
					SET activeMemberID = @newMemberID 
					WHERE memberid in (#allMemberIDs#)
					or activeMemberID in (#allMemberIDs#);

					/* update cached credit balances */
					delete from dbo.tr_creditBalances where orgID = @orgID and memberID in (#allMemberIDs#);
					EXEC dbo.tr_updateCreditBalanceByMember @memberID=@newMemberID;

					/* Flag all transactions for limits reprocessing */
					INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
					SELECT transactionID, ownedByOrgID, recordedOnSiteID
					FROM dbo.tr_transactions
					WHERE ownedByOrgID = @orgID
					AND assignedToMemberID in (#allMemberIDs#);

					/* copy all linked record connections */
					INSERT INTO dbo.ams_recordRelationships (orgID, recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive, startDate, endDate)
					select distinct @orgID, rtrt.recordTypeRelationshipTypeID, masterMember.memberID as masterMemberID, @newMemberID as childMemberID,
						rr.isActive, rr.startDate, rr.endDate
					from dbo.ams_members m
					inner join dbo.ams_recordRelationships rr on rr.orgID = @orgID
						and rr.childMemberID = m.memberID
						and rr.isActive = 1
					inner join dbo.ams_members masterMember on masterMember.orgID = @orgID
						and rr.masterMemberID = masterMember.memberID
						and masterMember.status in ('A','I')
					inner join dbo.ams_recordTypesRelationshipTypes rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
					inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
					where m.orgID = @orgID
					and m.memberID in (#allMemberIDs#)
						union
					select distinct @orgID, rtrt.recordTypeRelationshipTypeID, @newMemberID as masterMemberID, childMember.memberID as childMemberID,
						rr.isActive, rr.startDate, rr.endDate
					from dbo.ams_members m
					inner join dbo.ams_recordRelationships rr on rr.orgID = @orgID
						and rr.masterMemberID = m.memberID
						and rr.isActive = 1
					inner join dbo.ams_members childMember on childMember.orgID = @orgID 
						and rr.childMemberID = childMember.memberID
						and childMember.status in ('A','I')
					inner join dbo.ams_recordTypesRelationshipTypes rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
					inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
					where m.orgID = @orgID
					and m.memberID in (#allMemberIDs#);
				
					-- handle member network profiles
					IF EXISTS (SELECT TOP 1 mnpID FROM ##tmpMNP) BEGIN
						-- determining which member's mnp to be used for the new member if that selection is not passed in
						IF @mnpMemberID = 0 BEGIN
							SELECT TOP 1 @mnpMemberID = memberID
							FROM ##tmpMNP
							ORDER BY MFATOTPIdentity DESC, MFAPhoneNumber DESC, dateLastLogin DESC;
						END

						-- considering that multisite orgs may have multiple MNP entries for one member
						SELECT @mnpID = MIN(mnpID) FROM ##tmpMNP WHERE memberID = @mnpMemberID;
						WHILE @mnpID IS NOT NULL BEGIN
							SELECT @mnpSiteID = siteID, @profileID = profileID, @userName = userName, @passwordHash = PasswordHash, @passwordSalt = PasswordSalt,
								@isValidCredentials = isValidCredentials, @MFAPhoneNumber = MFAPhoneNumber, @MFATOTPIdentity = MFATOTPIdentity,
								@MFATOTPSecret = MFATOTPSecret, @MFATOTPSid = MFATOTPSid
							FROM ##tmpMNP
							WHERE mnpID = @mnpID;

							EXEC dbo.ams_createMemberNetworkProfile @memberID=@newMemberID, @profileID=@profileID, @siteID=@mnpSiteID, @status='A';

							SELECT @maxSA = max(dateSiteAgreement), @maxLogin = max(dateLastLogin)
							FROM ##tmpMNP
							WHERE siteID = @mnpSiteID;

							UPDATE dbo.ams_memberNetworkProfiles
							SET userName = @userName,
								PasswordHash = @passwordHash,
								passwordSalt = @PasswordSalt,
								MFAPhoneNumber = @MFAPhoneNumber,
								MFATOTPIdentity = @MFATOTPIdentity,
								MFATOTPSecret = @MFATOTPSecret,
								MFATOTPSid = @MFATOTPSid,
								isValidCredentials = @isValidCredentials,
								dateSiteAgreement = @maxSA,
								dateLastLogin = @maxLogin
							WHERE siteID = @mnpSiteID
							AND memberID = @newMemberID
							AND [status] = 'A';

							SELECT @mnpID = MIN(mnpID) FROM ##tmpMNP WHERE memberID = @mnpMemberID AND mnpID > @mnpID;
						END
					END

					IF OBJECT_ID('tempdb..##tmpMNP') IS NOT NULL 
						DROP TABLE ##tmpMNP;

					-- if any of the network profiles are now orphaned, go ahead and mark them as deleted
					UPDATE np
					SET status = 'D'
					FROM dbo.ams_networkProfiles as np
					INNER JOIN @tblDepoIDs as tbl on tbl.profileid = np.profileID
					LEFT OUTER JOIN dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID AND mnp.status = 'A'
					WHERE mnp.mnpID is null
					AND np.status <> 'D';

					-- del from membermergematch cache and process new member
					DELETE FROM dbo.cache_members_merge
					WHERE memberID in (#allMemberIDs#)
					OR matchMemberID in (#allMemberIDs#);

					-- correct group members history cache
					EXEC dbo.cache_correctGroupMemberHistory @orgID=@orgID, @activeMemberID=@newMemberID;

					DECLARE @xmlMessage xml;
					SELECT @xmlMessage = isnull((
						SELECT 'memberMergeMatchLoad' as t, cast(@newMemberID as varchar(10)) as m, cast(@orgID as varchar(10)) as o
						FOR XML RAW('mc'), TYPE
					),'<mc/>');
					EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

					IF EXISTS (SELECT 1 FROM ##tmpAuditLog) BEGIN
						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						SELECT ('{ ""c"":""auditLog"", ""d"": {
							""AUDITCODE"":""'+ auditCode +'"",
							""ORGID"":' + cast(@orgID as varchar(10)) + ',
							""SITEID"":' + cast(@siteID as varchar(10)) + ',
							""ACTORMEMBERID"":' + cast(@performedByMemberID as varchar(20)) + ',
							""ACTIONDATE"":""' + convert(varchar(20),GETDATE(),120) + '"",
							""MESSAGE"":""' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'""','\""') + '"" } }')
						FROM ##tmpAuditLog;
					END
				COMMIT TRAN;

				SELECT @newMemberID as newMemberID, @chosenMemberNumber as newMemberNumber, 1 as success
				
				-- trialsmith MCMemberIDTemp
				UPDATE trialsmith.dbo.depomemberdata
				set MCMemberIDTemp = @newMemberID
				where MCMemberIDTemp in (#allMemberIDs#);

				-- trialsmith merge queue
				INSERT INTO trialsmith.dbo.pendingMerges (mergeID, depomemberdataid)
				select distinct mergeid, depomemberdataid
				from @tblDepoIDs
				where mergeid in (
					select mergeid from @tblDepoIDs group by mergeid having count(depomemberdataid) > 1
				);

				-- update lyris external member id & logging merge
				DECLARE @message varchar(max);
			");
		
			cfloop (query="qryAllMembers") {
				if (strMember['membernumber'] NEQ qryAllMembers.membernumber) {
					writeOutput("
						EXEC lyris.trialslyris1.dbo.mc_changeExternalMemberID @orgcode=@orgcode, @oldMemberNumber='#qryAllMembers.membernumber#', @newMemberNumber=@chosenMemberNumber;

						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						SELECT ('{ ""c"":""historyEntries_SYS_ADMIN_MEMUPDATE"", ""d"": {
							""HISTORYCODE"":""SYS_ADMIN_MEMUPDATE"",
							""ORGID"":' + cast(@orgID as varchar(10)) + ',
							""SITEID"":' + cast(@siteID as varchar(10)) + ',
							""ACTORMEMBERID"":' + cast(@performedByMemberID as varchar(20)) + ',
							""RECEIVERMEMBERID"":' + cast(@newMemberID as varchar(20)) + ',
							""MAINMESSAGE"": ""Member Record Merged"",
							""SELFUPDATE"": false,
							""UPDATEDATE"": ""' + convert(varchar(20),GETDATE(),120) + '"",
							""MESSAGES"":[""' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@memNumChangeMsg_#qryAllMembers.memberID#),'""','\""') + '""] } }');
					");
				}
			}

			writeOutput("
					-- run the membermerge hook for each site in the org
					SET @dataXML = null;
					SELECT @dataXML = (
						SELECT @newMemberID AS newmid, @chosenMembernumber AS newmemnum, (
								SELECT memberID AS ""@mid"",  memberNumber AS ""@memnum""
								FROM @tblDeletedMembers
								WHERE memberNumber <> @chosenMembernumber
								FOR XML PATH ('member'), TYPE
							) AS merged
						FOR XML PATH ('data'));

					DECLARE @tblSites TABLE (memberAdminSiteResourceID int);
					INSERT INTO @tblSites (memberAdminSiteResourceID)
					SELECT memberAdminSiteResourceID
					FROM dbo.sites
					WHERE orgID = @orgID;

					SELECT @memberAdminSRID = MIN(memberAdminSiteResourceID) FROM @tblSites;
					WHILE @memberAdminSRID IS NOT NULL BEGIN
						INSERT INTO @tblHookListeners (executionType, objectPath)
						EXEC dbo.hooks_runHook @event='memberMerge', @siteResourceID=@memberAdminSRID, @dataXML=@dataXML;

						SELECT @memberAdminSRID = min(memberAdminSiteResourceID) FROM @tblSites WHERE memberAdminSiteResourceID > @memberAdminSRID;
					END

					-- process conditions for new member
					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					VALUES (@orgID, @newMemberID, null);

					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroups';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
						DROP TABLE ##tmpAuditLog;
					IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			");
		};

		var qryMerge = queryExecute(sqlMerge, sqlParams, { datasource="membercentral" } );
	
		if (qryMerge.success IS 1) {
			/* handle member photos */
			var qryMemberPhoto = queryExecute("
				select memberNumber
				from qryAllMembers
				where hasMemberPhoto = 1",
				{}, { dbtype="query", maxRows: 1 }
			);

			if (qryMemberPhoto.recordCount) {
				var memNumOfMemPhoto = lcase(qryMemberPhoto.memberNumber);
				var photoPath = "#RAIDUSERASSETROOT_PATH##lcase(arguments.orgcode)#/memberphotos/"
				var photoThPath = "#RAIDUSERASSETROOT_PATH##lcase(arguments.orgcode)#/memberphotosth/"

				/* 1. copy chosenPhotoRDO photo to merged fldr with _use suffix */
				if (FileExists(photoPath & memNumOfMemPhoto & ".jpg")) {
					try {
						var sourceFile = "#photoPath##memNumOfMemPhoto#.jpg";
						var destinationFile = "#photoPath#merged/#memNumOfMemPhoto#_use.jpg";
						fileCopy(sourceFile, destinationFile);
					} catch (any e) {
						var exdata = { sourceFile:sourceFile, destinationFile:destinationFile };
						cfmail(to=exceptionEmail, from="<EMAIL>", subject='[MemberCentralAPI] Unable to copy selected member photo during member merge', 
							type="text/html" mailerID="MemberCentral.com" priority="high" wrapText="72") {
							dump(var=e, label="exception", format="simple");
							dump(var=exdata, label="exdata", format="simple");
						}
					}
				}

				/* 2. move all member photos to merged fldr with _memberid suffix */
				cfloop (query="qryAllMembers") {
					if (FileExists(photoPath & lcase(qryAllMembers.membernumber) & ".jpg")) {
						try {
							var sourceFile = "#photoPath##lcase(qryAllMembers.membernumber)#.jpg";
							var destinationFile = "#photoPath#merged/#lcase(qryAllMembers.membernumber)#_#qryAllMembers.memberid#.jpg";
							fileCopy(sourceFile, destinationFile);
							fileDelete(sourceFile);

							if (FileExists("#photoThPath##lcase(qryAllMembers.membernumber)#.jpg")) {
								fileDelete("#photoThPath##lcase(qryAllMembers.membernumber)#.jpg");
							}

							storedproc procedure="ams_removeMemberPhoto" datasource="membercentral" {
								procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgID#";
								procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#qryAllMembers.memberid#";
							};

						} catch (any e) {
							var exdata = { sourceFile:sourceFile, destinationFile:destinationFile };
							cfmail(to=exceptionEmail, from="<EMAIL>", subject='[MemberCentralAPI] Unable to move all member photos to merged folder during member merge', 
								type="text/html" mailerID="MemberCentral.com" priority="high" wrapText="72") {
								dump(var=e, label="exception", format="simple");
								dump(var=exdata, label="exdata", format="simple");
							}
						}
					}
				}

				/* 3. move the _use photo back */
				if (FileExists(photoPath & "merged/" & memNumOfMemPhoto & "_use.jpg")) {
					try {
						var sourceFile = "#photoPath#merged/#memNumOfMemPhoto#_use.jpg";
						var destinationFile = "#photoPath##lcase(qryMerge.newMemberNumber)#.jpg";
						fileCopy(sourceFile, destinationFile);
						fileDelete(sourceFile);

						storedproc procedure="ams_saveMemberPhoto" datasource="membercentral" {
							procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgID#";
							procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#qryMerge.newMemberID#";
						};

					} catch (any e) {
						var exdata = { sourceFile:sourceFile, destinationFile:destinationFile };
						cfmail(to=exceptionEmail, from="<EMAIL>", subject='[MemberCentralAPI] Unable to move selected member photo back during member merge', 
							type="text/html" mailerID="MemberCentral.com" priority="high" wrapText="72") {
							dump(var=e, label="exception", format="simple");
							dump(var=exdata, label="exdata", format="simple");
						}
					}
				}
			}

			strResult.report = {};
			if (qryNewManualGroups.recordcount) {
				strResult.report.groups = { summary:"The new member account will have #qryNewManualGroups.recordcount# manual group assignments.", assignedgroups:[] };
				cfloop (query="qryNewManualGroups") {
					arrayAppend(strResult.report.groups.assignedgroups,qryNewManualGroups.groupPath);
				}
			}
			if (qryNewDupeRegistrations.recordcount) {
				strResult.report.events = { summary:"The new member account will have #qryNewDupeRegistrations.recordcount# duplicate event registrations.", events:[] };
				cfloop (query="qryNewDupeRegistrations") {
					arrayAppend(strResult.report.events.events,"#dateformat(qryNewDupeRegistrations.startTime,'mm/dd/yy')# - #qryNewDupeRegistrations.contentTitle#");
				}
			}

			strResult.newMemberNumber = qryMerge.newMemberNumber;
			strResult.ignoredfields = arrPayloadIgnored;
			strResult.status = "ok";
		} else {
			strResult.status = "error";
		}

		return strResult;
	}

	private query function getAllMergeMembers (required numeric memberid, required numeric orgid, required string mergeMemberIDs) {
		// master account
		var qSelect = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT top 1 m.memberID, m.LastName, case when o.hasMiddleName = 1 then m.middlename else '' end as middlename, m.FirstName, 
				case when o.hasPrefix = 1 then m.prefix else '' end as prefix, 
				case when o.hasSuffix = 1 then m.suffix else '' end as suffix, 
				case when o.hasProfessionalSuffix = 1 then m.professionalSuffix else '' end as professionalSuffix, 
				m.Company, m.memberNumber, m.memberTypeID, mt.memberType, m.status, m.isProtected,
				case m.status when 'I' then 'Inactive' when 'A' then 'Active' else 'Deleted' end as statusFull, m.hasMemberPhoto,
				rt.recordTypeID, rt.recordTypeName, rt.recordTypeCode, rt.isPerson, rt.isOrganization, convert(varchar, m.earliestDateCreated , 1) as createddate
			FROM dbo.ams_members as m 
			INNER JOIN dbo.organizations as o on o.orgID = m.orgID
			INNER JOIN dbo.ams_memberTypes as mt on mt.membertypeid = m.membertypeID
			INNER JOIN dbo.ams_recordTypes as rt on rt.recordTypeID = m.recordTypeID
			WHERE m.memberID = :memberid
			AND m.orgID = :orgid
			AND m.memberid = m.activeMemberID
			AND m.status <> 'D';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;", 
			{ 
				memberid = { value=arguments.memberid, cfsqltype="CF_SQL_INTEGER" },
				orgid = { value=arguments.orgid, cfsqltype="CF_SQL_INTEGER" }
			},
			{ datasource="membercentral" }
		);

		var qSelectMerged = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT m.memberID, m.LastName, case when o.hasMiddleName = 1 then m.middlename else '' end as middlename, m.FirstName, 
				case when o.hasPrefix = 1 then m.prefix else '' end as prefix, 
				case when o.hasSuffix = 1 then m.suffix else '' end as suffix, 
				case when o.hasProfessionalSuffix = 1 then m.professionalSuffix else '' end as professionalSuffix, 
				m.Company, m.memberNumber, m.memberTypeID, mt.memberType, m.status, m.isProtected,
				case m.status when 'I' then 'Inactive' when 'A' then 'Active' else 'Deleted' end as statusFull, m.hasMemberPhoto,
				rt.recordTypeID, rt.recordTypeName, rt.recordTypeCode, rt.isPerson, rt.isOrganization,convert(varchar, m.earliestDateCreated , 1) as createddate
			FROM dbo.ams_members m 
			INNER JOIN dbo.ams_memberTypes as mt on mt.membertypeid = m.membertypeID
			INNER JOIN dbo.organizations as o on o.orgID = m.orgID
			left outer join dbo.ams_recordTypes rt on rt.recordTypeID = m.recordTypeID
			WHERE m.memberID in (:mergeMemberIDs) 
			AND m.memberID <> :memberid
			AND m.orgID = :orgid
			AND m.status <> 'D';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;", 
			{ 
				memberid = { value=arguments.memberid, cfsqltype="CF_SQL_INTEGER" },
				mergeMemberIDs = { value=arguments.mergeMemberIDs, cfsqltype="CF_SQL_INTEGER", list="Yes" },
				orgid = { value=arguments.orgid, cfsqltype="CF_SQL_INTEGER" }
			},
			{ datasource="membercentral" }
		);

		// get all accounts together
		var qAllAccounts = queryExecute("
			select memberID, LastName, middlename, FirstName, prefix, suffix, professionalSuffix, company, memberNumber, 
				memberTypeID, memberType, status, statusFull, hasMemberPhoto, recordTypeID, recordTypeName, recordTypeCode, isPerson, 
				isProtected, isOrganization, createddate, 1 as IsMaster
			from qSelect
				union
			select memberID, LastName, middlename, FirstName, prefix, suffix, professionalSuffix, company, memberNumber, 
				memberTypeID, memberType, status, statusFull, hasMemberPhoto, recordTypeID, recordTypeName, recordTypeCode, isPerson, 
				isProtected, isOrganization, createddate, 2 as IsMaster
			from qSelectMerged
			order by isMaster, memberID",
			{}, { dbtype="query" } 
		);

		return qAllAccounts;
	}

	private query function getOrgMemberFields(required numeric orgid) {
		var qryOrgMemberFields = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select hasPrefix, hasMiddleName, hasSuffix, hasProfessionalSuffix
			from dbo.organizations
			where orgID = :orgID;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;", 
			{ orgID = { value=arguments.orgid, cfsqltype="CF_SQL_INTEGER" } },
			{ datasource="membercentral" }
		);

		return qryOrgMemberFields;
	}

	private query function getOrgAddressTypes(required numeric orgid) {
		var qryOrgAddressTypes = '';

		storedproc procedure="ams_getOrgAddressTypes" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procparam cfsqltype="CF_SQL_BIT" type="in" value="0";
			procresult variable="qryOrgAddressTypes";
		};

		return qryOrgAddressTypes;
	}

	private query function getOrgAddressTagTypes(required numeric orgid) {
		var qryOrgAddressTagTypes = '';

		storedproc procedure="ams_getOrgAddressTagTypes" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procresult variable="qryOrgAddressTagTypes";
		};

		return qryOrgAddressTagTypes;
	}

	private query function getOrgPhoneTypes(required numeric orgid) {
		var qryOrgPhoneTypes = '';

		storedproc procedure="ams_getOrgPhoneTypes" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procresult variable="qryOrgPhoneTypes";
		};

		return qryOrgPhoneTypes;
	}

	private query function getOrgDistrictTypes(required numeric orgid) {
		var qryOrgDistrictTypes = '';

		storedproc procedure="ams_getOrgDistrictTypes" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procresult variable="qryOrgDistrictTypes";
		};

		return qryOrgDistrictTypes;
	}
	
	private query function getOrgEmailTypes(required numeric orgid) {
		var qryOrgEmailTypes = '';

		storedproc procedure="ams_getOrgEmailTypes" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="0";
			procresult variable="qryOrgEmailTypes";
		};

		return qryOrgEmailTypes;
	}

	private query function getOrgEmailTagTypes(required numeric orgid) {
		var qryOrgEmailTagTypes = '';

		storedproc procedure="ams_getOrgEmailTagTypes" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procresult variable="qryOrgEmailTagTypes";
		};

		return qryOrgEmailTagTypes;
	}

	private query function getOrgWebsiteTypes(required numeric orgid) {
		var qryOrgWebsiteTypes = '';

		storedproc procedure="ams_getOrgWebsiteTypes" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procresult variable="qryOrgWebsiteTypes";
		};

		return qryOrgWebsiteTypes;
	}
	
	private query function getOrgProfessionalLicenseTypes(required numeric orgid) {
		var qryOrgProfessionalLicenseTypes = '';

		storedproc procedure="ams_getOrgProfessionalLicenseTypes" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procresult variable="qryOrgProfessionalLicenseTypes";
		};

		return qryOrgProfessionalLicenseTypes;
	}

	private xml function getOrgXMLAdditionalData(required numeric orgid) {
		var xmlDataColumns;

		storedproc procedure="ams_getOrgMemberDataColumns" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgid#";
			procresult variable="xmlDataColumns";
		};

		return XMLParse(xmlDataColumns.additionalDataXML);
	}

	private query function getManualGroupsAllMembers(required numeric orgid, required string allMemberIDs) {
		var qryNewManualGroups = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = :orgID;
			
			select distinct g.groupID, g.groupPathExpanded as groupPath
			from dbo.ams_memberGroups as mg
			inner join dbo.ams_groups as g on g.orgID = @orgID
				and g.groupID = mg.groupID
				and g.status = 'A'
				and g.hideOnGroupLists = 0
			where mg.orgID = @orgID
			and mg.memberID in (:allMemberIDs);
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;", 
			{ 
				orgID = { value=arguments.orgID, cfsqltype="cf_sql_integer" },
				allMemberIDs = { value=arguments.allMemberIDs, cfsqltype="CF_SQL_INTEGER", list="yes" }
			},
			{ datasource="membercentral" }
		);

		return qryNewManualGroups;
	}

	private query function getDupeEventRegAllMembers(required string allMemberIDs) {
		var qryNewDupeRegistrations = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select e.eventid, et.startTime, ec.contentTitle
			from dbo.ev_registrants as r
			inner join dbo.ams_members as m on m.memberID = r.memberid
			inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID
				and r.status = 'A'
				and reg.status = 'A'
			inner join dbo.ev_events as e on e.eventID = reg.eventID
				and e.status = 'A' and reg.siteID = e.siteID
			inner join dbo.sites as s on s.siteID = e.siteID
			inner join dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = s.defaultTimeZoneID
			inner join dbo.cms_contentLanguages as ec on ec.contentID = e.eventContentID and ec.languageID = 1
			where m.activeMemberID in (:allMemberIDs)
			group by e.eventid, et.startTime, ec.contentTitle
			having count(r.memberID) > 1
			order by 2, 3;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;", 
			{ 
				allMemberIDs = { value=arguments.allMemberIDs, cfsqltype="CF_SQL_INTEGER", list="yes" }
			},
			{ datasource="membercentral" }
		);
		
		return qryNewDupeRegistrations;
	}

	private query function getOrgDocColumns(required numeric orgID) {
		var qryDocumentColumns = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select columnName, 'varchar' as columnDataType, mdc.allowMultiple, 'Custom Fields' as area
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			where mdc.orgID = :orgID
			and dt.dataTypeCode = 'DOCUMENTOBJ';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ orgID = { value=arguments.orgID, cfsqltype="cf_sql_integer" } },
			{ datasource="membercentral" }
		);
		
		return qryDocumentColumns;
	}

	private numeric function getMergeFieldID(required string columnName, required struct strFields) {
		return strFields[columnName].colID;
	}

}