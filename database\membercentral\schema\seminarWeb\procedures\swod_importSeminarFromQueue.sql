ALTER PROC dbo.swod_importSeminarFromQueue
@itemUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @siteCode varchar(10), @orgID int, @recordedByMemberID int, @ovAction char(1), @itemGroupUID uniqueidentifier,
		@itemStatus int, @queueTypeID int, @statusReady int, @existingSeminar bit, @nowDate datetime, @minAutoID int, @objectiveID int,
		@categoryID int, @formID int, @MCSeminarID int, @MCLayoutID int, @CompleteTime int, @programCode varchar(15), @seminarTitle varchar(250),
		@seminarSubtitle varchar(250), @objective varchar(max), @MCCategoryIDList varchar(max), @MCFormIDList varchar(max), @origPublished datetime,
		@startSale datetime, @endSale datetime, @featured bit, @certificate bit, @sellInCatalog bit, @credit bit, @autoCreateTitle bit,
		@description varchar(max), @endofSeminartext varchar(max), @isPublished bit, @dateActivated date, @offerQA bit,
		@blankOnInactivity bit, @preReqSeminarID int, @lockSettings bit, @preventSeminarFees int,
		@isPriceBasedOnActual bit, @freeRateDisplay varchar(5), @revenueGLAccountID int, @titleID int, 
		@videoFileID int, @participantID int, @columnID int, @swStdEvalFormID int, @seminarFormID int, @creditStatusID int,
		@crlf varchar(10), @msgjson varchar(500) = '', @overwriteProgramData bit, @RateName varchar(max), @RateAmount decimal(9,2), @rateID int, 
		@isHidden bit, @siteResourceID int, @IntroText varchar(max), @isFeatured BIT, @RegistrationText varchar(max), @includeConnectionInstructionCustomText BIT;;

	-- if itemUID is not readyToProcess, kick out now
	SELECT @statusReady = qs.queueStatusID, @queueTypeID = qt.queueTypeID
	FROM platformQueue.dbo.tblQueueStatuses as qs
	INNER JOIN platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	WHERE qt.queueType = 'importSWODPrograms'
	AND qs.queueStatus = 'ReadyToProcess';

	SELECT @itemStatus = queueStatusID
	FROM platformQueue.dbo.tblQueueItems
	WHERE itemUID = @itemUID;

	IF @itemStatus IS NULL OR @itemStatus <> @statusReady
		RETURN 0;

	-- update status
	EXEC platformQueue.dbo.queue_setStatus @queueType='importSWODPrograms', @itemUID=@itemUID, @queueStatus='Processing';

	IF OBJECT_ID('tempdb..#tmpSWODQueueData') IS NOT NULL
		DROP TABLE #tmpSWODQueueData;
	IF OBJECT_ID('tempdb..#tmpSWODImpObjectives') IS NOT NULL
		DROP TABLE #tmpSWODImpObjectives;
	IF OBJECT_ID('tempdb..#tmpSWODImpCategories') IS NOT NULL
		DROP TABLE #tmpSWODImpCategories;
	IF OBJECT_ID('tempdb..#tmpSWODImpLogs') IS NOT NULL
		DROP TABLE #tmpSWODImpLogs;

	CREATE TABLE #tmpSWODImpObjectives(autoID int identity(1,1), objective varchar(max));
	CREATE TABLE #tmpSWODImpCategories(autoID int identity(1,1), categoryID int);
	CREATE TABLE #tmpSWODImpLogs (autoID int identity(1,1), msg varchar(MAX));
	CREATE TABLE #tmpSWODImpEvaluation(autoID int identity(1,1), formID int);

	SELECT qid.columnID, dc.columnname, qid.columnValueString, qid.columnValueDecimal2, qid.columnValueInteger, qid.columnvalueDate,
		qid.columnValueBit, qid.columnValueText
	INTO #tmpSWODQueueData
	FROM platformQueue.dbo.tblQueueItemData as qid
	INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
	WHERE qid.itemUID = @itemUID;

	SELECT TOP 1 @recordedByMemberID=recordedByMemberID, @siteID=siteID
	FROM platformQueue.dbo.tblQueueItemData
	WHERE itemUID = @itemUID;

	SET @nowDate = GETDATE();
	SET @crlf = char(13) + char(10);
	SELECT @siteCode = siteCode, @orgID = orgID FROM membercentral.dbo.sites WHERE siteID = @siteID;
	SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@siteCode);

	SELECT @MCSeminarID = columnValueInteger FROM #tmpSWODQueueData WHERE columnname = 'MCSeminarID';
	SELECT @MCLayoutID = columnValueInteger FROM #tmpSWODQueueData WHERE columnname = 'MCLayoutID';
	SELECT @completeTime = columnValueInteger FROM #tmpSWODQueueData WHERE columnname = 'CompleteTime';
	SELECT @ovAction = columnValueString FROM #tmpSWODQueueData WHERE columnname = 'ovAction';
	SELECT @programCode = columnValueString FROM #tmpSWODQueueData WHERE columnname = 'ProgramCode';
	SELECT @seminarTitle = columnValueString FROM #tmpSWODQueueData WHERE columnname = 'SeminarTitle';
	SELECT @seminarSubtitle = columnValueString FROM #tmpSWODQueueData WHERE columnname = 'SeminarSubtitle';
	SELECT @origPublished = columnvalueDate FROM #tmpSWODQueueData WHERE columnname = 'OrigPublished';
	SELECT @featured = columnValueBit FROM #tmpSWODQueueData WHERE columnname = 'Featured';
	SELECT @offerQA = columnValueBit FROM #tmpSWODQueueData WHERE columnname = 'PlayerQATab';
	SELECT @blankOnInactivity = columnValueBit FROM #tmpSWODQueueData WHERE columnname = 'BlankPlayer';
	SELECT @certificate = columnValueBit FROM #tmpSWODQueueData WHERE columnname = 'Certificate';
	SELECT @sellInCatalog = columnValueBit FROM #tmpSWODQueueData WHERE columnname = 'SellInCatalog';
	SELECT @credit = columnValueBit FROM #tmpSWODQueueData WHERE columnname = 'Credit';
	SELECT @autoCreateTitle = columnValueBit FROM #tmpSWODQueueData WHERE columnname = 'AutoCreateTitle';
	SELECT @description = columnValueText FROM #tmpSWODQueueData WHERE columnname = 'Description';
	SELECT @MCCategoryIDList = ISNULL(columnValueText,'') FROM #tmpSWODQueueData WHERE columnname = 'MCCategoryIDList';
	SELECT @MCFormIDList = ISNULL(columnValueText,'') FROM #tmpSWODQueueData WHERE columnname = 'MCFormIDList';
	SELECT @endOfSeminarText = columnValueText FROM #tmpSWODQueueData WHERE columnname = 'CompletionText';
	SELECT @IntroText = columnValueText FROM #tmpSWODQueueData WHERE columnname = 'IntroductoryText';
	SELECT @RegistrationText = columnValueText FROM #tmpSWODQueueData WHERE columnname = 'RegistrationText';
	IF LEN(ISNULL(@RegistrationText, '')) > 0
	    SET @includeConnectionInstructionCustomText = 1;
	ELSE
	    SET @includeConnectionInstructionCustomText = 0;

	IF @sellInCatalog = 1 BEGIN
		SELECT @startSale = columnvalueDate FROM #tmpSWODQueueData WHERE columnname = 'StartSale';
		SELECT @endSale = columnvalueDate FROM #tmpSWODQueueData WHERE columnname = 'EndSale';
	END

	SET @overwriteProgramData = 0;
	IF @MCSeminarID IS NOT NULL BEGIN
		SET @existingSeminar = 1;
		SELECT @lockSettings=lockSettings FROM dbo.tblSeminars WHERE seminarID = @MCSeminarID;
		IF (@ovAction = 'o' AND @lockSettings = 0)
			SET @overwriteProgramData = 1;
	END
	ELSE BEGIN
		SET @existingSeminar = 0;
	END

	BEGIN TRAN;
		-- updating seminar with overwrite setting
		IF @overwriteProgramData = 1 BEGIN
			SELECT @isPublished=s.isPublished, @dateActivated=swod.dateActivated, @preventSeminarFees=s.preventSeminarFees,
				@isPriceBasedOnActual = isPriceBasedOnActual, @revenueGLAccountID = NULLIF(revenueGLAccountID,0), @freeRateDisplay = freeRateDisplay
			FROM dbo.tblSeminarsSWOD AS swod
			INNER JOIN dbo.tblSeminars AS s ON s.seminarID = swod.seminarID
			WHERE s.seminarID = @MCSeminarID;

			EXEC dbo.swod_updateSeminar @seminarID=@MCSeminarID, @sitecode=@siteCode, @seminarName=@seminarTitle, @seminarSubTitle=@seminarSubTitle,
				@programCode=@programCode, @seminarDesc=@description, @isPublished=@isPublished,
				@dateActivated=@dateActivated, @dateorigPublished=@origPublished, @layoutID=@MCLayoutID,
				@seminarLength=@completeTime, @lockSettings=@lockSettings, @recordedByMemberID=@recordedByMemberID;

			IF @preventSeminarFees = 1 AND EXISTS (
				SELECT 1 AS priceCount
				FROM dbo.tblSeminarsAndRates AS r 
				INNER JOIN memberCentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
					AND sr.siteResourceStatusID = 1
				WHERE r.seminarID = @MCSeminarID
			)
				SET @preventSeminarFees = 0;

			UPDATE dbo.tblSeminars
			SET preventSeminarFees = @preventSeminarFees
			WHERE seminarID = @MCSeminarID;;

			EXEC dbo.swod_updateSeminarSettings @seminarID=@MCSeminarID, @sitecode=@siteCode, @introMessageText=@IntroText, @endofSeminartext=@endofSeminartext, @isPublisher=1,
				@offerQA=@offerQA, @blankOnInactivity=@blankOnInactivity, @offerCertificate=@certificate, @preReqSeminarID=0, @includeConnectionInstructionCustomText = @includeConnectionInstructionCustomText,
							@customText = @RegistrationText, @recordedByMemberID=@recordedByMemberID;

			SELECT @isFeatured = CASE WHEN f.featuredID IS NOT NULL THEN 1 ELSE 0 END 
			FROM dbo.tblSeminars s  
			INNER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = s.seminarID  
			LEFT OUTER JOIN dbo.tblFeaturedPrograms AS f ON f.participantID = @participantID AND f.seminarID = s.seminarID  
			LEFT JOIN dbo.tblSeminarsOptIn AS optIn ON optIn.seminarID = s.seminarID AND optIn.participantID = @participantID
			WHERE s.seminarID = @MCSeminarID;

			IF @isFeatured <> @featured
			BEGIN
				EXEC dbo.sw_toggleFeaturedProgram @orgCode = @siteCode, @programType = 'SWOD', @programID = @MCSeminarID, @isFeatured = @featured, @recordedByMemberID = @recordedByMemberID;
			END;
			
			EXEC dbo.sw_updateSeminarPricing @seminarID=@MCSeminarID, @allowCatalog=@sellInCatalog, @isPriceBasedOnActual=@isPriceBasedOnActual,
				@dateCatalogStart=@startSale, @dateCatalogEnd=@endSale, @freeRateDisplay=@freeRateDisplay, @revenueGLAccountID=@revenueGLAccountID,
				@recordedByMemberID=@recordedByMemberID;

			-- on program update, turn off these flags if any entries are already there
			IF EXISTS(SELECT 1 FROM dbo.tblSeminarsAndCredit WHERE seminarID = @MCSeminarID)
				SET @credit = 0;

			IF EXISTS(SELECT 1 FROM dbo.tblSeminarsAndTitles WHERE seminarID = @MCSeminarID)
				SET @autoCreateTitle = 0;

			INSERT INTO #tmpSWODImpLogs(msg)
			VALUES('SWOD' + '-' + CAST(@MCSeminarID AS VARCHAR(10))	+ ' has been updated via SWOD Programs Import.');
		END

		-- adding new seminar
		IF @existingSeminar = 0 BEGIN
			EXEC dbo.swod_createSeminar @orgcode=@siteCode, @seminarName=@seminarTitle, @seminarSubTitle=@seminarSubTitle,
				@freeRateDisplay='$0.00', @recordedByMemberID=@recordedByMemberID, @introMessageText=@IntroText, @dateorigPublished=@origPublished, @seminarID=@MCSeminarID OUTPUT;

			IF ISNULL(@programCode,'') = '' BEGIN
				SELECT @programCode = programCode
				FROM dbo.tblSeminars
				WHERE seminarID = @MCSeminarID;

				-- for the use of import report
				SELECT @columnID = columnID
				FROM platformQueue.dbo.tblQueueTypeDataColumns
				WHERE queueTypeID = @queueTypeID
				AND columnName = 'ProgramCode';

				MERGE platformQueue.dbo.tblQueueItemData AS t
				USING (
					SELECT TOP 1 qid.itemGroupUID, qi.itemUID, qid.recordedByMemberID, qid.siteID, qid.dataKey
					FROM platformQueue.dbo.tblQueueItems as qi
					INNER JOIN platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
					WHERE qi.itemUID = @itemUID
				) AS s
				ON s.itemGroupUID = t.itemGroupUID
					AND s.itemUID = t.itemUID
					AND t.columnID = @columnID
				WHEN NOT MATCHED BY TARGET THEN
					INSERT (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString) 
					VALUES (s.itemGroupUID, s.itemUID, s.recordedByMemberID, s.siteID, @columnID, s.dataKey, @programCode)
				WHEN MATCHED THEN UPDATE SET
					t.columnValueString = @programCode;
			END
						
			EXEC dbo.swod_updateSeminar @seminarID=@MCSeminarID, @sitecode=@siteCode, @seminarName=@seminarTitle, @seminarSubTitle=@seminarSubTitle,
				@programCode=@programCode, @seminarDesc=@description, @isPublished=0, 
				@dateActivated=@nowDate, @dateorigPublished=@origPublished, @layoutID=@MCLayoutID,
				@seminarLength=@completeTime, @lockSettings=0, @recordedByMemberID=@recordedByMemberID;

			UPDATE dbo.tblSeminars
			SET preventSeminarFees = 0
			WHERE seminarID = @MCSeminarID;

			EXEC dbo.swod_updateSeminarSettings @seminarID=@MCSeminarID, @sitecode=@siteCode, @introMessageText=@IntroText, @endofSeminartext=@endofSeminartext, @isPublisher=1,
				@offerQA=@offerQA, @blankOnInactivity=@blankOnInactivity, @offerCertificate=@certificate, @preReqSeminarID=0,@includeConnectionInstructionCustomText = @includeConnectionInstructionCustomText,
							@customText = @RegistrationText, @recordedByMemberID=@recordedByMemberID;

			SELECT @isFeatured = CASE WHEN f.featuredID IS NOT NULL THEN 1 ELSE 0 END 
			FROM dbo.tblSeminars s  
			INNER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = s.seminarID  
			LEFT OUTER JOIN dbo.tblFeaturedPrograms AS f ON f.participantID = @participantID AND f.seminarID = s.seminarID  
			LEFT JOIN dbo.tblSeminarsOptIn AS optIn ON optIn.seminarID = s.seminarID AND optIn.participantID = @participantID
			WHERE s.seminarID = @MCSeminarID;

			IF @isFeatured <> @featured
			BEGIN
				EXEC dbo.sw_toggleFeaturedProgram @orgCode = @siteCode, @programType = 'SWOD', @programID = @MCSeminarID, @isFeatured = @featured, @recordedByMemberID = @recordedByMemberID;
			END;

			EXEC dbo.sw_updateSeminarPricing @seminarID=@MCSeminarID, @allowCatalog=@sellInCatalog, @isPriceBasedOnActual=0,
				@dateCatalogStart=@startSale, @dateCatalogEnd=@endSale, @freeRateDisplay='$0.00', @revenueGLAccountID=NULL,
				@recordedByMemberID=@recordedByMemberID;
			
			INSERT INTO #tmpSWODImpLogs(msg)
			VALUES('SWOD' + '-' + CAST(@MCSeminarID AS VARCHAR(10))	+ ' has been created via SWOD Programs Import.');
		END

		IF (@existingSeminar = 0 OR @overwriteProgramData = 1) BEGIN
			-- adding objectives
			INSERT INTO #tmpSWODImpObjectives(objective)
			SELECT columnValueText
			FROM #tmpSWODQueueData
			WHERE columnname IN ('Objective1','Objective2','Objective3','Objective4','Objective5')
			AND LEN(ISNULL(columnValueText,'')) > 0;

			IF @existingSeminar = 1
				AND EXISTS(SELECT 1 FROM #tmpSWODImpObjectives)
				AND EXISTS(SELECT 1 FROM dbo.tblLearningObjectives WHERE seminarID = @MCSeminarID) BEGIN
				DELETE FROM dbo.tblLearningObjectives WHERE seminarID = @MCSeminarID;
				INSERT INTO #tmpSWODImpLogs(msg) VALUES('All previous learning objectives were removed from the seminar.');
			END

			SET @minAutoID = NULL;
			SELECT @minAutoID = MIN(autoID) FROM #tmpSWODImpObjectives;
			WHILE @minAutoID IS NOT NULL BEGIN
				SELECT @objective = objective FROM #tmpSWODImpObjectives WHERE autoID = @minAutoID;

				EXEC dbo.sw_createLearningObjective @siteCode=@siteCode, @objective=@objective, @programType='SWOD',
					@programID=@MCSeminarID, @recordedByMemberID=@recordedByMemberID, @objectiveID=@objectiveID OUTPUT;

				SELECT @minAutoID = MIN(autoID) FROM #tmpSWODImpObjectives WHERE autoID > @minAutoID;
			END

			IF @sellInCatalog = 1 
			BEGIN
				DECLARE @index int = 1;
				WHILE @index <=5 
				BEGIN

					SELECT @RateName = NULL, @RateAmount = NULL, @rateID = NULL, @isHidden = NULL, @revenueGLAccountID = NULL;

					SELECT @RateName = columnValueText FROM #tmpSWODQueueData WHERE columnname = 'RateName'+ CAST(@index AS VARCHAR(10));
					SELECT @RateAmount = columnValueDecimal2 FROM #tmpSWODQueueData WHERE columnname = 'RateAmount'+ CAST(@index AS VARCHAR(10));	

					IF (@RateName IS NOT NULL AND @RateAmount IS NOT NULL) 
					BEGIN
						SELECT @rateID = rateID, @isHidden = isHidden, @revenueGLAccountID = revenueGLAccountID
						FROM dbo.tblSeminarsAndRates 
						WHERE ratename = @RateName
						AND participantID = @participantID
						AND seminarID = @MCSeminarID 
						AND rateGroupingID IS NULL;
		
						IF(@rateID IS NOT NULL) 
						BEGIN
							EXEC dbo.sw_updateSeminarRate @participantID=@participantID, @rateID=@rateID, @rateGroupingID=NULL,
								@rateName=@rateName, @rate=@rateAmount, @isHidden=@isHidden, @revenueGLAccountID=@revenueGLAccountID, 
								@recordedByMemberID=@recordedByMemberID;
		
						END
						ELSE 
						BEGIN
							EXEC dbo.sw_createSeminarRate @participantID=@participantID, @seminarID=@MCSeminarID, @rateGroupingID=NULL, @rateName=@rateName,
								@rate=@RateAmount, @isHidden=0, @revenueGLAccountID=NULL, @recordedByMemberID=@recordedByMemberID, @rateID=@rateID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;
						END
					END
					SET @index = @index + 1;
				END
			END

			-- adding credits
			IF @credit = 1  BEGIN
				SELECT @creditStatusID = dbo.fn_getStatusIDFromStatus('Not Submitted');

				INSERT INTO dbo.tblSeminarsAndCredit (seminarID, CSALinkID, statusID, courseApproval, wddxcreditsAvailable,
					creditOfferedStartDate, creditOfferedEndDate, creditCompleteByDate)
				SELECT @MCSeminarID, csa.CSALinkID, @creditStatusID, '', '', NULL, NULL, NULL
				FROM dbo.tblCreditSponsorsAndAuthorities AS csa 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID
				WHERE cs.orgCode = @siteCode;

				IF @@ROWCOUNT > 0
					INSERT INTO #tmpSWODImpLogs(msg) VALUES('All credit authorities assigned to the site is added to the seminar.');
			END

			-- adding subjects
			INSERT INTO #tmpSWODImpCategories(categoryID)
			SELECT c.categoryID
			FROM dbo.tblCategories AS c 
			INNER JOIN dbo.tblParticipants AS p ON c.participantID = p.participantID
				AND p.participantID = @participantID
			INNER JOIN membercentral.dbo.fn_intListToTable(@MCCategoryIDList,'|') tmp ON tmp.listitem = c.categoryID
				AND tmp.listItem <> ''
			ORDER BY c.categoryName;

			IF @existingSeminar = 1
				AND EXISTS(SELECT 1 FROM #tmpSWODImpCategories)
				AND EXISTS(SELECT 1 FROM dbo.tblSeminarsAndCategories WHERE seminarID = @MCSeminarID) BEGIN
				DELETE FROM dbo.tblSeminarsAndCategories WHERE seminarID = @MCSeminarID;
				INSERT INTO #tmpSWODImpLogs(msg) VALUES('All previous subject areas were removed from the seminar.');
			END

			SELECT @minAutoID = NULL, @categoryID = NULL;
			SELECT @minAutoID = MIN(autoID) FROM #tmpSWODImpCategories;
			WHILE @minAutoID IS NOT NULL BEGIN
				SELECT @categoryID = categoryID FROM #tmpSWODImpCategories WHERE autoID = @minAutoID;

				EXEC sw_addSeminarAndCategory @seminarID=@MCSeminarID, @categoryID=@categoryID,@recordedByMemberID=@recordedByMemberID;

				SELECT @minAutoID = MIN(autoID) FROM #tmpSWODImpCategories WHERE autoID > @minAutoID;
			END

			-- auto-add title
			IF @autoCreateTitle = 1 BEGIN
				EXEC dbo.sw_addTitle @orgCode=@siteCode, @seminarID=@MCSeminarID, @titleName=@seminarTitle, @recordedByMemberID=@recordedByMemberID, @titleID=@titleID OUTPUT;
			END
			
			-- adding evaluations
			INSERT INTO #tmpSWODImpEvaluation(formID)
			SELECT f.formID
			FROM formbuilder.dbo.tblForms as f
			INNER JOIN formbuilder.dbo.tblFormTypes as ft on ft.formTypeID = f.formTypeID AND ft.formTypeAbbr = 'S' AND f.isDeleted = 0 AND f.siteID = @siteID
			INNER JOIN membercentral.dbo.fn_intListToTable(@MCFormIDList,'|') tmp ON tmp.listitem = f.formID AND tmp.listItem <> ''
			ORDER BY f.formTitle;

			SELECT @minAutoID = NULL, @formID = NULL;
			SELECT @minAutoID = MIN(autoID) FROM #tmpSWODImpEvaluation;
			WHILE @minAutoID IS NOT NULL BEGIN
				SELECT @formID = formID FROM #tmpSWODImpEvaluation WHERE autoID = @minAutoID;
				
				EXEC dbo.sw_addSeminarForm @seminarID=@MCSeminarID, @formID=@formID, @loadPoint='evaluation', @numResponsesPerEnrollment=0, @recordedByMemberID=@recordedByMemberID, @seminarformID=@seminarFormID OUTPUT;

				SELECT @minAutoID = MIN(autoID) FROM #tmpSWODImpEvaluation WHERE autoID > @minAutoID;
			END
		END
	COMMIT TRAN;

	IF EXISTS(SELECT 1 FROM #tmpSWODImpLogs) BEGIN
		SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
		FROM #tmpSWODImpLogs
		WHERE msg IS NOT NULL;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES ('{ "c":"auditLog", "d": {
			"AUDITCODE":"SW",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
	END

	-- update status
	EXEC platformQueue.dbo.queue_setStatus @queueType='importSWODPrograms', @itemUID=@itemUID, @queueStatus='ReadyToNotify';

	IF OBJECT_ID('tempdb..#tmpSWODQueueData') IS NOT NULL
		DROP TABLE #tmpSWODQueueData;
	IF OBJECT_ID('tempdb..#tmpSWODImpObjectives') IS NOT NULL
		DROP TABLE #tmpSWODImpObjectives;
	IF OBJECT_ID('tempdb..#tmpSWODImpCategories') IS NOT NULL
		DROP TABLE #tmpSWODImpCategories;
	IF OBJECT_ID('tempdb..#tmpSWODImpLogs') IS NOT NULL
		DROP TABLE #tmpSWODImpLogs;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
