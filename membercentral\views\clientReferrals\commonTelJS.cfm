<cfsavecontent variable="local.telJS">
	<cfoutput>
    <link rel="stylesheet" href="/assets/common/javascript/intl-tel-input/25.2.0/css/intlTelInput.css" />
	<script src="/assets/common/javascript/intl-tel-input/25.2.0/js/intlTelInputWithUtils.min.js"></script>	
	<script language="JavaScript">
        var arrPhoneNumbersIds = ['homePhone','cellPhone','alternatePhone','repHomePhone','repCellPhone','repAlternatePhone','homePhoneSecondary'];
        var MFAPhNo = [];
        var MFAPhNoInput = [];
        var MFAPhNoErrMsg = '';
        var MFAPhNoValidMsg = '';
        var MFAPhNoErrMap = ["Invalid number", "Invalid country code", "Too short", "Too long", "Invalid number"];
        var MFAPhNoOTPTimer = 0;
        var #ToScript(attributes.data.countryCode,"countryCode")#
        strNationalMode = true;
        var InitialisedMultiSelectClient = {};
        var InitialisedMultiSelectRep = {};
            
		$(function() {
			
            var #ToScript(attributes.data.homePhone,"strHomePhone")#;
			var #ToScript(attributes.data.cellPhone,"strCellPhone")#;
			var #ToScript(attributes.data.alternatePhone,"strAlternatePhone")#;
			var #ToScript(attributes.data.homePhoneE164,"strHomePhoneE164")#;
			var #ToScript(attributes.data.cellPhoneE164,"strCellPhoneE164")#;
			var #ToScript(attributes.data.alternatePhoneE164,"strAlternatePhoneE164")#;
			var #ToScript(attributes.data.repHomePhone,"strRepHomePhone")#;
			var #ToScript(attributes.data.repCellPhone,"strRepCellPhone")#;
			var #ToScript(attributes.data.repAlternatePhone,"strRepAlternatePhone")#;
			var #ToScript(attributes.data.repHomePhoneE164,"strRepHomePhoneE164")#;
			var #ToScript(attributes.data.repCellPhoneE164,"strRepCellPhoneE164")#;
			var #ToScript(attributes.data.repAlternatePhoneE164,"strRepAlternatePhoneE164")#;	
            var arrClientPhones = [];
            var arrClientRepPhones = [];
            
            $(arrPhoneNumbersIds).each(function(key,value){
                if ($("##"+value)[0] != undefined) {
                    MFAPhNoInput.push(0);
                    MFAPhNo[key] = $("##"+value)[0];
                    MFAPhNoErrMsg = $("."+value);
                    MFAPhNoValidMsg = $("##MFAPhNoValidMsg"+value);
                    if(countryCode == '' || countryCode == undefined){
                        countryCode = 'us';
                    }

                    strNationalMode = false;
                    strNumberVal = $(MFAPhNo[key]).val().trim();	
                    

                    if(value == 'homePhone'){
                        if(strHomePhoneE164.trim() != ''){
                            strNationalMode = false;
                        }else{
                            strNationalMode = true;
                        }
                    }

                    if(value == 'cellPhone'){
                        if(strCellPhoneE164.trim() != ''){
                            strNationalMode = false;
                        }else{
                            strNationalMode = true;
                        }
                    }

                    if(value == 'alternatePhone'){
                        if(strAlternatePhoneE164.trim() != ''){
                            strNationalMode = false;
                        }else{
                            strNationalMode = true;
                        }
                    }

                    if(value == 'repHomePhone'){
                        if(strRepHomePhoneE164.trim() != ''){
                            strNationalMode = false;
                        }else{
                            strNationalMode = true;
                        }
                    }

                    if(value == 'repCellPhone'){
                        if(strRepCellPhoneE164.trim() != ''){
                            strNationalMode = false;
                        }else{
                            strNationalMode = true;
                        }
                    }

                    if(value == 'repAlternatePhone'){
                        if(strRepAlternatePhoneE164.trim() != ''){
                            strNationalMode = false;
                        }else{
                            strNationalMode = true;
                        }
                    }

                    if(strNumberVal == ''){
                        strNationalMode = true;
                    }

                    /* initialise plugin */
                    MFAPhNoInput[key] = window.intlTelInput(MFAPhNo[key], {
                        initialCountry: countryCode,
                        nationalMode:strNationalMode,
                        loadUtils: () => import("/assets/common/javascript/intl-tel-input/25.2.0/js/utils.js"),
                    });
                    var MFAPhNoReset = () => {
                        MFAPhNoErrMsg.html('').hide();
                        MFAPhNoValidMsg.hide();
                    };
                    /* on blur: validate */
                    MFAPhNo[key].addEventListener('blur', () => {
                        MFAPhNoReset();
                        if (MFAPhNo[key].value.trim()) {
                            if (MFAPhNoInput[key].isValidNumber()) {
                                strContactNational = MFAPhNoInput[key].getNumber(intlTelInput.utils.numberFormat.NATIONAL);
				                strContactE164 = MFAPhNoInput[key].getNumber(intlTelInput.utils.numberFormat.E164);
                                $('##'+arrPhoneNumbersIds[key]+'National').val(strContactNational);							
                                $('##'+arrPhoneNumbersIds[key]+'E164').val(strContactE164);	
                                setSMSNumbers(arrPhoneNumbersIds[key]);						
                            }else {
                                $('##'+arrPhoneNumbersIds[key]+'National').val('');							
                                $('##'+arrPhoneNumbersIds[key]+'E164').val('');		
                                setSMSNumbers(arrPhoneNumbersIds[key]);	
                            }
                        }else{
                            $('##'+arrPhoneNumbersIds[key]+'National').val('');							
                            $('##'+arrPhoneNumbersIds[key]+'E164').val('');		
                            setSMSNumbers(arrPhoneNumbersIds[key]);
                        }
                    });
                
                    /* on keyup / change flag: reset */
                    MFAPhNo[key].addEventListener('change', MFAPhNoReset);
                    MFAPhNo[key].addEventListener('keyup', MFAPhNoReset);	
                }				
			
			});
            setSMSNumbers('homePhone'); 

            <cfif attributes.data.isRep>
                setSMSNumbers('repHomePhone'); 
            </cfif>
               
         
             $("##smsClientNumberstest").multiselect({
                header: "Choose options below"
            });      
		});
        function setNumberFormats(key,strType){
            if (MFAPhNo[key].value.trim()) {
                if (MFAPhNoInput[key].isValidNumber()) {
                    strContactNational = MFAPhNoInput[key].getNumber(intlTelInput.utils.numberFormat.NATIONAL);
                    strContactE164 = MFAPhNoInput[key].getNumber(intlTelInput.utils.numberFormat.E164);
                    $('##'+arrPhoneNumbersIds[key]+'National').val(strContactNational);							
                    $('##'+arrPhoneNumbersIds[key]+'E164').val(strContactE164);		
                    if(strType != 'validation'){
                     setSMSNumbers(arrPhoneNumbersIds[key])	
                    }			
                }else {
                    $('##'+arrPhoneNumbersIds[key]+'National').val('');							
                    $('##'+arrPhoneNumbersIds[key]+'E164').val('');
                    if(strType != 'validation'){
                     setSMSNumbers(arrPhoneNumbersIds[key])	
                    }
                }
            }
        }
        function setSMSNumbers(key){   
            if(key == 'repHomePhone' || key == 'repCellPhone' || key == 'repAlternatePhone') {
                arrClientRepPhones = [];
                arrClientRepPhonesNational = [];
                strRepHomePhoneE164 = isset($('##repHomePhoneE164').val())?$('##repHomePhoneE164').val().trim():0;
                strRepHomePhoneNational = isset($('##repHomePhoneNational').val())?$('##repHomePhoneNational').val().trim():0;
                strRepCellPhoneE164 = isset($('##repCellPhoneE164').val())?$('##repCellPhoneE164').val().trim():0;
                strRepCellPhoneNational = isset($('##repCellPhoneNational').val())?$('##repCellPhoneNational').val().trim():0;
                strRepAlternatePhoneE164 = isset($('##repAlternatePhoneE164').val())?$('##repAlternatePhoneE164').val().trim():0;
                strRepAlternatePhoneNational = isset($('##repAlternatePhoneNational').val())?$('##repAlternatePhoneNational').val().trim():0;
                if(strRepHomePhoneE164 != 0){
                    arrClientRepPhones.push(strRepHomePhoneE164);
                    arrClientRepPhonesNational.push(strRepHomePhoneNational);
                }
                if(strRepCellPhoneE164 != 0){
                    arrClientRepPhones.push(strRepCellPhoneE164);
                    arrClientRepPhonesNational.push(strRepCellPhoneNational);
                }
                if(strRepAlternatePhoneE164 != 0){
                    arrClientRepPhones.push(strRepAlternatePhoneE164);
                    arrClientRepPhonesNational.push(strRepAlternatePhoneNational);
                }
                arrClientRepPhones = [...new Set(arrClientRepPhones)];
                arrClientRepPhonesNational = [...new Set(arrClientRepPhonesNational)];
                $('##smsRepNumbers').html('');

                InitialisedMultiSelectRep = $(arrClientRepPhones).each(function(k,v){
                    $('##smsRepNumbers').append(
                        $('<option></option>').val(v).text(arrClientRepPhonesNational[k])
                    );
                });

                $("##smsRepNumbers").multiselect({
                    header: "Choose options below"
                });
                $('##smsRepNumbers').multiselect('refresh');
            }else{
                arrClientPhones = [];
                arrClientPhonesNational = [];
                strHomePhoneE164 = isset($('##homePhoneE164').val())?$('##homePhoneE164').val().trim():0;
                strHomePhoneNational = isset($('##homePhoneNational').val())?$('##homePhoneNational').val().trim():0;
                strCellPhoneE164 = isset($('##cellPhoneE164').val())?$('##cellPhoneE164').val().trim():0;
                strCellPhoneNational = isset($('##cellPhoneNational').val())?$('##cellPhoneNational').val().trim():0;
                strAlternatePhoneE164 = isset($('##alternatePhoneE164').val())?$('##alternatePhoneE164').val().trim():0;
                strAlternatePhoneNational = isset($('##alternatePhoneNational').val())?$('##alternatePhoneNational').val().trim():0;
                if(strHomePhoneE164 != 0){
                    arrClientPhones.push(strHomePhoneE164);
                    arrClientPhonesNational.push(strHomePhoneNational);
                }
                if(strCellPhoneE164 != 0){
                    arrClientPhones.push(strCellPhoneE164);
                    arrClientPhonesNational.push(strCellPhoneNational);
                }
                if(strAlternatePhoneE164 != 0){
                    arrClientPhones.push(strAlternatePhoneE164);
                    arrClientPhonesNational.push(strAlternatePhoneNational);
                }

                arrClientPhones = [...new Set(arrClientPhones)];
                arrClientPhonesNational = [...new Set(arrClientPhonesNational)];
                $('##smsClientNumbers').html('');

                InitialisedMultiSelectClient =  $(arrClientPhones).each(function(k,v){
                    $('##smsClientNumbers').append(
                        $('<option></option>').val(v).text(arrClientPhonesNational[k])
                    );
                });

                $("##smsClientNumbers").multiselect({
                    header: "Choose options below"
                });

                $('##smsClientNumbers').multiselect('refresh');
            } 
        }


        function isset(el){	
            if(el !=  undefined && el != null && el != ''){		
                return true;
            }else{
                return false;
            }	
        }
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.telJS#">