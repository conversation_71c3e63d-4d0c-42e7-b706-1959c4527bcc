ALTER PROC dbo.swod_updateSeminarSettings
@seminarID INT,
@sitecode VARCHAR(10),
@introMessageText VARCHAR(MAX),
@endofSeminartext VARCHAR(MAX),
@isPublisher BIT,
@offerQA BIT,
@blankOnInactivity BIT,
@offerCertificate BIT,
@preReqSeminarID INT,
@includeConnectionInstructionCustomText BIT,
@customText VARCHAR(1000),
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID INT, @siteID INT, @crlf VARCHAR(10), @msgjson VARCHAR(MAX), @participantID int;

	SET @crlf = CHAR(13) + CHAR(10);
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = @sitecode;

	IF OBJECT_ID('tempdb..#tblExistingSeminar') IS NOT NULL
		DROP TABLE #tblExistingSeminar;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #tblExistingSeminar(seminarID int, seminarName VARCHAR(250), introMessageText VARCHAR(MAX), endofSeminartext VARCHAR(MAX), offerQA BIT, blankOnInactivity BIT,
		offerCertificate BIT, preReqSeminarID INT,
		includeConnectionInstructionCustomText BIT,customText VARCHAR(1000));
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

	SET @participantID = dbo.fn_getParticipantIDFromOrgcode(@siteCode);

	-- Existing Data
	INSERT INTO #tblExistingSeminar (seminarID, seminarName, introMessageText, endofSeminartext, offerQA, blankOnInactivity, offerCertificate, preReqSeminarID,
		includeConnectionInstructionCustomText,customText)
	SELECT s.seminarID, s.seminarName, 
	CASE WHEN @isPublisher = 0 THEN optIn.introMessageText ELSE swod.introMessageText END AS introMessageText,
    CASE WHEN @isPublisher = 0 THEN optIn.endofSeminartext ELSE swod.endofSeminartext END AS endofSeminartext,
	swod.offerQA, swod.blankOnInactivity, s.offerCertificate, pr.preReqSeminarID,
		swod.includeConnectionInstructionCustomText, swod.customText
	FROM dbo.tblSeminars AS s
	LEFT JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = s.seminarID
	LEFT JOIN dbo.tblSeminarsPreReqs AS pr ON pr.seminarID = swod.seminarID
	LEFT JOIN dbo.tblSeminarsOptIn AS optIn ON optIn.seminarID = s.seminarID AND optIn.participantID = @participantID
	WHERE s.seminarID = @seminarID;

	BEGIN TRAN;

		IF @offerCertificate IS NOT NULL
		BEGIN
			UPDATE dbo.tblSeminars
			SET offerCertificate = @offerCertificate
			WHERE seminarID = @seminarID;
		END

		DELETE from dbo.tblSeminarsPreReqs
		WHERE seminarID = @seminarID;

		IF @preReqSeminarID > 0  
		BEGIN
			INSERT INTO dbo.tblSeminarsPreReqs (seminarID, preReqSeminarID)
			VALUES (@seminarID, @preReqSeminarID);
		END

		IF @isPublisher = 1
		BEGIN
			UPDATE dbo.tblSeminarsSWOD
			SET 
				offerQA = @offerQA,
				introMessageText = @introMessageText,
				endofSeminartext = @endofSeminartext,
				blankOnInactivity = @blankOnInactivity,
				includeConnectionInstructionCustomText = @includeConnectionInstructionCustomText,
				customText = @customText
			WHERE seminarID = @seminarID;
		END
		ELSE
		BEGIN
			UPDATE optIn
			SET 
				optIn.introMessageText = @introMessageText,
				optIn.endofSeminartext = @endofSeminartext
			FROM dbo.tblSeminarsOptIn AS optIn
			INNER JOIN dbo.tblSeminars AS s ON optIn.seminarID = s.seminarID
			WHERE s.seminarID = @seminarID AND optIn.participantID = @participantID;
		END

	COMMIT TRAN;

	/* audit log */
	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Introductory Message changed from [' + ISNULL(NULLIF(introMessageText,''),'blank') + '] to [' + ISNULL(NULLIF(@introMessageText,''),'blank') + '].'
	FROM #tblExistingSeminar
	WHERE seminarID = @seminarID
	AND  ISNULL(introMessageText,'') <> ISNULL(@introMessageText,'');

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Completion Text changed from [' + ISNULL(NULLIF(endofSeminartext,''),'blank') + '] to [' + ISNULL(NULLIF(@endofSeminartext,''),'blank') + '].'
	FROM #tblExistingSeminar
	WHERE seminarID = @seminarID
	AND ISNULL(endofSeminartext,'') <> ISNULL(@endofSeminartext,'');

	IF @offerQA IS NOT NULL
	BEGIN
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Show Player Q&A Tab changed from ' + CASE WHEN offerQA = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @offerQA = 1 THEN 'Yes' ELSE 'No' END + '.'
		FROM #tblExistingSeminar
		WHERE seminarID = @seminarID
		AND offerQA <> @offerQA;
	END

	IF @blankOnInactivity IS NOT NULL
	BEGIN
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Blank Player on Loss of Focus changed from ' + CASE WHEN blankOnInactivity = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @blankOnInactivity = 1 THEN 'Yes' ELSE 'No' END + '.'
		FROM #tblExistingSeminar
		WHERE seminarID = @seminarID
		AND blankOnInactivity <> @blankOnInactivity;
	END

	IF @offerCertificate IS NOT NULL
	BEGIN
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Offer Certificates changed from ' + CASE WHEN offerCertificate = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @offerCertificate = 1 THEN 'Yes' ELSE 'No' END + '.'
		FROM #tblExistingSeminar
		WHERE seminarID = @seminarID
		AND offerCertificate <> @offerCertificate;
	END

	IF @preReqSeminarID IS NOT NULL
	BEGIN
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Prerequisite Seminar changed from [' + ISNULL(NULLIF(s1.seminarName,''),'blank') + '] to [' + ISNULL(NULLIF(s2.seminarName,''),'blank') + '].'
		FROM #tblExistingSeminar AS tmp
		LEFT JOIN dbo.tblSeminars AS s1 ON s1.seminarID = tmp.preReqSeminarID
		LEFT JOIN dbo.tblSeminars AS s2 ON s2.seminarID = @preReqSeminarID
		WHERE tmp.seminarID = @seminarID
		AND ISNULL(tmp.preReqSeminarID,'') <> ISNULL(@preReqSeminarID,'');
	END
	
	
	IF @includeConnectionInstructionCustomText IS NOT NULL
	BEGIN
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Include Connection Instructions, Allow Custom Text changed from ' + CASE WHEN includeConnectionInstructionCustomText = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @includeConnectionInstructionCustomText = 1 THEN 'Yes' ELSE 'No' END + '.'
		FROM #tblExistingSeminar
		WHERE seminarID = @seminarID
		AND includeConnectionInstructionCustomText <> @includeConnectionInstructionCustomText;
	END
	
	IF @customText IS NOT NULL
	BEGIN
		INSERT INTO #tmpLogMessages(msg)
		SELECT 'Include Connection Instructions, Custom Text changed from [' + ISNULL(NULLIF(customText,''),'blank') + '] to [' + @customText + '].'
		FROM #tblExistingSeminar
		WHERE seminarID = @seminarID
		AND customText <> @customText;
	END	
	

	SELECT @msgjson = 'SWOD-' + CAST(seminarID AS VARCHAR(10)) + ' [' + memberCentral.dbo.fn_cleanInvalidXMLChars(seminarName) + '] has been updated for ' + @sitecode +'.'
	FROM #tblExistingSeminar
	WHERE seminarID = @seminarID;

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';

		SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
		FROM #tmpLogMessages
		WHERE msg IS NOT NULL;
	END

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');

	IF OBJECT_ID('tempdb..#tblExistingSeminar') IS NOT NULL
		DROP TABLE #tblExistingSeminar;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	RETURN 0;
	
END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
