ALTER PROC dbo.sw_getTitleFilesFromSeminarID
@seminarID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SELECT f.fileID, f.fileName, taf.titleID, ft.fileType, CASE WHEN (ft.fileType = 'audio' OR ft.fileType = 'video') THEN 'stream' ELSE CASE WHEN f.fileName = 'Slides' THEN 'pvr' ELSE 'download' END END AS fileMode
    FROM dbo.tblTitlesAndFiles AS taf 
    INNER JOIN dbo.tblFiles AS f ON taf.fileID = f.fileID 
    INNER JOIN dbo.tblFilesTypes AS ft ON f.fileTypeID = ft.filetypeID
    INNER JOIN dbo.tblParticipants as p ON f.participantID = p.participantID
    WHERE taf.titleID IN (	
							SELECT t.titleID 
							FROM dbo.tblSeminarsAndTitles AS sat 
							INNER JOIN dbo.tblTitles AS t ON sat.titleID = t.titleID
							WHERE sat.seminarID = @seminarID
							AND t.isDeleted = 0
						)
    AND f.isDeleted = 0
    ORDER BY taf.titleID, taf.fileOrder;

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO
