ALTER PROC dbo.email_sendTestBlast
@recordedByMemberID int,
@siteID int,
@blastID int,
@memberID int,
@emailList varchar(max),
@messageHTML varchar(max),
@markRecipientAsReady bit,
@fromNameOverride varchar(200) = NULL,
@replyToEmailOverride varchar(200) = NULL,
@subjectOverride varchar(400) = NULL,
@messageID int OUTPUT,
@recipientIDList varchar(max) OUTPUT,
@membernumber varchar(50) OUTPUT,
@orgcode varchar(10) OUTPUT,
@outputMessage varchar(max) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @messageTypeID int, @messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
		@rawcontent varchar(max), @messageToParse varchar(max), @fieldID int, @fieldName varchar(300), @contentID int, @orgID int, 
		@languageID int, @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @subject varchar(400), 
		@contentVersionID int, @vwSQL varchar(max), @ParamDefinition nvarchar(100), @fieldValueString varchar(200), @mcSQL nvarchar(max), 
		@emailTypeID int, @sendOnDate datetime = getdate(), @colList varchar(max), @contentLanguageID int, @orgSystemMemberID int, 
    	@orgIdentityID int, @consentListIDs varchar(MAX), @globalOptOutListID int, @isOptedOut BIT = 0, @consentListName VARCHAR(200),
		@optedOutEmails varchar(max) = '';
	declare @metadataFields TABLE (fieldName varchar(300), fieldID int NULL);
	declare @emailAddresses TABLE (email varchar(200));
	declare @optedOut TABLE (email VARCHAR(255), consentListName VARCHAR(255));
	SET @outputMessage = '';
	
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);

	set @messageID = null;
	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q';
	select @languageID = dbo.fn_getLanguageID('en');
	
	select @sendingSiteResourceID = st.siteResourceID, @orgcode = o.orgcode, @orgID = o.orgID, @emailTypeID = met.emailTypeID
	from dbo.sites as s
	inner join dbo.organizations as o on o.orgID = s.orgID
	inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
	inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID and met.emailTypeOrder = 1;

	select @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID, @orgIdentityID=orgIdentityID
	from dbo.email_EmailBlasts
	where blastID = @blastID;

	-- Parse multiple override email addresses if provided
	INSERT INTO @emailAddresses (email)
	SELECT listItem
	FROM dbo.fn_varcharListToTable(@emailList,';')

	set @orgSystemMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);

	-- override memberID to use orgSystemMemberID for member org mismatch
	select @memberID = case when orgID = @orgID then @memberID else @orgSystemMemberID end
	from dbo.ams_members
	where memberID = @memberID;

	select @subject='**TEST** ' + contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent, @contentLanguageID=contentLanguageID
	from dbo.fn_getContent(@contentID,@languageID) as messageContent;

	IF LEN(ISNULL(@fromNameOverride,'')) > 0
		SET @fromName = @fromNameOverride;

	IF LEN(ISNULL(@replyToEmailOverride,'')) > 0
	SET @replyToEmail = @replyToEmailOverride;

	IF LEN(ISNULL(@subjectOverride,'')) > 0
		SET @subject = '**TEST** ' + @subjectOverride;

	-- add any necessary metadata fields for message
	SET @messageToParse = @messageHTML + isnull(@subject,'');

	declare @regexMergeCode varchar(40);
	select @regexMergeCode = regexMergeCode from dbo.fn_getServerSettings();
	
	insert into @metadataFields (fieldName)
	select distinct left([Text],300)
	from dbo.fn_RegexMatches(@messageToParse,@regexMergeCode);
		
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT @memberID as memberID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@messageToParse,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

    IF OBJECT_ID('tempdb..##tmpEmailBlastMemberData') IS NOT NULL 
        DROP TABLE ##tmpEmailBlastMemberData;

    if @colList is null BEGIN
        select @memberID as memberID 
		into ##tmpEmailBlastMemberData
    END ELSE BEGIN
		set @vwSQL = 'select m.memberID, ' + @colList + ' 
			into ##tmpEmailBlastMemberData 
			from dbo.ams_members as m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';
        EXEC(@vwSQL);
    END
	
	-- Create recipients for each valid email address
	select m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, 
		m.membernumber, m.firstname + ' ' + m.lastname as fullname, 
		m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername, ea.email as mc_emailBlast_email, 
		@emailTypeID as emailTypeID, vw.*
	into #tmpRecipients
	FROM dbo.ams_members as m
	INNER JOIN ##tmpEmailBlastMemberData as vw on vw.memberID = m.memberID
	INNER JOIN dbo.orgIdentities AS i ON i.orgIdentityID = @orgIdentityID
	INNER JOIN dbo.ams_states AS s ON s.stateID = i.stateID
	INNER JOIN dbo.ams_countries c on c.countryID = s.countryID
	CROSS JOIN @emailAddresses ea
	WHERE m.memberID = @memberID;

	IF OBJECT_ID('tempdb..##tmpEmailBlastMemberData') IS NOT NULL 
		DROP TABLE ##tmpEmailBlastMemberData;

	-- get consentListIDS
	SELECT @consentListIDs = STUFF((SELECT ',' + cast(bcl.consentListID AS VARCHAR(10))
	FROM platformMail.dbo.email_consentListTypes clt 
	INNER JOIN platformMail.dbo.email_consentLists cl ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
		AND cl.[status] = 'A'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID 
		AND modeName = 'Opt-Out'
	INNER JOIN membercentral.dbo.email_emailBlastConsentLists AS bcl ON cl.consentListID = bcl.consentListID
		AND  bcl.blastID = @blastID
	order by isPrimary desc, emailBlastConsentListID
	FOR XML PATH('')),1,1,'');

	IF NULLIF(@consentListIDs,'') is null 
		-- get default consent list for site 	
		SELECT @consentListIDs = cast(defaultConsentListID as varchar(max))
		from dbo.sites
		where siteID = @siteID;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	INSERT INTO @optedOut (email, consentListName)
	SELECT DISTINCT ea.email, cl.consentListName
	FROM @emailAddresses ea
	INNER JOIN platformMail.dbo.email_consentListMembers clm ON clm.email = ea.email
	INNER JOIN platformMail.dbo.email_consentLists cl ON cl.consentListID = clm.consentListID
		AND cl.[status] = 'A'
	WHERE cl.consentListID = @globalOptOutListID
	OR EXISTS (
		SELECT 1
		FROM platformMail.dbo.email_consentListTypes AS clt
		INNER JOIN platformMail.dbo.email_consentLists AS cl2 ON clt.consentListTypeID = cl2.consentListTypeID
			AND clt.orgID = @orgID
			AND cl2.[status] = 'A'
		INNER JOIN platformMail.dbo.email_consentListModes AS clm2 ON clm2.consentListModeID = cl2.consentListModeID
			AND clm2.modeName = 'Opt-Out'
		INNER JOIN membercentral.dbo.email_emailBlastConsentLists AS bcl ON bcl.consentListID = cl.consentListID
			AND bcl.blastID = @blastID
		WHERE cl2.consentListID = cl.consentListID
	);

	-- Step 2: Build the output message from the temp table
	SELECT @optedOutEmails = STUFF((
		SELECT '; ' + email + ' (opted out from: ' + consentListName + ')'
		FROM @optedOut
		FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '');

	-- Step 3: Remove opted-out emails
	DELETE ea
	FROM @emailAddresses ea
	INNER JOIN @optedOut o ON o.email = ea.email;

	-- Set output message if any emails were opted out (but continue processing valid emails)
	IF LEN(@optedOutEmails) > 0 BEGIN
		SET @outputMessage = 'The following email addresses will not receive the test message: ' + @optedOutEmails;
		RETURN 0;
	END

	-- If no valid emails remain after opt-out filtering, exit
	IF NOT EXISTS (SELECT 1 FROM @emailAddresses) BEGIN
		SET @outputMessage = 'No valid email addresses remain after opt-out filtering.';
		RETURN 0;
	END

	-- get tmpRecipients columns
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));

	insert into #tmpRecipientsCols
	select c.column_id, c.name, t.name
	from tempdb.sys.columns as c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	where c.object_id = object_id('tempdb..#tmpRecipients');

	select @membernumber = membernumber
	from #tmpRecipients;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- if changes were made to the content, create a new inactive contentversion and use it for the email.
	if @messageHTML <> @rawContent BEGIN
		exec membercentral.dbo.cms_createContentVersion
			@contentLanguageID = @contentLanguageID,
			@rawContent = @messageHTML,
			@isActive = 0,
			@memberID = @orgSystemMemberID,
			@contentVersionID = @contentVersionID OUTPUT;
	END

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	-- add email_message
	EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID,  @orgIdentityID=@orgIdentityID,
		@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=1, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='', 
		@subject=@subject, @contentVersionID=@contentVersionID, @messageWrapper='', 
		@referenceType='EmailBlast', @referenceID=@blastID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
	select @messageID, memberID, getdate(), fullname, mc_emailBlast_email, @messageStatusIDInserting, null, null, emailTypeID, @siteID,@initialQueuePriority
	from #tmpRecipients;

	SELECT @recipientIDList = 
	    ISNULL((
	      SELECT 
	        CASE 
	          WHEN COUNT(*) = 1 
	          THEN CAST(MIN(recipientID) AS VARCHAR(20))
	          ELSE STRING_AGG(CAST(recipientID AS VARCHAR(20)), ',') 
	        END
	      FROM platformMail.dbo.email_messageRecipientHistory
	      WHERE messageID = @messageID
	    ), '')

	-- add metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields;

	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1;		
	
	-- add recipient metadata for all recipients
	set @ParamDefinition = N'@messageID int, @fieldID int';
	select @fieldID = min(fieldID) from @metadataFields;
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName) BEGIN
			set @fieldValueString = 'isnull(cast([' + @fieldName + '] as varchar(max)),'''')';

			set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				select @messageID, @fieldID, tr.memberID, fieldValue = ' + @fieldValueString + ', mrh.recipientID
				from #tmpRecipients tr
				inner join platformMail.dbo.email_messageRecipientHistory mrh on mrh.messageID = @messageID and mrh.memberID = tr.memberID and mrh.toEmail = tr.mc_emailBlast_email;';
			exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		select @fieldID = min(fieldID) from @metadataFields where fieldID > @fieldID;
	END	

	-- mark recipients as queued
	if @markRecipientAsReady = 1
		update mrh 
		set emailStatusID = @messageStatusIDQueued
		from platformMail.dbo.email_messages m
		inner join platformMail.dbo.email_messageRecipientHistory mrh on m.messageID = mrh.messageID
			and m.messageID = @messageID;


	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
