ALTER PROC dbo.dash_tr_paymentSumByPayerTop
@siteID INT,
@lookbackdays INT,
@xmlDataset XML OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpTopPayers') IS NOT NULL 
		DROP TABLE #tmpTopPayers;
	CREATE TABLE #tmpTopPayers (rowNum INT, memberID INT, payAmt DECIMAL(18,2));

	SET @lookbackdays = ISNULL(@lookbackdays,30);
	DECLARE @orgID INT, @lookbackFrom DATETIME = DATEADD(DAY,-@lookbackdays,GETDATE()), @tabledatacountXML xml, @tabledataXML xml;

	SELECT @orgID = orgID 
	FROM dbo.sites
	WHERE siteID = @siteID;
	
	INSERT INTO #tmpTopPayers (row<PERSON>um, memberID, payAmt)
	select ROW_NUMBER() OVER(ORDER BY payAmt desc, memberID) as row<PERSON><PERSON>, memberID, payAmt
	from (	
		SELECT TOP 20 m.activeMemberID as memberID, SUM(t.amount) as payAmt
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = t.assignedToMemberID
		WHERE t.ownedByOrgID = @orgID 
		AND t.typeID = 2 
		AND t.statusID = 1 
		AND t.transactionDate > @lookbackFrom
		GROUP BY m.activeMemberID
		ORDER BY SUM(t.amount) DESC
	) as tmp;

	-- we do this because if there are no rows to display, we need to not just return an empty data node so we can tell the difference between no rows and no cache.
	SELECT @tabledatacountXML = ISNULL((
		select count(*) as datarowcount 
		from #tmpTopPayers
		FOR XML RAW(''), ELEMENTS
	),'<reportdata/>');

	select @tabledataXML = (
		select datarow.lastname, datarow.firstname, isnull(datarow.middlename,'') as middlename, 
			isnull(datarow.company,'') as company, 
			datarow.membernumber, isnull(tmp.payamt,0) as payamt
		from #tmpTopPayers as tmp
		inner join dbo.ams_members as datarow on datarow.orgID = @orgID and datarow.memberID = tmp.memberID
		order by tmp.rowNum
		FOR XML AUTO, ELEMENTS
	);

	SELECT @xmlDataset = ISNULL((
		select @tabledatacountXML, @tabledataXML for xml path('data')
	),'<data/>');

    IF OBJECT_ID('tempdb..#tmpTopPayers') IS NOT NULL 
        DROP TABLE #tmpTopPayers;

    SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
    RETURN 0;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
    SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO
