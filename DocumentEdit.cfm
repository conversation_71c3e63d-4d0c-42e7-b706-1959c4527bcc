<cfset local.objDocuments = CreateObject("component","models.tsadmin.act_documents")>

<!--- if we need to mark the document for admin review --->
<cfif isDefined("request.btnPendingFlag")>

	<cfset local.objDocuments.saveDocument(request)>
	<cfset local.objDocuments.flagAdminReview(request)>
	<cfset application.objCommon.redirect('DocumentsQueue.cfm')>

<!--- if we need to approve the document from the review screen --->
<cfelseif isDefined("request.btnReviewApprove")>

	<cfset local.objDocuments.saveDocument(request)>
	<cfset local.objDocuments.returnDocumentToPending(request)>
	<cfset application.objCommon.redirect('DocumentsQueue.cfm')>

<!--- if we need to deny the document from the pending screen / review screen --->
<cfelseif isDefined("request.btnPendingDeny") or isDefined("request.btnReviewDeny")>

	<cfif request.actFormMode eq "editDocument">
		<cfset local.objDocuments.saveDocument(request)>
	</cfif>
	<cfset local.objDocuments.denyDocument(request)>

	<!--- from pending screen --->
	<cfif isDefined("request.btnPendingDeny")>
		<cfset local.qryNextDocument = local.objDocuments.getNextDocumentPending(documentID=request.documentID)>
		<cfif local.qryNextDocument.recordCount>
			<cfset local.navigateURL = "DocumentEdit.cfm?depoMemberDataID=#local.qryNextDocument.depoMemberDataID#&documentID=#local.qryNextDocument.DocumentID#">
		<cfelse>
			<cfset local.navigateURL = "DocumentsQueue.cfm">
		</cfif>

	<!--- from review screen --->
	<cfelse>
		<cfset local.qryNextDocument = local.objDocuments.getNextDocumentForAdminReview(documentID=request.documentID)>
		<cfif local.qryNextDocument.recordCount>
			<cfset local.navigateURL = "DocumentEdit.cfm?depoMemberDataID=#local.qryNextDocument.depoMemberDataID#&documentID=#local.qryNextDocument.DocumentID#">
		<cfelse>
			<cfset local.navigateURL = "DocumentsQueue.cfm">
		</cfif>
	</cfif>

	<cfset application.objCommon.redirect(local.navigateURL)>

<!--- if we need to add another document --->
<cfelseif isDefined("request.btnAdd")>

	<cfscript>
	local.objDocuments.saveDocument(request);
	application.objCommon.redirect('DocumentAdd.cfm?depoMemberDataID=#request.depoMemberDataID#');
	</cfscript>

<!--- if we need to save the document --->
<cfelseif isDefined("request.btnSave")>

	<cfscript>
	local.objDocuments.saveDocument(request);
	application.objCommon.redirect('DocumentEdit.cfm?depoMemberDataID=#request.depoMemberDataID#&documentID=#request.documentID#&msg=1&skipToIndex=1');
	</cfscript>

<!--- show edit document screen --->
<cfelse>
	<cfparam name="request.documentID" default="0">
	<cfparam name="request.depoMemberDataID" default="0">
	<cfparam name="request.ExpertFirstName" default="">
	<cfparam name="request.ExpertMiddleName" default="">
	<cfparam name="request.ExpertLastName" default="">
	<cfparam name="request.DocumentDate" default="">

	<cfset getDocument = local.objDocuments.getDocument(val(request.documentID))>
	<cfif getDocument.recordcount is 0>
		<cfset application.objCommon.redirect('adm_depomenu.cfm')>
	</cfif>

	<cfset formActionURL = "DocumentEdit.cfm?depoMemberDataID=#getDocument.depoMemberDataID#&documentID=#getDocument.documentID#">
	<cfset qryMemberData = application.objCommon.getMemberData(getDocument.depoMemberDataID)>
	<cfset qryDocumentSettings = local.objDocuments.getDocumentSettings()>

	<cfset dupesFound = false>
	<cfif isDefined("request.btnCheckDups")>
		<!--- get matching docs. purposely do not include middlename here --->
		<cfset qryMatchingDocs = local.objDocuments.getMatchingDocuments(expertFirstName=request.expertFirstName, expertLastName=request.expertLastName, depositionDate=request.DocumentDate, excludeDocumentID=request.documentID)>
		<cfset dupesFound = qryMatchingDocs.recordCount gt 0>
	</cfif>

	<cfset formMode = "checkDups">
	<cfset isDocApproved = getDocument.documentStatusName eq 'Approved'>
	<cfif isDefined("request.skipToIndex") or (isDefined("request.btnCheckDups") and dupesFound eq 0) or isDocApproved>
		<cfset formMode = "editDocument">
	</cfif>

	<!--- override values --->
	<cfif (isDefined("request.btnCheckDups") or isDefined("request.skipToIndex")) AND NOT isDocApproved>
		<cfset ExpertFirstName = len(request.ExpertFirstName) GT 0 ? request.ExpertFirstName : getDocument.fname>
		<cfset ExpertMiddleName = len(request.ExpertMiddleName) GT 0 ? request.ExpertMiddleName : getDocument.mname>
		<cfset ExpertLastName = len(request.ExpertLastName) GT 0 ? request.ExpertLastName : getDocument.lname>
		<cfset DocumentDate = len(request.DocumentDate) GT 0 ? request.DocumentDate : getDocument.DocumentDate>
		<cfset ExpertName = trim("#ExpertFirstName#" & (len(ExpertMiddleName) ? " #ExpertMiddleName#" : "") & " #ExpertLastName#")>
	<cfelse>
		<cfset ExpertFirstName = getDocument.fname>
		<cfset ExpertMiddleName = getDocument.mname>
		<cfset ExpertLastName = getDocument.lname>
		<cfset ExpertName = getDocument.ExpertName>
	</cfif>

	<cfif not isDefined("request.btnCheckDups") and not isDefined("request.skipToIndex") and formMode eq "editDocument">
		<cfset DocumentDate = getDocument.DocumentDate>
	<cfelse>
		<cfset DocumentDate = request.DocumentDate>
	</cfif>
	<cfif getDocument.documentStatusName EQ "Pending Approval">
		<cfset DocumentDate = getDocument.DocumentDate>
	</cfif>

	<!--- doc viewer info --->
	<cfset showDocViewer = false>
	<cfset showDocNotAvailableMsg = false>
	<cfif listFindNoCase("checkDups,editDocument",formMode) AND listFindNoCase("Pending Approval,Flagged for Review,Approved",getDocument.documentStatusName)>
		<cfset s3DocStruct = CreateObject('component','models.trialsmith.tsDocument').getDocumentInfoForWebViewer(docID=val(request.documentID))>
		<cfset showDocViewer = s3DocStruct.documentID gt 0 and s3DocStruct.result eq "viewingAllowed">
		<cfset showDocNotAvailableMsg = s3DocStruct.documentID gt 0 and (s3DocStruct.result eq "documentNotAvailable" or s3DocStruct.result eq "invalidDocument")>
	</cfif>

	<!--- get doc attachments --->
	<cfset local.attachmentsCheckedInS3 = false>
	<cfif getDocument.documentStatusName EQ "Pending Approval" and getDocument.origHasAttachments is 1>
		<cfset arrApprovalDocumentAttachments = local.objDocuments.getApprovalDocumentAttachments(documentID=getDocument.documentID)>
		<cfset local.attachmentsCheckedInS3 = true>

		<cfif arrayLen(arrApprovalDocumentAttachments)>
			<cfset local.arrAttach = []>
			<cfset local.arrAttach.append({ "fn":"Original Document", "ext":"PDF", "orig":1 })>
			<cfloop array="#arrApprovalDocumentAttachments#" index="local.thisDocument">
				<cfset local.arrAttach.append({
					"fn":"#local.thisDocument.attachmentName#",
					"ext":"#UCASE(local.thisDocument.attachmentext)#",
					"orig":0,
					"b64":"#local.thisDocument.attachmentNameBase64Value#",
					"s3":"#local.thisDocument.documentS3Link#",
					"pw":"#local.thisDocument.documentEncPassword#"
				})>
			</cfloop>
		</cfif>
	</cfif>

	<cfif formMode eq "editDocument">
		<cfscript>
		// get states
		qryStates = application.objCommon.getStates();

		// get document types
		qryDocTypes = local.objDocuments.getAllDocumentTypes();

		// get associations with members
		getAssociations = application.objCommon.getAssociationsWithMembers();
		</cfscript>

		<cfstoredproc datasource="#application.settings.dsn.trialsmith.dsn#" procedure="account_memberInit">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#getDocument.depomemberdataID#" null="No">
			<cfprocresult name="memberLinkedSites" resultset="4">
		</cfstoredproc>

		<cfif qryMemberData.adminFlag2 neq "Y" and memberLinkedSites.recordCount>
			<cfset defaultStateForCredit = memberLinkedSites.SiteCode> <!---  member's top Website Affiliations --->
		<cfelseif len(getDocument.Jurisdiction)>
			<cfset defaultStateForCredit = getDocument.Jurisdiction>
		<cfelse>
			<cfset defaultStateForCredit = "NO">
		</cfif>

		<cfif val(getDocument.CaseTypeID) eq 0>
			<cfset documentCaseTypeID = local.objDocuments.getCaseTypeIDByDescription(description="Other Types of Cases")>
		<cfelse>
			<cfset documentCaseTypeID = getDocument.CaseTypeID>
		</cfif>
	</cfif>

	<!--- Javascript --->
	<cfsavecontent variable="editJS">
		<script type='text/javascript' src='/javascripts/membercentral/jquery-1.8.3.min.js'></script>
		<script type='text/javascript' src="/javascripts/webviewer/8.1.0/webviewer.min.js"></script>
		<script type='text/javascript' src='/javascripts/membercentral/colorbox/jquery.colorbox-min-1.4.19.js'></script>
		<script type='text/javascript' src="/javascripts/membercentral/jquery.rowsorter.1.0.0.MC.js"></script>
		<script language="javascript">
		function hideAlert() { $('#errDocForm').html('').hide(); };
		function showAlert(msg) { $('#errDocForm').html(msg).show(); };
		<cfif formMode eq "editDocument">
			function onBlurExpert(){
				var ExpertFirstName = $("#ExpertFirstName").val();
				var ExpertMiddleName = $("#ExpertMiddleName").val();
				var ExpertLastName = $("#ExpertLastName").val();
				if(ExpertMiddleName.length){
					ExpertMiddleName = " " + ExpertMiddleName;
				}
				$("#ExpertName").val(ExpertLastName.length ? (ExpertFirstName + ExpertMiddleName + " " + ExpertLastName).trim() : "");
			}
			function validateForm(theForm) {
				hideAlert();

				/*skip validations while denying a doc*/
				var actionBtn = theForm.submittedBtn || 'btnSave';
				if (['btnPendingDeny','btnReviewDeny'].indexOf(actionBtn) != -1) {
					$('.docBtnContainer').hide();
					$('#frmDocSaveLoading').show();
					return true;
				}

				var errMsg = '';
				if (!_CF_hasValue(theForm['DocumentDate'], "TEXT", false))
					errMsg = "Enter the Deposition Date";
				else if (!_CF_checkdate(theForm['DocumentDate'].value, true))
					errMsg = "Enter a valid Deposition Date";
				else {
					var documentDate = new Date(theForm['DocumentDate'].value);
					var nowDate = new Date();
					if(documentDate.getTime() > nowDate.getTime()){
						errMsg = "Deposition Date does not allow future dates.";
					}
				}
				if(errMsg.length){
					showAlert(errMsg);
					theForm.DocumentDate.focus();
					return false;
				}
				if (theForm.ExpertName.value == "") {
					showAlert("Enter the expert name / document title.");
					theForm.ExpertName.focus();
					return (false);
				}
				if (theForm.DocType.value == "0") {
					showAlert("Select a type of document.");
					theForm.DocType.focus();
					return (false);
				}
				if (theForm.DepoAmazonBucks[1].checked) {
					if (theForm.DepoAmazonBucksFullName.value == "") {
						showAlert("Enter the Full Name of Receiver for Amazon Bucks.");
						theForm.DepoAmazonBucksFullName.focus();
						return (false);
					}
					if (theForm.DepoAmazonBucksEmail.value == "") {
						showAlert("Enter the Receiver Email Address for Amazon Bucks.");
						theForm.DepoAmazonBucksEmail.focus();
						return (false);
					}
					else if(!_CF_checkEmail(theForm.DepoAmazonBucksEmail.value)){
						showAlert("Enter a valid Receiver Email Address for Amazon Bucks.");
						theForm.DepoAmazonBucksEmail.focus();
						return (false);
					}
				}
				else {
					theForm.DepoAmazonBucksFullName.value = "";
					theForm.DepoAmazonBucksEmail.value = "";
					theForm.DepoAmazonBucksCredit.value = 0;
				}
				if (theForm.State.value == "") {
					showAlert("Enter the contributing association.");
					theForm.State.focus();
					return (false);
				}
				$('.docBtnContainer').hide();
				$('#frmDocSaveLoading').show();
				return (true);
			}
			function activeFormButtons() {
				document.getElementById('divButtons').style.display = 'block';
			}
			function toggleAmazonBucksOptions(f){
				document.querySelectorAll('.amazonBucksOptionRow').forEach(function(el) {
					if (f) el.style.display = "table-row";
					else el.style.display = "none";
				});
			}
			function resetBtns() {
				$('.docBtnContainer').show();
				$('#frmDocSaveLoading').hide();
			}
			function loadApproveDocOptions() {
				if (validateForm($('#frmDocument')[0])) {
					$.colorbox( { onCleanup:resetBtns, innerWidth:550, innerHeight:350, href:'<cfoutput>DocumentApprove.cfm?documentID=#getDocument.documentID#&nobanner=1&</cfoutput><cfif local.attachmentsCheckedInS3 AND arrayLen(arrApprovalDocumentAttachments)>opt3=1&</cfif>' + $('#frmDocument').serialize(), iframe:true, overlayClose:false} );
				}
			}
		<cfelseif formMode eq "checkDups">
			function validateForm(theForm) {
				hideAlert();
				var errMsg = '';
				var actionBtn = theForm.submittedBtn || 'btnCheckDups';
				if(actionBtn == 'btnCheckDups'){
					if (theForm.ExpertFirstName.value == "") errMsg = "Enter the Expert First Name.";
					else if (theForm.ExpertLastName.value == "") errMsg = "Enter the Expert Last Name.";
					else if (!_CF_hasValue(theForm['DocumentDate'], "TEXT", false)) errMsg = "Enter the Deposition Date";
					else if (!_CF_checkdate(theForm['DocumentDate'].value, true)) errMsg = "Enter a valid Deposition Date";
					if (errMsg.length){ showAlert(errMsg); return false; }
				}
				$('.docBtnContainer').hide();
				$('#frmDocSaveLoading').show();
				return true;
			}
			function skipToIndex() {
				var ExpertFirstName = $("#ExpertFirstName").val();
				var ExpertMiddleName = $("#ExpertMiddleName").val() || '';
				var ExpertLastName = $("#ExpertLastName").val();
				var DocumentDate = $("#DocumentDate").val();
				self.location.href='<cfoutput>#formActionURL#</cfoutput>&skipToIndex=1&ExpertFirstName=' + ExpertFirstName + '&ExpertMiddleName=' + ExpertMiddleName + '&ExpertLastName=' + ExpertLastName + '&DocumentDate=' + DocumentDate;
			}
		</cfif>

		function reprocessOriginalPDF() {
			$.colorbox( {onCleanup:closeBox, innerWidth:500, innerHeight:370, html:$('#reprocessOriginalPDFTemplate').html(), overlayClose:false} );
		}
		function doReprocessOriginalPDF() {
			$('#btnReprocess').prop('disabled',true).html('Please wait...');
			<cfoutput>self.location.href = 'DocumentView.cfm?depomemberdataID=#qryMemberData.depomemberdataID#&documentid=#getDocument.documentid#&type=reprocessoriginalpdf';</cfoutput>
		}
		function reuploadAndProcessDoc(){
			$.colorbox( { onCleanup:closeBox, innerWidth:550, innerHeight:270, href:'DocumentAdd.cfm?documentID=<cfoutput>#getDocument.documentID#&depomemberdataID=#qryMemberData.depomemberdataID#</cfoutput>&reuploadOriginalDoc=1&nobanner=1', iframe:true, overlayClose:false} );
		}
		function reprocessApprovedPDF() {
			$.colorbox( {onCleanup:closeBox, innerWidth:500, html:$('#reprocessApprovedPDFTemplate').html(), overlayClose:false} );
		}
		function doReprocessApprovedPDF() {
			$('#btnReprocessApprovedPDF').prop('disabled',true).html('Please wait...');
			<cfoutput>self.location.href = 'DocumentView.cfm?depomemberdataID=#qryMemberData.depomemberdataID#&documentid=#getDocument.documentid#&type=reprocessapprovedpdf';</cfoutput>
		}
		function initDocViewer() {
			let defineDocViewerInstances =
				new Promise(function(resolve, reject) {
					let definedDocViewerInstances = 0, totalDocViewerInstances = $('.tsDocumentViewerInstance').length;
					$('.tsDocumentViewerInstance').each(function() {
						WebViewer({
							path: "/javascripts/webviewer/8.1.0"
						}, this)
						.then(instance => {
							const { documentViewer } = instance.Core;
							instance.UI.disableElements(['ribbons']);
							documentViewer.addEventListener('documentLoaded', () => {
								instance.UI.setZoomLevel(0.75);
							});
							$(document).on($(this).data('triggerhandlername'), function(event ) {
								let thisDocInstanceViewerDiv = $('div[data-triggerhandlername="'+event.type+'"]');
								instance.UI.loadDocument(thisDocInstanceViewerDiv.attr('data-s3link'), {
									decrypt: instance.Core.Encryption.decrypt,
									decryptOptions: {
										p: thisDocInstanceViewerDiv.attr('data-encpass'),
										type: 'aes',
										error: msg => { console.log(msg); },
									}
								});
								thisDocInstanceViewerDiv.show();
							});
							definedDocViewerInstances++;

							if (totalDocViewerInstances == definedDocViewerInstances)
								resolve();
						});
					});
				}).catch(function(e) {
					console.log(e);
				});

			defineDocViewerInstances.then(function() {
				previewOriginalDoc();
			});
		}
		function previewOriginalDoc() {
			<cfif showDocViewer and structKeyExists(s3DocStruct, "s3Link") and structKeyExists(s3DocStruct, "encPass")>
				$('#tsDocumentViewer').attr('data-s3link','<cfoutput>#s3DocStruct.s3Link#</cfoutput>');
				$('#tsDocumentViewer').attr('data-encpass','<cfoutput>#s3DocStruct.encPass#</cfoutput>');
				$(document).trigger("loadMainDocToViewer");
			</cfif>
		}
		function closePreviewDocViewer() {
			$('#approvedDocPreviewSection').hide();
			$('#documentFieldsSection').show(300);
			$('#approvedDocPreviewSection,#documentPreviewSection').removeClass('greyBorder');
		}
		function closeBox() { $.colorbox.close(); }

		function writeAttachOption(o) {
			var tbl = $('table#attachSortTable');
			var fieldnum = (tbl.find('tbody > tr.csv_in_row').length || 0) + 1;
			var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
				var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
				return v.toString(16);
			});
			var newrow = '<tr class="csv_in_row" id="row'+fieldnum+'">';
				<cfif formMode eq "editDocument">
					newrow += '<td class="grabTD"><i class="fas fa-bars fa-lg" title="Hold and drag to move file"></i> &nbsp;<span class="sortdispnum">'+fieldnum+'</span></td>';
					newrow += '<td><input type="checkbox" data-attype="' + (o.orig == 1 ? 'orig' : 'attach') + '" data-atb64="' + (o.orig == 1 ? '' : o.b64) + '" name="attach_'+uuid+'" id="attach_'+uuid+'" class="sortfld" value="1"' + (o.ext != 'PDF' ? ' disabled="disabled"' : '') + '></td>';
				</cfif>
				newrow += '<td>'+o.fn+' (' + o.ext + ')</td>';
			if (o.orig == 1) {
				newrow += '<td style="font-size:1.05em;" align="center"><a href="#" onclick="previewOriginalDoc();return false;" style="text-decoration:none;" title="Preview Original Document"><i class="fas fa-eye"></i></a></td>';
				newrow += '<td style="font-size:1.05em;" align="center"><a href="DocumentView.cfm?documentid=<cfoutput>#getDocument.documentid#</cfoutput>&type=orig" style="margin-left:10px;text-decoration:none;" title="Download Original Document"><i class="fas fa-download"></i></a></td>';
			} else {
				if (o.s3 != '' && o.pw != '')
					newrow += '<td style="font-size:1.05em;" align="center"><a href="#" class="approvalDocAttachLink" data-s3link="' + o.s3 + '" data-encpass="' + o.pw + '" style="text-decoration:none;" title="Preview Attachment"><i class="fas fa-eye"></i></a></td>';
				else
					newrow += '<td style="font-size:1.05em;" align="center">N/A</td>';
				newrow += '<td style="font-size:1.05em;" align="center"><a href="DocumentView.cfm?documentid=<cfoutput>#getDocument.documentid#</cfoutput>&type=attach&attachNameEnc=' + o.b64 + '" style="margin-left:10px;text-decoration:none;" title="Download Attachment"><i class="fas fa-download"></i></a></td>';
			}
			newrow += '</tr>';
			tbl.append(newrow);
		}
		<cfif local.attachmentsCheckedInS3 AND arrayLen(arrApprovalDocumentAttachments)>
			function writeInitialAttachments() {
				var <cfoutput>#toScript(SerializeJSON(local.arrAttach), "jsonStr")#</cfoutput>
				$.each($.parseJSON(jsonStr), function(idx, obj) {
					writeAttachOption(obj);
				});
			}
		</cfif>
		function reorderSortOptions(tbody) {
			tbody.find('tr').each(function() {
				count = $(this).parent().children().index($(this)) + 1;
				$(this).find('.sortdispnum').html(count);
			});
		}
		function resetAttachDragDrop() {
			$('#attachSortTable').rowSorter({
				handler: 'tr > td.grabTD',
				onDrop: function(tbody, row, new_index, old_index) {
					reorderSortOptions($(tbody));
				}
			});
		}

		$(function() {
			initDocViewer();
			<cfif showDocNotAvailableMsg>
				$('#docAlertMessageHolder').show();
			</cfif>
			<cfif dupesFound>
				$('.dupDocLink').on('click', function() {
					let thisDupDoc = $(this);
					$('#tsApprovedDocViewer').attr('data-s3link',thisDupDoc.data('s3link'));
					$('#tsApprovedDocViewer').attr('data-encpass',thisDupDoc.data('encpass'));
					$(document).trigger("loadPreviewDocToViewer");
					$('#documentFieldsSection').hide();
					$('#approvedDocPreviewSection').show(300);
					$('#approvedDocPreviewSection,#documentPreviewSection').addClass('greyBorder');
				});
			</cfif>
			<cfif local.attachmentsCheckedInS3 AND arrayLen(arrApprovalDocumentAttachments)>
				resetAttachDragDrop();
				writeInitialAttachments();
				$('.approvalDocAttachLink').on('click', function() {
					let thisDoc = $(this);
					$('#tsDocumentViewer').attr('data-s3link',thisDoc.data('s3link'));
					$('#tsDocumentViewer').attr('data-encpass',thisDoc.data('encpass'));
					$(document).trigger("loadMainDocToViewer");
				});
			</cfif>
		});
		</script>
		<style>
			.alert { background:#fff6bf url(/media/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border:2px solid #f00; }
			#errDocForm { width:500px;padding:0.5rem 1rem;margin:10px 0px;border:1px solid #e78989;border-radius:0.25rem;color:#870808;background-color:#ebcaca; }
			#formFieldsContainer { display:flex; }
			#formFieldsContainer > div { width:50%; }
			.thFont {font-size: 12px;}
			.greyBorder {border:1px solid #ccc;}
			tr.csv_in_row { cursor:pointer; }
			tr.csv_in_row:hover { background:rgba(0,159,254,0.45); }
			tr.sorting-row { cursor:move; background:rgba(0,159,254,0.45); }
		</style>
		<link type='text/css' rel='stylesheet' href='/javascripts/membercentral/colorbox/colorbox-1.4.19.css'>
	</cfsavecontent>
	<cfhtmlhead text="#editJS#">

	<!--- Crumb and title --->
	<cfoutput>
	<div id="crumbs">
		You are here: <a href="adm_depomenu.cfm">Admin menu</a> \
		<cfif getDocument.documentStatusName EQ 'Pending Approval'>
			<a href="DocumentsQueue.cfm">Find and Process Documents</a> \
		<cfelseif getDocument.reviewFlag is 1>
			<a href="DocumentsQueue.cfm">Find and Process Documents</a> \
		<cfelse>
			<a href="MemberEdit.cfm?depomemberdataID=#qryMemberData.depomemberdataID#">Member Account: #qryMemberData.firstname# #qryMemberData.lastName#</a> \
			<a href="MemberDocuments.cfm?depomemberdataID=#qryMemberData.depomemberdataID#">Document Contribution History</a> \
		</cfif>
		<cfif getDocument.uploadStatus is 2>Edit Document<cfelse>Edit Newly Uploaded Document</cfif>
	</div>
	<div id="pageTitle"><cfif getDocument.uploadStatus is 2>Edit Document<cfelse>Edit Newly Uploaded Document</cfif></div>
	</cfoutput>

	<cfif isDefined("request.msg")>
		<div class="red b" style="margin-bottom:5px;">Document updated successfully.</div>
	</cfif>

	<!--- Document form --->
	<cfoutput>
	<form id="frmDocument" name="frmDocument" action="#formActionURL#" method="POST" onsubmit="return validateForm(this)">
	<div id="formFieldsContainer" style="display:flex;">
		<div id="documentFieldsSection">
			<input type="hidden" name="formsubmit" value="y">
			<input type="hidden" name="actFormMode" value="#formMode#">

			<cfset local.s3keyMod = numberFormat(getDocument.documentid mod 1000,"0000")>
			<cfset local.s3objectKey = lcase("depos/original/#local.s3keyMod#/#getDocument.documentid#.#getDocument.originalExt#")>
			<cfset local.origDocFound = 0>
			<cfif application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey=local.s3objectKey, requestType="vhost")>
				<cfset local.origDocFound = 1>
			<cfelseif FileExists("#application.settings.DOCS_ORIGINALS_PATH##getDocument.documentid#.#getDocument.originalExt#")>
				<cfset local.origDocFound = 1>
			</cfif>
			<cfif isDocApproved>
				<cfset local.objectKeyOfApprovedDoc = lcase("depos/documenttoprocess/#local.s3keyMod#/#getDocument.documentid#.pdf")>
				<cfset local.approvedPDFFound = false>
				<cfif application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey=local.objectKeyOfApprovedDoc, requestType="vhost")>
					<cfset local.approvedPDFFound = true>
				</cfif>
			</cfif>

			<cfif formMode eq "editDocument">
				<input type="hidden" name="CaseType" value="#documentCaseTypeID#">

				<div id="divButtons" class="docBtnContainer" style="display:none;">
					<!--- show pdf doc if it is there --->
					<cfset local.pdfDocFound = 0>
					<cfset local.s3keyMod = numberFormat(getDocument.documentid mod 1000,"0000")>
					<cfset local.s3objectKey = lcase("depos/pdfs/#local.s3keyMod#/#getDocument.documentid#.pdf")>
					<cfif application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey=local.s3objectKey, requestType="vhost")>
						<cfset local.pdfDocFound = 1>
					<cfelseif FileExists("#application.settings.DOCS_PDFS_PATH##getDocument.documentid#.pdf")>
						<cfset local.pdfDocFound = 1>
					</cfif>
					<cfif local.pdfDocFound is 1>
						<input type="button" value="View PDF" onclick="self.location.href='DocumentView.cfm?documentid=#getDocument.documentid#&type=pdf';" style="font-size:9pt;">
					<cfelse>
						<input type="button" value="View PDF" style="font-size:9pt;" disabled>
					</cfif>

					<!--- show tif doc if it is there --->
					<cfset local.tifDocFound = 0>
					<cfset local.s3objectKey = lcase("depos/tiffs/#local.s3keyMod#/#getDocument.documentid#.tif")>
					<cfif application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey=local.s3objectKey, requestType="vhost")>
						<cfset local.tifDocFound = 1>
					<cfelseif FileExists("#application.settings.DOCS_TIFS_PATH##getDocument.documentid#.tif")>
						<cfset local.tifDocFound = 1>
					</cfif>
					<cfif local.tifDocFound is 1>
						<input type="button" value="View TIF" onclick="self.location.href='DocumentView.cfm?documentid=#getDocument.documentid#&type=tif';" style="font-size:9pt;">
					<cfelse>
						<input type="button" value="View TIF" style="font-size:9pt;" disabled>
					</cfif>

					<!--- show txt doc if it is there --->
					<cfset local.txtDocFound = 0>
					<cfset local.s3objectKey = lcase("depos/text/#local.s3keyMod#/#getDocument.documentid#.txt")>
					<cfif application.objS3.s3FileExists(bucket='trialsmith-depos', objectKey=local.s3objectKey, requestType="vhost")>
						<cfset local.txtDocFound = 1>
					<cfelseif FileExists("#application.settings.DEPODOCUMENTTEXT_PATH##getDocument.documentid#.txt")>
						<cfset local.txtDocFound = 1>
					</cfif>
					<cfif local.txtDocFound is 1>
						<input type="button" value="View TXT" onclick="self.location.href='DocumentView.cfm?documentid=#getDocument.documentid#&type=txt';" style="font-size:9pt;">
					<cfelse>
						<input type="button" value="View TXT" style="font-size:9pt;" disabled>
					</cfif>

					&nbsp; &nbsp; &nbsp;
					<!--- Pending approval --->
					<cfif getDocument.documentStatusName EQ "Pending Approval">
						<input type="button" name="btnShowDocApproveOpt" value="Approve" onclick="loadApproveDocOptions();" style="font-size:9pt;">
						<input type="submit" name="btnPendingDeny" value="Deny" onclick="this.form.submittedBtn=this.name" style="font-size:9pt;">
						<input type="submit" name="btnPendingFlag" value="Flag for Admin Review" style="font-size:9pt;">

					<!--- Admin Review --->
					<cfelseif getDocument.reviewFlag is 1>
						<input type="submit" name="btnReviewApprove" value="Return to Pending Queue" style="font-size:9pt;">
						<input type="submit" name="btnReviewDeny" value="Deny" onclick="this.form.submittedBtn=this.name" style="font-size:9pt;">

					<!--- editing doc --->
					<cfelse>
						<cfif getDocument.documentStatusName EQ "Uploaded">
							<input type="button" name="btnShowDocApproveOpt" value="Approve" onclick="loadApproveDocOptions();" style="font-size:9pt;">
							&nbsp; &nbsp;
						</cfif>
						<input type="submit" name="btnSave" value="Save Changes" style="font-size:9pt;">
						&nbsp; &nbsp; &nbsp;
						<input type="submit" name="btnAdd" value="Add Document" style="font-size:9pt;">
					</cfif>
				</div>
				<div id="frmDocSaveLoading" style="display:none;margin:10px 0;"><i class="fal fa-circle-notch fa-spin"></i> <b>Please Wait...</b></div>
				<br/>
			</cfif>

			<table id="tblDocFields" cellpadding="2" style="min-width:600px;">
			<tr style="height:22px;">
				<td class="red">Document ID:</td>
				<td class="red">&nbsp;</td>
				<td>
					<strong>#getDocument.DocumentID#</strong>
					<!--- Show orig doc if it is there --->
					<cfif local.origDocFound>
						&nbsp;&nbsp;<input type="button" value="View Original Doc (#ucase(getDocument.originalExt)#)" onclick="self.location.href='DocumentView.cfm?documentid=#getDocument.documentid#&type=orig';" style="font-size:9pt;">
					<cfelse>
						&nbsp;&nbsp;<input type="button" value="View Original Doc" style="font-size:9pt;" disabled>
					</cfif>

					<cfif (getDocument.documentStatusName EQ "Pending Approval" or getDocument.documentStatusName EQ "Flagged for Review") and (local.origDocFound and getDocument.originalExt eq "pdf")>
						&nbsp;&nbsp;<input type="button" value="Reprocess Original PDF" onclick="reprocessOriginalPDF();" style="font-size:9pt;">
					</cfif>
					<cfif (isDocApproved AND local.approvedPDFFound) or getDocument.documentStatusName EQ "Flagged for Review">
						&nbsp;&nbsp;<input type="button" value="Reprocess Approved PDF" onclick="reprocessApprovedPDF();" style="font-size:9pt;">
					</cfif>
					<cfif getDocument.documentStatusName EQ "Pending Approval" or (getDocument.documentStatusName EQ "Flagged for Review" and val(getDocument.XODPreApprove) eq 0)>
						&nbsp;&nbsp;<input type="button" value="Re-Upload Original Doc" onclick="reuploadAndProcessDoc();" style="font-size:9pt;">
					</cfif>
				</td>
			</tr>
			<tr style="height:22px;">
				<td class="red">Contributor:</td>
				<td class="red">&nbsp;</td>
				<td>
					<strong><span id="contribSpan"><a href="MemberEdit.cfm?depomemberdataID=#qryMemberData.depomemberdataID#"><span id="contribName">#qryMemberData.FirstName# #qryMemberData.LastName#</span></a> (SourceID: #qryMemberData.sourceID#)</span></strong>
				</td>
			</tr>
			<tr style="height:22px;">
				<td class="red">Date Entered:</td>
				<td class="red">&nbsp;</td>
				<td><strong>#DateFormat(getDocument.DateEntered,"mmmm d, yyyy")# #TimeFormat(getDocument.DateEntered,"h:mm tt")#</strong></td>
			</tr>
			<tr style="height:22px;">
				<td class="red">Date Last Updated:</td>
				<td class="red">&nbsp;</td>
				<td><strong>#DateFormat(getDocument.Datelastmodified,"mmmm d, yyyy")# #TimeFormat(getDocument.Datelastmodified,"h:mm tt")#</strong></td>
			</tr>
			<tr>
				<td colspan="3">
					&nbsp;
					<div id="errDocForm" style="display:none;"></div>
				</td>
			</tr>
			<tr>
				<td>Expert First Name:</td>
				<td>&nbsp;</td>
				<td><input type="text" size="16" id="ExpertFirstName" name="ExpertFirstName" value="#ExpertFirstName#"<cfif formMode eq "editDocument"> onblur="onBlurExpert()"</cfif>></td>
			</tr>
			<tr>
				<td>Expert Middle Name:</td>
				<td>&nbsp;</td>
				<td><input type="text" size="16" id="ExpertMiddleName" name="ExpertMiddleName" value="#ExpertMiddleName#"<cfif formMode eq "editDocument"> onblur="onBlurExpert()"</cfif>></td>
			</tr>
			<tr>
				<td>Expert Last Name:</td>
				<td>&nbsp;</td>
				<td><input type="text" size="16" id="ExpertLastName" name="ExpertLastName" value="#ExpertLastName#"<cfif formMode eq "editDocument"> onblur="onBlurExpert()"</cfif>></td>
			</tr>
			<tr>
				<td class="red">Deposition Date:</td>
				<td class="red">(R)</td>
				<td><input type="text" name="DocumentDate" id="DocumentDate" size="10" maxlength="10" value="#DateFormat(DocumentDate,"mm/dd/yyyy")#" placeholder="mm/dd/yyyy" autocomplete="off"></td>
			</tr>
			<cfif formMode eq "editDocument">
				<tr>
					<td>Style of Case:</td>
					<td>&nbsp;</td>
					<td><input type="text" size="60" name="Style" value="#getDocument.Style#"></td>
				</tr>
				<tr>
					<td>Jurisdiction:</td>
					<td>&nbsp;</td>
					<td><select name="Jurisdiction">
						<option value=""></option>
						<cfloop query="qryStates">
							<option value="#code#" <cfif trim(getDocument.Jurisdiction) eq code>selected</cfif>>#Code# - #Name#</option>
						</cfloop>
						</select>
					</td>
				</tr>
				<tr valign="top">
					<td>Notes / Comments / Doc Name:</td>
					<td>&nbsp;</td>
					<td><textarea name="Notes" rows="4" cols="46">#getDocument.Notes#</textarea></td>
				</tr>
				<tr><td colspan="3">&nbsp;</td></tr>
				<tr>
					<td class="red">Expert Name / Document Title:</td>
					<td class="red">(R)</td>
					<td><input type="text" size="40" id="ExpertName" name="ExpertName" value="#htmleditformat(ExpertName)#"></td>
				</tr>
				<tr>
					<td class="red">Type of Document</td>
					<td class="red">(R)</td>
					<td><select name="DocType" id="selDocType">
						<option value="0"></option>
						<cfoutput query="qryDocTypes" group="category">
							<cfif len(category)>
								<optgroup label="#category#">
									<cfoutput>
										<option value="#qryDocTypes.TypeID#" <cfif getDocument.DocumentTypeID is qryDocTypes.typeID>selected</cfif>>#qryDocTypes.Description#</option>
									</cfoutput>
								</optgroup>
							<cfelse>
								<cfoutput>
									<option value="#qryDocTypes.TypeID#" <cfif getDocument.DocumentTypeID is qryDocTypes.typeID>selected</cfif>>#qryDocTypes.Description#</option>
								</cfoutput>
							</cfif>
						</cfoutput>
						</select>
					</td>
				</tr>
				<cfif listFindNoCase("Pending Approval,Uploaded",getDocument.documentStatusName)>
					<tr>
						<td class="red">Member Credit Applied:</td>
						<td class="red">(R)</td>
						<td><select name="MemberCredit">
							<option value="PC">Purchase Credit</option>
							<option value="CD">CD Order</option>
							<option value="NO">No Credit Given</option>
							</select>
						</td>
					</tr>
					<input type="hidden" name="StateCredit" value="#defaultStateForCredit#">
				</cfif>
				<tr>
					<td class="red">Eligible for Amazon Bucks:</td>
					<td class="red">(R)</td>
					<td><input type="radio" name="DepoAmazonBucks" Value="0" <cfif getDocument.DepoAmazonBucks neq "1">checked</cfif> onclick="toggleAmazonBucksOptions(0)">No &nbsp;
						<input type="radio" name="DepoAmazonBucks" value="1" <cfif getDocument.DepoAmazonBucks eq "1">checked</cfif> onclick="toggleAmazonBucksOptions(1)">Yes
					</td>
				</tr>
				<tr class="amazonBucksOptionRow">
					<td class="red">Full Name of Receiver:</td>
					<td class="red">(R)</td>
					<td><input type="text" name="DepoAmazonBucksFullName" value="#htmleditformat(getDocument.DepoAmazonBucksFullName)#" size="40"></td>
				</tr>
				<tr class="amazonBucksOptionRow">
					<td class="red">Receiver Email Address:</td>
					<td class="red">(R)</td>
					<td><input type="text" name="DepoAmazonBucksEmail" value="#getDocument.DepoAmazonBucksEmail#" size="40"></td>
				</tr>
				<tr class="amazonBucksOptionRow">
					<td>Amazon Credit Value:</td>
					<td>&nbsp;</td>
					<td>
						<input type="text" name="DepoAmazonBucksCredit" value="#val(getDocument.DepoAmazonBucksCredit) GT 0 ? val(getDocument.DepoAmazonBucksCredit) : val(qryDocumentSettings.DepoAmazonBucksCredit)#" size="10" style="color:##999999" maxlength="10" readonly> <small>(Read Only)</small>
					</td>
				</tr>
				<tr>
					<td class="red">Contributing Association:</td>
					<td class="red">(R)</td>
					<td><select name="State">
						<cfloop query="getAssociations">
							<option value="#TLAMemberState#" <cfif trim(getDocument.state) eq TLAMemberState>selected</cfif>>#TLAMemberState# - #description#</option>
						</cfloop>
						</select>
					</td>
				</tr>
				<cfif getDocument.uploadStatus is not 1>
					<tr>
						<td>Security:</td>
						<td>&nbsp;</td>
						<td><input type="checkbox" name="disabled" value="1" <cfif getDocument.disabled eq "Y">checked</cfif>>This document is disabled</td>
					</tr>
				</cfif>
			</cfif>
			<cfif local.attachmentsCheckedInS3 AND arrayLen(arrApprovalDocumentAttachments)>
				<tr>
					<td colspan="3">
						<div style="width:600px;margin-top:20px;">
							<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Document Attachments</div>
							<div style="border:1px solid ##0E568D;padding:4px;">
								<table cellpadding="4" id="attachSortTable" cellspacing="0" cellpadding="4" width="100%">
									<thead>
										<tr>
											<cfif formMode eq "editDocument">
												<td colspan="2"><b>Order</b></td>
											</cfif>
											<td><b>File Name</b></td>
											<td align="center"><b>Preview</b></td>
											<td align="center"><b>Download</b></td>
										</tr>
									</thead>
									<tbody>
									</tbody>
									<cfif formMode eq "editDocument">
										<tfoot>
											<tr>
												<td colspan="5">
													<br/>
													Note: Reordering is only used when approving this document and will not be saved to the document.
												</td>
											</tr>
										</tfoot>
									</cfif>
								</table>
							</div>
						</div>
					</td>
				</tr>
			</cfif>
			<cfif dupesFound>
				<cfset arrDocumentsInfo = local.objDocuments.getMatchingDocumentsInfo(qryDocuments=qryMatchingDocs)>
				<tr>
					<td colspan="3">
						<div id="matchingDocumentsContainer" style="width:600px;margin:5px 0px;">
							<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Matching Existing Documents</div>
							<div style="border:1px solid ##0E568D;padding:4px;">
								The following documents closely match this document:<br/><br/>
								<table width="100%">
								<tr>
									<th class="thFont" width="15%">Doc ID</th>
									<th class="thFont" width="15%">Depo Date</th>
									<th class="thFont" width="20%">Expert Name</th>
									<th class="thFont" width="30%">Contributor Name</th>
									<th class="thFont" width="20%">View</th>
								</tr>
								<cfloop array="#arrDocumentsInfo#" item="thisDocument">
									<tr>
										<td align="center"><a href="DocumentEdit.cfm?depoMemberDataID=#thisDocument.depoMemberDataID#&documentID=#thisDocument.documentID#&skipToIndex=1">#thisDocument.documentID#</a></td>
										<td align="center">#DateFormat(thisDocument.documentDate,"m/d/yyyy")#</td>
										<td align="center">#thisDocument.expertName#</td>
										<td align="center">#thisDocument.contributorName# (SourceID: #thisDocument.SourceID#)</td>
										<td align="center" id="tsDupDoc#thisDocument.documentID#" style="margin:3px;">
											<cfif thisDocument.canViewDocument>
												<a href="##" class="dupDocLink" data-docid="#thisDocument.documentID#" data-docdepomemdataid="#thisDocument.depoMemberDataID#" data-s3link="#thisDocument.documentS3Link#" data-encpass="#thisDocument.documentEncPassword#">Preview</a>
											</cfif>
										</td>
									</tr>
								</cfloop>
								</table>
							</div>
						</div>
					</td>
				</tr>
			</cfif>
			<cfif formMode eq "checkDups">
				<tr><td colspan="3"></td></tr>
				<tr>
					<td colspan="2"></td>
					<td>
						<div id="divButtons" class="docBtnContainer">
							<input type="submit" name="btnCheckDups" value="Check for Dups" onclick="this.form.submittedBtn=this.name;" >
							<cfif not dupesFound>
								<input type="button" name="btnSkipToIndex" value="Skip to Index"  title="Proceed to Index" onclick="skipToIndex();" >
							<cfelse>
								<input type="button" name="btnProceedToIndex" value="Proceed to Index" title="Proceed to Index" onclick="skipToIndex();">
							</cfif>
							<!--- Deny (Pending) --->
							<cfif getDocument.documentStatusName EQ 'Pending Approval'>
								<input type="submit" name="btnPendingDeny" value="Deny/Flag as Dupe" title="Select this button if the new document matches an existing document" onclick="this.form.submittedBtn=this.name;">
							<!--- Deny (Admin Review) --->
							<cfelseif getDocument.reviewFlag is 1>
								<input type="submit" name="btnReviewDeny" value="Deny/Flag as Dupe" title="Select this button if the new document matches an existing document" onclick="this.form.submittedBtn=this.name;">
							</cfif>
							<cfif getDocument.uploadStatus is 1>
								<input type="button" name="btnReturnToDocQueue" value="Cancel" title="Cancel and return to Find Documents" onclick="self.location.href='DocumentsQueue.cfm';">
							</cfif>
						</div>
						<div id="frmDocSaveLoading" style="display:none;margin:10px 0;"><i class="fal fa-circle-notch fa-spin"></i> <b>Please Wait...</b></div>
					</td>
				</tr>
			</cfif>
			</table>
		</div>
		<cfif dupesFound>
			<div id="approvedDocPreviewSection" style="padding:10px 20px;display:none;">
				<div style="display:flex;margin-bottom:4px;">
					<span><b>Document Already in TrialSmith</b></span>
					<button type="button" name="btnClosePreviewDocViewer" onclick="closePreviewDocViewer();" style="margin-left:auto;">Close Document Viewer</button>
				</div>
				<div id="tsApprovedDocViewer" class="tsDocumentViewerInstance" data-triggerhandlername="loadPreviewDocToViewer" data-s3link="" data-encpass="" style="clear:both;height:800px;overflow:hidden;display:none;"></div>
			</div>
		</cfif>
		<div id="documentPreviewSection" style="padding:10px 20px;margin-left:4px;">
			<cfif (getDocument.documentStatusName EQ "Pending Approval" or getDocument.documentStatusName EQ "Flagged for Review") AND (formMode eq "checkDups" AND showDocViewer AND structKeyExists(s3DocStruct, "s3Link") AND structKeyExists(s3DocStruct, "encPass"))>
				<div class="docBtnContainer" style="display:flex;margin-bottom:4px;">
					<cfif getDocument.documentStatusName EQ "Pending Approval">
						<span style="font-weight:bold;font-size:1.2em;">Document Pending Approval</span>
					<cfelseif getDocument.documentStatusName EQ "Flagged for Review">
						<span style="font-weight:bold;font-size:1.2em;">Document Flagged for Review</span>
					</cfif>
					<cfif dupesFound>
						<input type="submit" name="btnPendingDeny" value="Deny/Flag as Dupe" title="Select this button if the new document matches an existing document" onclick="this.form.submittedBtn=this.name;" style="margin-left:auto;">
					</cfif>
				</div>
			</cfif>
			<div id="tsDocumentViewer" class="tsDocumentViewerInstance" data-triggerhandlername="loadMainDocToViewer" style="clear:both;height:800px;overflow:hidden;display:none;"></div>
			<div id="docAlertMessageHolder" style="display:none;">
				<div class="alert" style="width:250px;">Document Preview Unavailable</div>
			</div>
		</div>
	</div>
	</form>
	<cfif formMode eq "editDocument">
		<!--- Show the form buttons after the whole page is loaded. Prevents the buttons from being clicked when the page hasnt completed loading. --->
		<script langauge="JavaScript">activeFormButtons();toggleAmazonBucksOptions(#val(getDocument.DepoAmazonBucks)#);</script>
	</cfif>

	<script type="text/html" id="reprocessOriginalPDFTemplate">
		<div style="padding:10px;font-family: verdana;">
			<div style="font-size: 1.5em;font-weight: bold;margin-bottom:20px;color: ##0E568D;">Reprocess Original PDF</div>
			<div style="font-size: 1.1em;margin-bottom:20px;">
				Reprocessing the original PDF will do the following:
				<ol>
				<li>If this document has been flagged as having attachments, we will immediately clear this flag and delete any extracted attachments that may be present in S3.</li>
				<li>We will download the original PDF and run it through the PDF Attachments queue, which will attempt to extract attachments and upload them.</li>
				<li>We will regenerate the document previews and the attachment list, if applicable.</li>
				</ol>
				This process will take several minutes to complete. You will need to reload this page to see the reprocessed document.
			</div>
			<div style="align-content:center !important; align-items:center !important; display:flex !important; color:##824224; background-color:##fde4d5; border-color:##fcd9c4; position:relative; padding:.75rem 1.25rem; margin-bottom:1em; border:1px solid transparent; border-radius:.65rem;font-size:14px;">
				<span style="font-size: 1.1875rem;height: 40px !important; line-height: 40px !important; width: 40px !important; text-align: center !important; margin-right: .5rem !important;display: block !important;">
					<i class="fas fa-question" style="padding-top:10px;"></i>
				</span>
				<span>Are you sure you want to reprocess this document?</span>
			</div>
			<div style="text-align:right;"><button type="button" name="btnReprocess" id="btnReprocess" onclick="doReprocessOriginalPDF();">Continue</button></div>
		</div>
	</script>
	<script type="text/html" id="reprocessApprovedPDFTemplate">
		<div style="padding:10px;font-family: verdana;">
			<div style="font-size: 1.5em;font-weight: bold;margin-bottom:20px;color: ##0E568D;">Reprocess Approved PDF</div>
			<div style="font-size: 1.1em;margin-bottom:20px;">
				Reprocessing the approved PDF will regenerate the document previews.<br/>
				This process will take several minutes to complete. You will need to reload this page to see the reprocessed document.
			</div>
			<div style="align-content:center !important; align-items:center !important; display:flex !important; color:##824224; background-color:##fde4d5; border-color:##fcd9c4; position:relative; padding:.75rem 1.25rem; margin-bottom:1em; border:1px solid transparent; border-radius:.65rem;font-size:14px;">
				<span style="font-size: 1.1875rem;height: 40px !important; line-height: 40px !important; width: 40px !important; text-align: center !important; margin-right: .5rem !important;display: block !important;">
					<i class="fas fa-question" style="padding-top:10px;"></i>
				</span>
				<span>Are you sure you want to reprocess this approved document?</span>
			</div>
			<div style="text-align:right;"><button type="button" name="btnReprocessApprovedPDF" id="btnReprocessApprovedPDF" onclick="doReprocessApprovedPDF();">Continue</button></div>
		</div>
	</script>
	</cfoutput>
</cfif>
