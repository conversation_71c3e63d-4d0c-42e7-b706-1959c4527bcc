ALTER FUNCTION dbo.swb_BundlesInMyCatalog (
	@OrgCode varchar(10)
) 
returns @tblBundles table (autoID int IDENTITY(1,1), bundleID int)
AS
begin

/* 
	1st part of union:
	-- my bundles, Active

	2nd part of union:
	-- opted in bundles, Active
	-- publisher allowed syndication 
*/
	declare @participantID int--, @now datetime
	set @participantID = dbo.fn_getParticipantIDFromOrgcode(@OrgCode)

	INSERT INTO @tblBundles (bundleID)
	SELECT b.bundleID
	FROM dbo.tblBundles as b
	WHERE b.participantID = @participantID
	AND b.status = 'A'
	union
	SELECT b.bundleID
	FROM dbo.tblBundles as b
	INNER JOIN dbo.tblBundlesOptIn as boi on boi.bundleID = b.bundleID AND boi.isActive = 1 and boi.sellCatalog = 1
	INNER JOIN dbo.tblParticipants as p on p.participantID = boi.participantID
	WHERE p.orgcode = @OrgCode
	AND b.status = 'A'
	AND b.allowSyndication = 1
	ORDER BY b.bundleID

	RETURN

end
GO
