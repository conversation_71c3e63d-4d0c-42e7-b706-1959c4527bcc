ALTER PROC dbo.sms_recordTwilioMsgStatusTracking
@siteID int,
@messagingServiceID int,
@messageID int,
@recipientID int,
@deliveryStatusID int,
@responseDate datetime

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	UPDATE mr
	SET mr.deliveryStatusID = @deliveryStatusID,
		dateLastUpdated = @responseDate
	FROM dbo.sms_messageRecipients AS mr
	INNER JOIN dbo.sms_statusApprovedFlow AS flow ON flow.currentStatusID = mr.deliveryStatusID
		AND flow.allowedNewStatusID = @deliveryStatusID
	WHERE mr.siteID=@siteID
	AND mr.messagingServiceID = @messagingServiceID
	AND mr.messageID = @messageID
	AND mr.recipientID = @recipientID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
