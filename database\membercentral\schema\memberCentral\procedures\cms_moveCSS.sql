ALTER PROC dbo.cms_moveCSS
@siteID int,
@customCSSID INT,
@dir varchar(4)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @cssOrder INT;

	SELECT @cssOrder = cssOrder
	FROM dbo.cms_customCSS
	WHERE siteID = @siteID AND customCSSID = @customCSSID;
	
	IF @cssOrder IS NULL
		RAISERROR('invalid Custom CSS',16,1);

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			UPDATE dbo.cms_customCSS
			SET cssOrder = cssOrder + 1
			WHERE siteID = @siteID
			AND cssOrder >= @cssOrder - 1;

			UPDATE dbo.cms_customCSS
			SET cssOrder = cssOrder - 2
			WHERE customCSSID = @customCSSID
			AND siteID = @siteID;
		END
		ELSE BEGIN
			UPDATE dbo.cms_customCSS
			SET cssOrder = cssOrder - 1
			WHERE siteID = @siteID
			AND cssOrder <= @cssOrder + 1;

			UPDATE dbo.cms_customCSS
			SET cssOrder = cssOrder + 2
			WHERE customCSSID = @customCSSID;
		END	
	COMMIT TRAN;

	EXEC dbo.cms_reorderCSS @siteID=@siteID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
