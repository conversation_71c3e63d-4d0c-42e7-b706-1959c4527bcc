ALTER PROC dbo.queue_conditionCacheCheck_clear
@status varchar(60)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DELETE qi
	FROM dbo.queue_conditionCacheCheck AS qi
	INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID
	WHERE qs.queueStatus = @status;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
