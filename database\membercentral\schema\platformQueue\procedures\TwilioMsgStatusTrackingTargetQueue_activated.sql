ALTER PROC dbo.TwilioMsgStatusTrackingTargetQueue_activated
AS

SET NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @receive_table TABLE (queuing_order BIGINT, [conversation_handle] UNIQUEIDENTIFIER, message_type_name SYSNAM<PERSON>, message_body VARBINARY(MAX));
DECLARE message_cursor CURSOR LOCAL FORWARD_ONLY READ_ONLY 
	FOR SELECT [conversation_handle], message_type_name, message_body 
	FROM @receive_table 
	ORDER BY queuing_order;
DECLARE @conversation_handle UNIQUEIDENTIFIER, @message_type SYSNAME, @message_body VARBINARY(max), @xmldata xml, 
	@error_number INT, @error_message VARCHAR(4000), @error_severity INT, @error_state INT, @error_procedure SYSNAME, 
	@error_line INT, @error_dialog VARCHAR(50), @messagingServiceID int, @recipientID int,
	@deliveryStatusID int, @responseDate datetime, @siteID int, @messageID int;

BEGIN TRY
WHILE (1 = 1) BEGIN
	BEGIN TRANSACTION;

		-- Receive all available messages into the table.
		-- Wait 5 seconds for messages.
		WAITFOR (
			RECEIVE [queuing_order], [conversation_handle], [message_type_name], [message_body]
			FROM TwilioMsgStatusTrackingTargetQueue
			INTO @receive_table
		), TIMEOUT 5000;

		IF @@ROWCOUNT = 0 BEGIN
			COMMIT;
			BREAK;
		END ELSE BEGIN
			OPEN message_cursor;
			WHILE (1=1) BEGIN
				FETCH NEXT FROM message_cursor INTO @conversation_handle, @message_type, @message_body; 
				IF (@@FETCH_STATUS != 0) BREAK;

				BEGIN TRY
					IF @message_type = 'platformQueue/GeneralXMLRequest' BEGIN
						-- process the msg.
						SET @xmldata = cast(@message_body as xml);

						SELECT 
							@messagingServiceID = isnull(@xmldata.value('(/t/msid)[1]','int'),0),
							@siteID = isnull(@xmldata.value('(/t/siteid)[1]','int'),0),
							@messageID  = isnull(@xmldata.value('(/t/mid)[1]','int'),0),
							@recipientID = isnull(@xmldata.value('(/t/rid)[1]','int'),0),
							@deliveryStatusID = isnull(@xmldata.value('(/t/st)[1]','int'),0),
							@responseDate = @xmldata.value('(/t/dt)[1]','datetime');

						IF @messagingServiceID > 0 and @siteID > 0 and @messageID > 0 and @recipientID > 0 and @deliveryStatusID > 0
							EXEC platformMail.dbo.sms_recordTwilioMsgStatusTracking
								@siteID=@siteID, @messagingServiceID=@messagingServiceID, @messageID=@messageID,
								@recipientID=@recipientID, @deliveryStatusID=@deliveryStatusID, @responseDate=@responseDate;
					END
					IF @message_type = 'platformQueue/EndOfStream' BEGIN
						-- initiator is signaling end of message stream: end the dialog
						END CONVERSATION @conversation_handle;
					END
					IF @message_type = 'https://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
						WITH XMLNAMESPACES ('https://schemas.microsoft.com/SQL/ServiceBroker/Error' AS ssb)
						SELECT @error_number = CAST(@message_body AS XML).value('(//ssb:Error/ssb:Code)[1]', 'INT'),
							@error_message = CAST(@message_body AS XML).value('(//ssb:Error/ssb:Description)[1]', 'VARCHAR(4000)');
						SET @error_dialog = CAST(@conversation_handle AS VARCHAR(50));

						RAISERROR('Error in dialog %s: %s (%i)', 16, 1, @error_dialog, @error_message, @error_number);
						END CONVERSATION @conversation_handle;
					END
				END TRY
				BEGIN CATCH
					SET @error_message = ERROR_MESSAGE();
					IF XACT_STATE() = -1 BEGIN
						-- The transaction is doomed. Only rollback possible.
						-- This could disable the queue if done 5 times consecutively!
						ROLLBACK TRANSACTION;

						-- Record the error.
						BEGIN TRAN;
							INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) 
							VALUES(@error_message, @message_body);
						COMMIT TRAN;

						-- For this level of error, it is best to exit the proc and give the queue monitor control.
						-- Breaking to the outer catch will accomplish this.
						RAISERROR ('Message processing error', 16, 1);
					END
					ELSE IF XACT_STATE() = 1 BEGIN
						-- Record error and continue processing messages.
						-- Failing message could also be put aside for later processing here.
						-- Otherwise it will be discarded.
						INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
						VALUES(@error_message, @message_body);
					END
				END CATCH
			END

			CLOSE message_cursor;
			DELETE FROM @receive_table;
		END

	COMMIT TRAN;
END
END TRY
BEGIN CATCH
	-- Process the error and exit the proc to give the queue monitor control
	SET @error_message = ERROR_MESSAGE();

	IF XACT_STATE() = -1 BEGIN
		-- The transaction is doomed. Only rollback possible.
		-- This could disable the queue if done 5 times consecutively!
		ROLLBACK TRANSACTION;

		-- Record the error.
		BEGIN TRAN;
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
			VALUES(@error_message, @message_body);
		COMMIT TRAN;
	END
	ELSE IF XACT_STATE() = 1 BEGIN
		-- Record error and commit transaction.
		-- Here you could also save anything else you want before exiting.
		BEGIN TRAN;
			INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody)
			VALUES(@error_message, @message_body);
		COMMIT TRAN;
	END
END CATCH
GO
