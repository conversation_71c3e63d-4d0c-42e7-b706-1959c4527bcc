ALTER PROC dbo.queue_batchPost_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	select 'batchPost', 'readyToProcess', count(qi.itemid), min(qi.dateUpdated), 1
	from dbo.queue_batchPost as qi
	having count(qi.itemid) > 0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
