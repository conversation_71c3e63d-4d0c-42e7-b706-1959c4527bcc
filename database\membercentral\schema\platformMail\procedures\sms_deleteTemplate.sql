ALTER PROC dbo.sms_deleteTemplate
@templateID int,
@isTemplateInUse bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @isTemplateInUse = 0;


	IF @isTemplateInUse = 0
		UPDATE dbo.sms_usageTypeTemplates 
		SET status = 2
		WHERE templateID = @templateID;

	RETURN @isTemplateInUse;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
