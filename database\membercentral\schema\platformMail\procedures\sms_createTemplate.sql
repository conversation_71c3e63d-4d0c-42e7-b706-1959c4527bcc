ALTER PROCEDURE dbo.sms_createTemplate
@templateName varchar(200),
@templateDescription varchar(max),
@rawContent varchar(max),
@languageID int,
@isDefault int,
@isActive int,
@createdByMemberID int,
@siteID int,
@parentResourceType varchar(250),
@triggerID int,
@usageTypeID int,
@templateID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @usageID int, @appCreatedContentResourceTypeID int, @parentSiteResourceID int, @contentID int, @contentSiteResourceID int;
	select @appCreatedContentResourceTypeID = membercentral.dbo.fn_getResourceTypeId('ApplicationCreatedContent');
	select @parentSiteResourceID = membercentral.dbo.fn_getSiteResourceIDForResourceType(@parentResourceType, @siteID);
 
	BEGIN TRAN;
		EXEC membercentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID,
			@parentSiteResourceID=@parentSiteResourceID, @siteResourceStatusID=1, @isHTML=0,
			@languageID=@languageID, @isActive=1, @contentTitle=@templateName, @contentDesc='', @rawContent=@rawContent,
			@memberID=@createdByMemberID, @contentID=@contentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		INSERT INTO dbo.sms_usageTypeTemplates (siteID, usageTypeID, triggerID,templateName, templateDescription, contentID, 
			languageID, status, createdByMemberID,dateCreated)
		VALUES(@siteID, @usageTypeID, @triggerID, @templateName, @templateDescription, @contentID, @languageID, 
			@isActive, @createdByMemberID,GETDATE());

		SELECT @templateID = SCOPE_IDENTITY();
		
		SELECT TOP 1 @usageID = usageID FROM dbo.sms_subuserMessagingServiceUsages su
		WHERE su.siteID  = @siteID
		and su.usageTypeID = @usageTypeID;
		
		IF @isActive = 1 
		BEGIN
			INSERT INTO dbo.sms_subuserMessagingServiceUsageActiveTemplates (usageID, siteID, triggerID, templateID, 
				languageID, isDefault)
			VALUES(@usageID, @siteID, @triggerID, @templateID, @languageID, @isDefault);
		END

		SELECT @templateID;
		
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
