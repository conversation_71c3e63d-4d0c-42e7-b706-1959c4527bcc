ALTER PROC dbo.cms_createApplicationInstanceFastCaseRedirect
@siteid int,
@sectionID int,
@ssoCompany varchar(50),
@ssoOffset int,
@ssoMultiplier int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @applicationTypeID int = dbo.fn_getApplicationTypeIDFromName('fastCaseRedirect'), 
		@zoneID int = dbo.fn_getZoneID('Main'), @applicationInstanceID int, @siteResourceID int, @pageID int,
		@pgResourceTypeID int = dbo.fn_getResourceTypeId('ApplicationCreatedPage'), @settingsXML xml;
	SET @settingsXML = CAST(CONCAT('<settings><setting name="ssocompany" value="',@ssoCompany,'" /><setting name="ssooffset" value="',@ssoOffset,'" /><setting name="ssomultiplier" value="',@ssoMultiplier,'" /></settings>') as xml);

	BEGIN TRAN;
		EXEC dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=1, @sectionID=@sectionID, 
			@applicationTypeID=@applicationTypeID, @isVisible=1, @pageName='fastCaseRedirect', 
			@pageTitle='Redirect to FastCase', @pageDesc='Redirect to FastCase', @zoneID=@zoneID, 
			@pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, 
			@allowReturnAfterLogin=1, @applicationInstanceName='fastCaseRedirect', 
			@applicationInstanceDesc='Redirect to FastCase', @applicationInstanceID=@applicationInstanceID OUTPUT, 
			@siteresourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT;

		UPDATE dbo.cms_applicationInstances
		SET settingsXML = @settingsXML
		WHERE applicationInstanceID = @applicationInstanceID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
