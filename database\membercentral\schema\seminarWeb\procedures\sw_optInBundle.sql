ALTER PROC dbo.sw_optInBundle
@orgcode varchar(10),
@bundleID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @participantID int, @optedIntoBundle bit;

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID 
	FROM dbo.tblBundles as b
	INNER JOIN dbo.tblParticipants as p on p.participantID = b.participantID
	INNER JOIN membercentral.dbo.sites as mcs on mcs.siteCode = p.orgCode
	WHERE b.bundleID = @bundleID;

	SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode);

	BEGIN TRAN;
		IF NOT EXISTS (select bundleOptinID from dbo.tblBundlesOptIn where bundleID = @bundleID and participantID = @participantID) BEGIN
			INSERT INTO dbo.tblBundlesOptIn (bundleID, participantID, isPriceBasedOnActual, freeRateDisplay, dateAdded, isActive)
			VALUES (@bundleID, @participantID, 0, '$0.00', GETDATE(), 1);

			SET @optedIntoBundle = 1;
		END
		ELSE BEGIN 
			UPDATE dbo.tblBundlesOptIn
			SET isActive = 1
			WHERE bundleID = @bundleID
			AND participantID = @participantID
			AND isActive = 0;

			IF @@ROWCOUNT > 0
				SET @optedIntoBundle = 1;
		END

		IF @optedIntoBundle = 1 BEGIN
			EXEC dbo.sw_convertPriceSyndicationToRates @programID=@bundleID, @programType='SWB', 
				@restrictToParticipantIDList=@participantID, @recordedByMemberID=@recordedByMemberID;

			-- log
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"SW",
				"ORGID":' + cast(@orgID as varchar(10)) + ',
				"SITEID":' + cast(@siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('Association [' + tla.description + ' (' + p.orgcode + ')] opted into SWB-' + cast(@bundleID as varchar(10)) + '.'),'"','\"') + '" } }'
			FROM dbo.tblParticipants as p
			INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
			WHERE p.participantID = @participantID;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
