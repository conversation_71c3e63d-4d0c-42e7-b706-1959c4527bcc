ALTER PROC dbo.sponsors_deleteSponsorGrouping
@siteID int,
@sponsorGroupingID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Check if any sponsors are using this grouping
	IF EXISTS (SELECT sponsorUsageGroupingID FROM dbo.sponsorsUsageGrouping WHERE sponsorGroupingID = @sponsorGroupingID)
		RAISERROR('Cannot delete sponsor grouping that has sponsors assigned to it.',16,1);

	IF EXISTS (SELECT 1 FROM dbo.sponsorsGrouping WHERE sponsorGroupingID = @sponsorGroupingID AND siteID <> @siteID)
		RAISERROR('Invalid action.',16,1);

	DECLARE @referenceType varchar(20), @referenceID int;

	SELECT @referenceType = referenceType, @referenceID = referenceID
	FROM dbo.sponsorsGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	DELETE FROM dbo.sponsorsGrouping WHERE sponsorGroupingID = @sponsorGroupingID;

	-- reorder remaining groupings
	EXEC dbo.sponsors_reorderSponsorGroupings @siteID=@siteID, @referenceType=@referenceType, @referenceID=@referenceID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
