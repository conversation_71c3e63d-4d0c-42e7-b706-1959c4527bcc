ALTER PROC dbo.queue_addMCPayECheckTrans_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	EXEC dbo.queue_getQueueTypeID @queueType='addMCPayECheckTrans', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpMCPayCheckProfiles') IS NOT NULL
		DROP TABLE #tmpMCPayCheckProfiles;
	CREATE TABLE #tmpMCPayCheckProfiles (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpMCPayCheckProfiles
	FROM dbo.queue_addMCPayECheckTrans as qid
	INNER JOIN (
		SELECT TOP (@BatchSize) itemID 
		FROM dbo.queue_addMCPayECheckTrans
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.MPProfileID, mp.gatewayMerchantID, mp.gatewayUsername, mp.gatewayPassword, qid.dateFrom, qid.dateTo, qid.filterOptions, s.siteID, s.orgID
	FROM #tmpMCPayCheckProfiles AS tmp
	INNER JOIN dbo.queue_addMCPayECheckTrans AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.mp_profiles AS mp ON mp.profileID = qid.MPProfileID
	INNER JOIN membercentral.dbo.sites AS s ON s.siteID = mp.siteID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpMCPayCheckProfiles') IS NOT NULL
		DROP TABLE #tmpMCPayCheckProfiles;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
