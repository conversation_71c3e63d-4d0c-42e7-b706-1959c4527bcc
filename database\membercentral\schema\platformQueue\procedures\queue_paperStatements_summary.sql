ALTER PROC dbo.queue_paperStatements_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	INSERT INTO #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	SELECT 'paperStatements', qs.queueStatus, count(qi.itemID), min(psd.dateUpdated), 1
	FROM dbo.queue_paperStatements as qi
	INNER JOIN platformQueue.dbo.queue_paperStatementsDetail AS psd ON psd.itemID = qi.itemID
	INNER JOIN dbo.tblQueueStatuses as qs on qs.queuestatusID = psd.queuestatusID
	GROUP BY qs.queueStatus;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
