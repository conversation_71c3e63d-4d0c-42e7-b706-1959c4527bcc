ALTER PROC dbo.queue_subscriptionRenew_csvForNotification
@itemGroupUID uniqueidentifier,
@csvfilename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubRenewalCSV') IS NOT NULL 
		DROP TABLE #tmpSubRenewalCSV;
	CREATE TABLE #tmpSubRenewalCSV ([Member] varchar(400), MessageType varchar(20), [Message] varchar(max), itemID int, innerOrder int);
		
	INSERT INTO #tmpSubRenewalCSV ([Member], MessageType, [Message], itemID, innerOrder)
	select
		x.structNode.value('(var[@name="MEMBERNAME"]/string)[1]','varchar(400)') AS [Member],
		x.structNode.value('(var[@name="ERRTYPE"]/string)[1]','varchar(20)') AS [MessageType],
		x.structNode.value('(var[@name="ERRMESSAGE"]/string)[1]','varchar(max)') AS [Message],
		sr.itemID,
		ROW_NUMBER() OVER (PARTITION BY sr.itemID ORDER BY (SELECT 0)) AS structOrder
	from dbo.queue_subscriptionRenew as sr
	cross apply sr.wddxMessages.nodes('/wddxPacket/data/array/struct') AS x(structNode)
	where sr.itemGroupUID = @itemGroupUID;

	DECLARE @selectsql varchar(max) = 'SELECT [Member], MessageType, [Message], 
		ROW_NUMBER() OVER(order by itemID, innerOrder) as mcCSVorder 
		*FROM* #tmpSubRenewalCSV';
	EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

	IF OBJECT_ID('tempdb..#tmpSubRenewalCSV') IS NOT NULL 
		DROP TABLE #tmpSubRenewalCSV;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
