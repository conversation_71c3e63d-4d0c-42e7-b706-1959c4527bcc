ALTER PROC dbo.sw_getProgramSpeakersForBrowse
@seminarIDList varchar(1000),
@bundleIDList varchar(1000)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @speakerAuthorTypeID int;
	select @speakerAuthorTypeID = authorTypeID
	from dbo.tblAuthorTypes
	where authorType = 'Speaker';

	IF OBJECT_ID('tempdb..#tmpSeminarsForSpeakers') IS NOT NULL 
		DROP TABLE #tmpSeminarsForSpeakers;
	IF OBJECT_ID('tempdb..#tmpBundlesForSpeakers') IS NOT NULL 
		DROP TABLE #tmpBundlesForSpeakers;
	IF OBJECT_ID('tempdb..#tmpSpeakersForCatalog') IS NOT NULL 
		DROP TABLE #tmpSpeakersForCatalog;
	CREATE TABLE #tmpSeminarsForSpeakers (seminarID int);
	CREATE TABLE #tmpBundlesForSpeakers (bundleID int);
	CREATE TABLE #tmpSpeakersForCatalog (programID int, speakerName varchar(200), isSeminar bit, isBundle bit, authorOrder int);

	IF @seminarIDList NOT IN ('','0') BEGIN
		INSERT INTO #tmpSeminarsForSpeakers (seminarID)
		select listItem 
		from memberCentral.dbo.fn_intListToTable(@seminarIDList,',');

		-- SWL and SWOD Seminars
		INSERT INTO #tmpSpeakersForCatalog (programID, speakerName, isSeminar, isBundle, authorOrder)
		SELECT s.seminarID, 
			LTRIM(RTRIM(a.firstName + ' ' + a.lastName + CASE WHEN LEN(LTRIM(RTRIM(a.suffix))) > 0 THEN ', ' + LTRIM(RTRIM(a.suffix)) ELSE '' END)) as speakerName,
			1, 0, saa.authorOrder
		FROM #tmpSeminarsForSpeakers as tmp
		INNER JOIN dbo.tblSeminars as s on s.seminarID = tmp.seminarID
		INNER JOIN dbo.tblSeminarsAndAuthors as saa on saa.seminarID = s.seminarID
		INNER JOIN dbo.tblAuthors as a on a.authorID = saa.authorID
			AND a.authorTypeID = @speakerAuthorTypeID
		WHERE a.displayOnWebsite = 1;
	END

	IF @bundleIDList NOT IN ('','0') BEGIN
		INSERT INTO #tmpBundlesForSpeakers (bundleID)
		select listItem 
		from memberCentral.dbo.fn_intListToTable(@bundleIDList,',');

		-- SWB Bundles - Get unique speakers from all bundled seminars
		INSERT INTO #tmpSpeakersForCatalog (programID, speakerName, isSeminar, isBundle, authorOrder)
		SELECT DISTINCT b.bundleID,
			LTRIM(RTRIM(a.firstName + ' ' + a.lastName + CASE WHEN LEN(LTRIM(RTRIM(a.suffix))) > 0 THEN ', ' + LTRIM(RTRIM(a.suffix)) ELSE '' END)) as speakerName,
			0, 1, MIN(saa.authorOrder) as authorOrder
		FROM #tmpBundlesForSpeakers as tmp
		INNER JOIN dbo.tblBundles as b on b.bundleID = tmp.bundleID
		INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = b.bundleID
		INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
		INNER JOIN dbo.tblSeminarsAndAuthors as saa on saa.seminarID = s.seminarID
		INNER JOIN dbo.tblAuthors as a on a.authorID = saa.authorID
			AND a.authorTypeID = @speakerAuthorTypeID
		WHERE a.displayOnWebsite = 1
		GROUP BY b.bundleID, a.firstName, a.lastName, a.suffix;
	END

	select programID, speakerName, isSeminar, isBundle, authorOrder
	FROM #tmpSpeakersForCatalog
	ORDER BY programID, authorOrder, speakerName;

	IF OBJECT_ID('tempdb..#tmpSeminarsForSpeakers') IS NOT NULL 
		DROP TABLE #tmpSeminarsForSpeakers;
	IF OBJECT_ID('tempdb..#tmpBundlesForSpeakers') IS NOT NULL 
		DROP TABLE #tmpBundlesForSpeakers;
	IF OBJECT_ID('tempdb..#tmpSpeakersForCatalog') IS NOT NULL 
		DROP TABLE #tmpSpeakersForCatalog;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
