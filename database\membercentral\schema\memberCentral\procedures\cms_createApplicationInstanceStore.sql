ALTER PROC dbo.cms_createApplicationInstanceStore
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@pageModeID int,
@pgResourceTypeID int,
@defaultGLAccountID int,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @rc int, @applicationTypeID int, @appCreatedContentResourceTypeID int, @maincontentID int,
		@maincontentSiteResourceID int,  @footerContentID int,  @footerContentSiteResourceID int, @documentSectionName varchar(50),
		@appCreatedSectionResourceTypeID int, @rootSectionID int, @orgName varchar(100), @hostName varchar(80),
		@scheme varchar(10), @environmentID int, @crlf varchar(10), @emailFooterContentHTML varchar(250), @defaultOrgIdentityID int;

	select @applicationInstanceID = null
	select @siteResourceID = null
	select @pageID = null
	select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
	select @applicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Store'
	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')

	BEGIN TRAN;
		-- create instance
		EXEC dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
				@applicationTypeID=@applicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
				@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
				@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, 
				@allowReturnAfterLogin=@allowReturnAfterLogin, @applicationInstanceName=@applicationInstanceName, 
				@applicationInstanceDesc=@applicationInstanceDesc, @applicationInstanceID=@applicationInstanceID OUTPUT, 
				@siteresourceID=@siteResourceID OUTPUT, 
				@pageID=@pageID OUTPUT

		-- create mainContent
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='Welcome to the store.<br/><br/>Click on the links to the left to browse the catalog.<br/><br/>Please note that applicable taxes and shipping charges will be added to purchases.', 
			@memberID=NULL,
			@contentID=@maincontentID OUTPUT, @siteResourceID=@maincontentSiteResourceID OUTPUT

		-- create footerContent
		SELECT @environmentID = environmentID FROM platform_environments WHERE environmentName = 'production';
		SET @crlf = CHAR(13) + CHAR(10);
		SELECT  @hostName = sh.HostName,   @orgName=oi.organizationName
			, @scheme=case when sh.hasssl = 1 then 'https' else 'http' end, @defaultOrgIdentityID = o.defaultOrgIdentityID

			FROM  dbo.sites s
			inner join dbo.organizations as o on o.orgid = s.orgid
			inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = s.defaultOrgIdentityID
			inner join dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
			inner join dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
			where s.siteid = @siteID
			SET  @emailFooterContentHTML = 'If you have any questions regarding your order, contact us at '+@scheme+'://'+@hostName+'.
			'+@crlf +@crlf +@orgName;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=0, @languageID=@languageID, 
			@isActive=1, @contentTitle='StoreMailFooter', @contentDesc=null, @rawContent=@emailFooterContentHTML,
			@memberID=NULL,
			@contentID=@footerContentID OUTPUT, @siteResourceID=@footerContentSiteResourceID OUTPUT	
			
		-- setup a section to hold downloadable content
		select @documentSectionName = 'StoreDocs ' + cast(@applicationInstanceID as varchar(8))

		exec dbo.cms_createPageSection
				@siteID = @siteID, 
				@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
				@ovTemplateID = NULL,
				@ovTemplateIDMobile=NULL,
				@ovModeID = NULL, 
				@parentSectionID = @sectionID, 
				@sectionName = @documentSectionName, 
				@sectionCode = @documentSectionName,
				@sectionBreadcrumb = NULL,
				@inheritPlacements = 1,
				@sectionID = @rootSectionID OUTPUT
			

		-- update parentSiteResourceID of section
		update sr
		set sr.parentSiteResourceID = @siteResourceID
		from dbo.cms_pageSections s
		inner join dbo.cms_siteResources sr on s.siteResourceID = sr.siteResourceID
			and s.sectionID = @rootSectionID;

		-- add store
		INSERT INTO dbo.store (siteID, applicationInstanceID, mainContentID, showProductID, MaxRecords, showCartThumbnails, rootSectionID, GLAccountID, shippingGLAccountID, orderReceiptFooterContentID, orgIdentityID)
		VALUES (@siteid, @applicationInstanceID, @maincontentID, 0, 25, 0, @rootSectionID, @defaultGLAccountID, @defaultGLAccountID, @footerContentID, @defaultOrgIdentityID)
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
