﻿<cfset local.printMode = false>
<cfif arguments.event.valueExists('printmode') and arguments.event.getValue('printmode','') eq "pdf">
	<cfset local.printMode = true>
</cfif>

<cfif local.printMode>
	<cfsavecontent variable="local.js">
		<cfoutput>
		<script language="javascript">
			$(function(){
				$(".notReg_evCredit").hide();
				$(".notReg_evCredit_noneMsg").show();
				$(".reg_evCredit_noneMsg").hide();
				window.print();
			});
		</script>
		</cfoutput>
	</cfsavecontent>
<cfelse>
	<cfsavecontent variable="local.js">
		<cfoutput>
		<script language="javascript">
		$(document).ready(function(){			
			$(".notReg_evCredit").hide();
			$(".notReg_evCredit_noneMsg").show();
			$(".reg_evCredit_noneMsg").hide();
			
			$(".attended").click(function(event) {
				var thisID = event.target.id;
				thisID = thisID.replace("attendedList_", "");

				if($("##" + thisID).is(":visible")){
					$("##" + thisID).hide();
					$("##" + thisID + "_noneMsg").show();
				} else {
					$("##" + thisID).show();
					$("##" + thisID + "_noneMsg").hide();
				}
			});
			
			$("form :input").change(function() {
				$("##AC_err_div").show();
				hideAlert();
			});
		});
		
		function doQuickAct() {
			$('##trRemoveCredit').hide();
			switch ($('##frmAttendanceCredit ##selQuickAct').val()) {
				<cfif local.isMaster>
					case 'markAllAttended': markAllAttended(); break;
					case 'markNoneAttended': markNoneAttended(); break;
					case 'removeCredit': removeCredit(); break;
				</cfif>
			}
		};
		
		<cfif local.isMaster>
			function markAllAttended() {
				$('.attended').each(function() {
					this.checked = true;
					var id = $(this).attr('id');
					var thisID = id.replace("attendedList_", "");
					$("##" + thisID).show();
					$("##" + thisID + "_noneMsg").hide();
				});
			};
			
			function markNoneAttended() {
				$('.attended').each(function() {
					this.checked = false;
					var id = $(this).attr('id');
					var thisID = id.replace("attendedList_", "");
					$("##" + thisID).hide();
					$("##" + thisID + "_noneMsg").show();
				});
			};

			function removeCredit() {
				$('##trRemoveCredit').show();
			};
		</cfif>

		function validateForm() {
			var numRegex = /^\d{0,3}(\.\d{1,2})?$/;
			var returnForm = true;
			hideAlert();

			var selectedAction = $('##frmAttendanceCredit ##selQuickAct').val();
			switch (selectedAction) {
				case 'removeCredit':
					var offeringTypeID = $('##sel_remove_offeringTypeID').val();
					if (!offeringTypeID || offeringTypeID == 0) {
						showAlert('Select which credit should be removed.');
						return false;
					}
					break;
			}
			
			var arrReq = [];

			$('.creditValue').each(function() { 
				if( $.trim(this.value).length == 0 || ($.trim(this.value).length > 0 && !numRegex.test($.trim(this.value)) ) ) {
					arrReq[arrReq.length] = this.value;
					returnForm = false;
				}
			});

			if(arrReq.length) {
				var errMsg = 'Enter valid numbers for credits. Only positive decimals are allowed (in the format "99.99").<br/>The following values are invalid: ' + arrReq.join(', ');
				showAlert(errMsg);
			}
			else 
				hideAlert();
			
			return returnForm;
		};

		function hideAlert() { $('##validate_err_msg').html('').addClass('d-none'); };
		function showAlert(msg) { $('##validate_err_msg').html(msg).removeClass('d-none'); };

		function printList() {
			<cfif arguments.event.valueExists('_rid')>
				window.open('#replaceNoCase(this.link.manageSubEventAttendanceCredit,"&mode=stream","&mode=direct")#&eid=#arguments.event.getValue('eID')#&_rid=#arguments.event.getValue('_rid')#&printmode=pdf');
			<cfelse>
				window.open('#replaceNoCase(this.link.manageSubEventAttendanceCredit,"&mode=stream","&mode=direct")#&eid=#arguments.event.getValue('eID')#&printmode=pdf');
			</cfif>
		};
		</script>
		<style type="text/css">
			table.tableSection > thead { display:block; }
			table.tableSection > tbody { display:block; height:300px; overflow-y:auto; overflow-x:hidden; }
		</style>
		</cfoutput>
	</cfsavecontent>
</cfif>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<form name="frmAttendanceCredit" id="frmAttendanceCredit" class="p-2" method="POST" action="#this.link.saveSubEventAttendanceCredit#">
	<input type="hidden" name="eid" id="eid" value="#arguments.event.getValue('eID')#">
	<input type="hidden" id="registrantList" name="registrantList" value="#valueList(local.qryNonBlankRegistrants.eventRegAttendIDs)#">
	<input type="hidden" id="eventList" name="eventList" value="#valueList(local.qryRegistrants.eventID)#">
	<input type="hidden" name="_rid" id="_rid" value="#arguments.event.getValue('_rid',0)#">
	<input type="hidden" name="mid" id="mid" value="#local.qryRegistrants.regMemberID#">

	<cfif local.printMode>
		<h4>#local.strEvent.qryEventMeta.eventContentTitle#</h4>
	<cfelse>
		<span class="mr-2 float-right">
			<a href="javascript:printList();"><i class="fa-regular fa-print"></i> Print List</a>
		</span>
		<h5 class="mb-3">#local.qryRegistrants.memberName#</h5>
	</cfif>
	
	<cfif not local.printMode>
		<cfset local.tmpCredits = xmlParse(local.qryRegistrants.creditsXML)>
		<cfset local.tmpCredits = local.tmpCredits.xmlRoot>

		<div class="row mx-0 no-gutters">
			<div class="col">
				<div class="form-group row no-gutters">
					<cfif local.isMaster>
						<div class="col-sm-6">
							<select name="selQuickAct" id="selQuickAct" class="form-control form-control-sm" onChange="doQuickAct();">
								<option value="">Select a quick action</option>
								<option value="markAllAttended">Mark All Attended</option>
								<option value="markNoneAttended">Mark None Attended</option>
								<option value="removeCredit">Remove Credit</option>
							</select>
						</div>
					</cfif>
					<div class="col-sm text-right">
						<button id="btnSaveChanges" type="submit" class="btn btn-sm btn-primary" onClick="return validateForm();">Save Changes</button>
					</div>
				</div>
				<div class="form-group row mt-3" id="trRemoveCredit" style="display:none;">
					<label for="sel_remove_offeringTypeID" class="col-sm-12">Remove awarded credit for:</label>
					<div class="col-sm-12">
						<cfif arrayLen(local.tmpCredits.xmlChildren)>
							<select name="sel_remove_offeringTypeID" id="sel_remove_offeringTypeID" class="form-control form-control-sm">
								<option value="0"></option>
								<cfloop array="#local.tmpCredits.xmlChildren#" index="local.thiscredit">
									<option value="#local.thiscredit.xmlAttributes.offeringTypeID#">#local.thiscredit.xmlAttributes.creditType# - #local.thiscredit.xmlAttributes.authority#</option>
								</cfloop>
							</select>
						</cfif>
					</div>
				</div>
				<div id="AC_err_div" class="alert alert-warning mt-3 mb-0 font-weight-bold" style="display:none;">
					<div id="validate_err_msg" class="mb-2 d-none"></div>
					Remember to save your changes by clicking the <em>Save Changes</em> button.
				</div>
			</div>
		</div>
	</cfif>
	
	<table class="table table-sm mt-4">
	<thead>
		<tr>
			<th>Event</th>
			<th class="text-center">Registered</th>
			<th class="text-center">Attendance</th>
			<th>Credits  / <span style="color:green;">Awarded</span></th>
		</tr>
	</thead>
	<tbody>
	<cfloop query="local.qryRegistrants">
		<cfset local.tmpEventCredits = xmlParse(local.qryRegistrants.eventCreditsXML).xmlRoot>
		<cfset local.tmpAssignedCredits = xmlParse(local.qryRegistrants.creditsXML).xmlRoot>
		<cfset local.hasEventCredits = len(trim(local.qryRegistrants.eventCreditsXML)) gt 0>

		<tr>
			<td width="30%" class="align-top">
				#encodeForHTML(local.qryRegistrants.eventTitle)#<br/>
				<cfif len(local.qryRegistrants.eventSubTitle)>#encodeForHTML(local.qryRegistrants.eventSubTitle)#<br/></cfif>
				<span class="text-muted text-muted font-weight-bold font-size-sm">
					#dateFormat(local.qryRegistrants.startTime, "m/d/yy")#
				</span>
			</td>
			<td class="align-top text-center">#yesNoFormat(local.qryRegistrants.isRegistered)#</td>
			<td class="align-top text-center" nowrap>
				<input type="checkbox" name="attendedList" id="attendedList_#iif(local.qryRegistrants.attended,de("reg_evCredit_#local.qryRegistrants.eventID#"),de("notReg_evCredit_#local.qryRegistrants.eventID#"))#" class="attended" value="#local.qryRegistrants.eventID#" <cfif local.qryRegistrants.attended eq 1>checked</cfif>>
			</td>
			<td class="align-top">
				<cfif local.hasEventCredits>
					<cfset local.thisIDPrefix = local.qryRegistrants.attended ? "reg_evCredit" : "notReg_evCredit">

					<div id="#local.thisIDPrefix#_#local.qryRegistrants.eventID#" class="#local.thisIDPrefix#">
						<table class="table table-sm table-borderless">
						<cfloop array="#local.tmpEventCredits.xmlChildren#" index="local.thisEventCredit">
							<tr>
								<!--- is event credit assigned --->
								<cfset var currentOfferingTypeID = local.thisEventCredit.xmlAttributes.offeringTypeID>
								<cfset local.arrAssignedCreditsMatch = arrayFilter(local.tmpAssignedCredits.xmlChildren, function(child) {
										return child.xmlAttributes.offeringTypeID EQ currentOfferingTypeID;
									}
								)>

								<cfset local.isCreditAwarded = 0>
								<cfif arrayLen(local.arrAssignedCreditsMatch)>
									<cfset local.thisAssignedCredit = arrayFirst(local.arrAssignedCreditsMatch).xmlAttributes>
									<cfset local.isCreditAwarded = local.thisAssignedCredit.creditAwarded>
									<cfif local.isCreditAwarded>
										<td class="font-weight-bold font-size-sm align-top" width="10%">
											<input type="text" name="creditValueAwarded_#currentOfferingTypeID#_new" id="creditValueAwarded_#currentOfferingTypeID#_new" class="form-control form-control-sm creditValue font-size-sm" value="#local.thisAssignedCredit.creditValueAwarded#" style="width:85px;" maxlength="6" onBlur="this.value=formatCurrency(this.value);">
											<input type="hidden" name="creditValueAwarded_#currentOfferingTypeID#_old"  id="creditValueAwarded_#currentOfferingTypeID#_old" value="#local.thisAssignedCredit.creditValueAwarded#">
											<cfset local.regOfferingTypeIDList = listAppend(local.regOfferingTypeIDList,"#local.qryRegistrants.eventID#|#local.qryRegistrants.registrantID#|#currentOfferingTypeID#")> 
										</td>
									<cfelse>
										<td width="10%" class="font-size-sm align-top"><cfif local.thisAssignedCredit.addedViaAward is not 1>Req<cfelse>n/a</cfif></td>
									</cfif>
								<cfelse>
									<td class="font-weight-bold font-size-sm align-top" width="10%">
										<input type="text" name="notReg_creditValueAwarded_#currentOfferingTypeID#_new" id="notReg_creditValueAwarded_#currentOfferingTypeID#_new" class="form-control form-control-sm creditValue font-size-sm"
											<!--- value 0 for the ones with attended = 1 but not awarded credits, otherwise default credit values --->
											<cfif local.qryRegistrants.attended>
												value="0.00"
											<cfelse>
												value="#local.thisEventCredit.xmlAttributes.creditValue#"
											</cfif>
											style="width:85px;" maxlength="6" onBlur="this.value=formatCurrency(this.value);">
										<cfset local.newRegOfferingTypeIDList = listAppend(local.newRegOfferingTypeIDList,"#local.qryRegistrants.eventID#|#val(local.qryRegistrants.registrantID)#|#currentOfferingTypeID#")>
									</td>
								</cfif>

								<td class="font-size-sm align-top<cfif local.isCreditAwarded> font-weight-bold text-green</cfif>">#local.thisEventCredit.xmlAttributes.creditType# - #local.thisEventCredit.xmlAttributes.authority#</td>
							</tr>
						</cfloop>
						</table>
					</div>
					<div id="#local.thisIDPrefix#_#local.qryRegistrants.eventID#_noneMsg" class="#local.thisIDPrefix#_noneMsg">
						<span class="p-1">None</span>
					</div>
				<cfelse>
					<span class="p-1">None</span>
				</cfif>
			</td>
		</tr>
	</cfloop>
	</tbody>
	</table>
	<input type="hidden" name="regOfferingTypeIDList" id="regOfferingTypeIDList" value="#local.regOfferingTypeIDList#">
	<input type="hidden" name="newRegOfferingTypeIDList" id="newRegOfferingTypeIDList" value="#local.newRegOfferingTypeIDList#">
</form>
</cfoutput>