ALTER PROC dbo.ev_importEvents_prepTable
@siteid int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @eventAdminSiteResourceID int, @crossEventFieldUsageID int, @recurringEvents bit, 
		@ASID int, @crdAuthorityCode varchar(20), @creditColName varchar(50);
	set @importResult = null;

	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	select @eventAdminSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EventAdmin',@siteID);
	select @crossEventFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Event',null);

	SELECT @recurringEvents = recurringEvents
	FROM dbo.siteFeatures 
	WHERE siteID = @siteID;

	insert into #tblPossibleCredits (ASID, authorityID, authorityCode)
	select distinct crdAS.ASID, crdA.authorityID, crdA.authorityCode
	from dbo.crd_sponsors as crdS
	inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
	inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
	where crdS.orgID = @orgID;

	insert into #tblPossibleCreditCols (ASID, column_name, ASTID)
	select crdAS.ASID, crdAS.authorityCode + '_' + crdAT.typeCode, crdAST.ASTID
	from #tblPossibleCredits as crdAS
	inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdAS.authorityID
	inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID;

	insert into #tblEvImportCustomCols (fieldID, columnName, isRequired, requiredMsg, displayTypeCode, dataTypeCode)
	select f.fieldID, f.fieldReference, f.isRequired, f.requiredMsg, ft.displayTypeCode, ft.dataTypeCode
	from dbo.cf_fields as f
	inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
	inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
	where fu.usageID = @crossEventFieldUsageID
	and f.controllingSiteResourceID = @eventAdminSiteResourceID
	and len(f.fieldReference) > 0
	and f.isActive = 1
	and ft.displayTypeCode <> 'LABEL'
	and 1 = case when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
				else 1 end
	order by f.fieldOrder;

	-- *********************************
	-- ensure all required columns exist 
	-- *********************************
	BEGIN TRY
		-- this will get the columns that are required
		IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
			DROP TABLE #tblEvOrgCols;
		CREATE TABLE #tblEvOrgCols (COLUMN_NAME sysname);

		insert into #tblEvOrgCols
		select 'rowID' union all
		select 'Calendar' union all
		select 'EventTitle' union all
		select 'EventCode' union all
		select 'EventCategory' union all
		select 'EventStart' union all
		select 'EventEnd' union all
		select 'RegistrationReplyEmail';

		-- this will get the columns that are actually in the import
		IF OBJECT_ID('tempdb..#tblEvImportCols') IS NOT NULL 
			DROP TABLE #tblEvImportCols;
		CREATE TABLE #tblEvImportCols (ORDINAL_POSITION int, COLUMN_NAME sysname);

		insert into #tblEvImportCols
		select column_id, [name] 
		from tempdb.sys.columns 
		where object_id = object_id('tempdb..#mc_EvImport');

		INSERT INTO #tblEvErrors (msg)
		select 'The required column ' + org.column_name + ' is missing from your data.'
		from #tblEvOrgCols as org
		left outer join #tblEvImportCols as imp on imp.column_name = org.column_name
		where imp.ORDINAL_POSITION is null;
			IF @@ROWCOUNT > 0 GOTO on_done;

		delete from #tblEvImportCols 
		where column_name in (select COLUMN_NAME from #tblEvOrgCols);

		delete from #tblEvImportCols 
		where column_name in (select columnName from #tblEvImportCustomCols);

		IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
			DROP TABLE #tblEvOrgCols;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate file contains all required columns.');

		GOTO on_done;
	END CATCH

	-- **********
	-- prep table 
	-- **********
	BEGIN TRY
		-- add holding columns
		ALTER TABLE #mc_EvImport ADD MCCalendarID int null, MCEventID int null, MCCategoryIDList varchar(max) null, 
			MCRegistrationID int null, MCParentEventID int null, MCParentRowID int null, MCRecurrenceOrder int,
			itemUID uniqueidentifier NOT NULL DEFAULT(NEWID());

		-- ensure rowID is an int
		ALTER TABLE #mc_EvImport ALTER COLUMN RowID int not null;

		-- add missing columns
		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventHidden') BEGIN
			ALTER TABLE #mc_EvImport ADD EventHidden bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventHidden');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EventHidden';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventAllDay') BEGIN
			ALTER TABLE #mc_EvImport ADD EventAllDay bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventAllDay');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EventAllDay';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'DisplayCredits') BEGIN
			ALTER TABLE #mc_EvImport ADD DisplayCredits bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('DisplayCredits');
		END ELSE
			delete from #tblEvImportCols where column_name = 'DisplayCredits';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ContactInclude') BEGIN
			ALTER TABLE #mc_EvImport ADD ContactInclude bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ContactInclude');
		END ELSE
			delete from #tblEvImportCols where column_name = 'ContactInclude';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'LocationInclude') BEGIN
			ALTER TABLE #mc_EvImport ADD LocationInclude bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('LocationInclude');
		END ELSE
			delete from #tblEvImportCols where column_name = 'LocationInclude';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'CancellationInclude') BEGIN
			ALTER TABLE #mc_EvImport ADD CancellationInclude bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('CancellationInclude');
		END ELSE
			delete from #tblEvImportCols where column_name = 'CancellationInclude';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'TravelInclude') BEGIN
			ALTER TABLE #mc_EvImport ADD TravelInclude bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('TravelInclude');
		END ELSE
			delete from #tblEvImportCols where column_name = 'TravelInclude';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventDescription') BEGIN
			ALTER TABLE #mc_EvImport ADD EventDescription varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventDescription');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EventDescription';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EnableRealTimeRoster') BEGIN
			ALTER TABLE #mc_EvImport ADD EnableRealTimeRoster bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EnableRealTimeRoster');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EnableRealTimeRoster';
		
		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ContactTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD ContactTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ContactTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'ContactTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Contact') BEGIN
			ALTER TABLE #mc_EvImport ADD Contact varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Contact');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Contact';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'LocationTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD LocationTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('LocationTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'LocationTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Location') BEGIN
			ALTER TABLE #mc_EvImport ADD Location varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Location');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Location';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'CancellationTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD CancellationTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('CancellationTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'CancellationTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Cancellation') BEGIN
			ALTER TABLE #mc_EvImport ADD Cancellation varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Cancellation');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Cancellation';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'TravelTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD TravelTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('TravelTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'TravelTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Travel') BEGIN
			ALTER TABLE #mc_EvImport ADD Travel varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Travel');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Travel';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'InformationTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD InformationTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('InformationTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'InformationTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Information') BEGIN
			ALTER TABLE #mc_EvImport ADD Information varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Information');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Information';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'InternalNotes') BEGIN
			ALTER TABLE #mc_EvImport ADD InternalNotes varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('InternalNotes');
		END ELSE
			delete from #tblEvImportCols where column_name = 'InternalNotes';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ParentEventCode') BEGIN
			ALTER TABLE #mc_EvImport ADD ParentEventCode varchar(15) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ParentEventCode');
		END ELSE
			delete from #tblEvImportCols where column_name = 'ParentEventCode';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'RecurringSeriesCode') BEGIN
			ALTER TABLE #mc_EvImport ADD RecurringSeriesCode varchar(15) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RecurringSeriesCode');
		END ELSE
			delete from #tblEvImportCols where column_name = 'RecurringSeriesCode';

		-- add missing credit columns
		select @ASID = min(ASID) from #tblPossibleCredits;
		while @ASID is not null begin
			select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID;

			IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @crdAuthorityCode + '_approval') BEGIN
				EXEC('ALTER TABLE #mc_EvImport ADD ' + @crdAuthorityCode + '_approval varchar(50) NULL;');
				INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@crdAuthorityCode + '_approval');
				update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0;
			END ELSE BEGIN
				delete from #tblEvImportCols where column_name = @crdAuthorityCode + '_approval';
				update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0;
			END

			IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @crdAuthorityCode + '_status') BEGIN
				EXEC('ALTER TABLE #mc_EvImport ADD ' + @crdAuthorityCode + '_status varchar(15) NULL;');
				INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@crdAuthorityCode + '_status');
				update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0;
			END ELSE BEGIN
				delete from #tblEvImportCols where column_name = @crdAuthorityCode + '_status';
				update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0;
			END

			set @creditColName = null;
			select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID;
			while @creditColName is not null begin
				IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @creditColName) BEGIN
					EXEC('ALTER TABLE #mc_EvImport ADD ' + @creditColName + ' decimal(6,2) NULL;');
					INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@creditColName);
					update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0;
				END ELSE BEGIN
					delete from #tblEvImportCols where column_name = @creditColName;
					update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0;
				END
				select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID and column_name > @creditColName;
			end

			select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID;
		end

		-- all credit columns must be included if one column is included
		INSERT INTO #tblEvErrors (msg)
		SELECT 'Missing credit columns for authority ' + authorityCode + '. If one credit column for an authority is included, all columns for that authority must be included.'
		FROM #tblPossibleCredits
		WHERE anyInFile = 1 and missingFromFile = 1;
			IF @@ROWCOUNT > 0 GOTO on_done;

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to prepare import table by adding missing columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- *************
	-- extra columns 
	-- *************
	BEGIN TRY
		-- extra columns should stop import to prevent accidental misnamed columns
		IF EXISTS (select column_name from #tblEvImportCols) BEGIN
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'The imported file contains the extra column ' + cast(column_name as varchar(300)) + '. Remove this column from your file.' 
			FROM #tblEvImportCols  
			ORDER BY column_name;
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate import file for extra columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ***************************************
	-- keep only custom columns in import file
	-- ***************************************
	BEGIN TRY
		IF EXISTS (select columnName from #tblEvImportCustomCols) BEGIN
			delete from #tblEvImportCustomCols
			where columnName not in (select [name] from tempdb.sys.columns where object_id = object_id('tempdb..#mc_EvImport'));
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to prepare import table for custom columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
		DROP TABLE #tblEvOrgCols;
	IF OBJECT_ID('tempdb..#tblEvImportCols') IS NOT NULL 
		DROP TABLE #tblEvImportCols;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
