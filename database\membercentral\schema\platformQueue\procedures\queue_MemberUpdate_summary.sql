ALTER PROC dbo.queue_MemberUpdate_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	select 'memberImport', qs.queueStatus+'/'+qs2.queueStatus, count(qid.memberID), min(qi.dateEntered), 0
	from dbo.memimport_jobs as qi
	inner join dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID
	inner join dbo.memimport_members as qid on qid.jobID = qi.jobID
	inner join dbo.tblQueueStatuses as qs2 on qs2.queuestatusID = qid.queuestatusID
	group by qs.queueStatus+'/'+qs2.queueStatus;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
