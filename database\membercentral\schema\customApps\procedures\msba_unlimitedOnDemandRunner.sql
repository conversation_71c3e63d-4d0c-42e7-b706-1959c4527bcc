ALTER PROC dbo.msba_unlimitedOnDemandRunner
@xmlData xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	/*
	This proc is only called from the mchooks activation proc, which starts in snapshot so we can go in and out of snapshot.
	The ams_addMemberHistory proc needs to be run in this mode.
	*/

	declare @orderID int, @nowDateTime datetime = getdate(), @nowDate date, @siteID int, @orgID int, @siteCode varchar(10); 
	select @orderID = @xmlData.value('(/h/orderid/text())[1]','int');
	set @nowDate = @nowDateTime;
	DECLARE @endDate DATETIME = DATEADD(YEAR, 1, @nowDate);

	select @siteID = siteID, @orgID = orgID, @siteCode = siteCode
	from membercentral.dbo.sites 
	where siteCode = 'MSBA';

	/*
	has the order been paid in full? If not, exit.
	*/
	declare @amountDue decimal(18,2);

	select @amountDue = sum(ts.cache_amountAfterAdjustment)-sum(ts.cache_activePaymentAllocatedAmount)
	from membercentral.dbo.fn_store_orderTransactions(@orgID,@orderID) as rt
	inner join membercentral.dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = rt.transactionID;

	if @amountDue > 0 goto on_done;

	/*
	are there UnlimitedOnDemand items in the order? if not, exit.
	*/
	declare @ApplicationTypeID int, @storeSiteResourceID int, @memberID int, @orderNumber varchar(50), 
		@storeID int, @itemID int;
	declare @tblOrderItems TABLE (orderDetailID int PRIMARY KEY, itemAmount decimal(18,2));

	select @ApplicationTypeID = membercentral.dbo.fn_getApplicationTypeIDFromName('Store');

	select @storeSiteResourceID = ai.siteResourceID, @memberID = m.activeMemberID, @orderNumber = o.orderNumber, @storeID = s.storeID
	from membercentral.dbo.store_Orders as o
	inner join membercentral.dbo.store as s on s.storeID = o.storeID
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceID = s.applicationInstanceID
	inner join membercentral.dbo.ams_members as m on m.memberID = o.memberID
	where o.orderID = @orderID;

	select TOP 1 @itemID = itemID
	from membercentral.dbo.store_products
	where storeID = @storeID
	and productID = 'UnlimitedOnDemand'
	and [status] = 'A';

	insert into @tblOrderItems (orderDetailID, itemAmount)
	select od.orderDetailID, tsFull.cache_amountAfterAdjustment as itemAmount
	from membercentral.dbo.store_orderDetails as od
	inner join membercentral.dbo.tr_applications as ta on ta.orgID = @orgID
		and ta.applicationTypeID = @ApplicationTypeID
		and ta.itemType = 'Product'
		and ta.itemID = od.orderDetailID
		and ta.[status] = 'A'
	cross apply membercentral.dbo.fn_tr_transactionSalesWithDIT(@orgID,ta.transactionID) as tsFull
	where od.orderID = @orderID
	and od.productItemID = @itemID;
		if @@rowcount = 0 goto on_done;

	/*
	has the order already been processed? if so, exit.
	*/
	declare @RTID int, @usageTypeID int, @historyID int;
	
	select @RTID = membercentral.dbo.fn_getResourceTypeID('store');

	select @usageTypeID = usageTypeID 
	from membercentral.dbo.ams_memberHistoryUsageTypes 
	where resourceTypeID = @RTID 
	and usageType = 'order' 
	and status = 'A';

	select top 1 @historyID = historyID
	from membercentral.dbo.ams_memberHistoryUsages
	where usageTypeID = @usageTypeID
	and itemID = @orderID
	and [status] = 'A';

	if @historyID > 0 goto on_done;


	/*
	at this point, we are going to record payments and history.
	get data needed for payments and history entry.
	*/
	declare @categoryTreeID int, @categoryID int, @enteredByMemberID int, 
		@historyDesc varchar(100), @mpprofileID int, @mpgatewayID int, @debitGLAccountID int, @mpprofileCode varchar(20), 
		@debitGLAccountName varchar(200), @totalAwardedamount decimal(18,2), @totalawardedCount int, @orderDetailID int, @awardedamount decimal(18,2),
		@paymentHistoryID int, @payTransactionID int, @newMemberHistoryID int;
	
	select @categoryTreeID = categoryTreeID 
	from membercentral.dbo.cms_categoryTrees
	where siteID = @siteID
	and categoryTreeName = 'MemberHistoryTypes';

	select @categoryID = categoryID								
	from membercentral.dbo.cms_categories
	where categoryTreeID = @categoryTreeID
	and categoryCode = 'CLEPass'
	and isActive = 1;

	select @enteredByMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);

	set @historyDesc = 'Unlimited CLE Pass purchased for store order ' + @siteCode + right('0000' + cast(@orderID as varchar(6)),5);

	select @mpprofileID = p.profileID, @mpgatewayID = p.gatewayID, @debitGLAccountID = gl.GLAccountID,
		@mpprofileCode = p.profileCode, @debitGLAccountName = gl.accountName
	from membercentral.dbo.mp_profiles as p
	inner join membercentral.dbo.tr_glaccounts as gl on gl.orgID = @orgID and gl.glaccountID = p.GLAccountID
	where p.siteID = @siteID
	and p.profileCode = 'MSBA_CC1'
	and p.[status] = 'A';

	select @totalAwardedamount = sum(itemAmount) from @tblOrderItems;
	select @totalawardedCount = count(orderDetailID) from @tblOrderItems;

	BEGIN TRAN;

		EXEC membercentral.dbo.ams_addMemberHistory @typeID=1, @memberID=@memberID, @categoryID=@categoryID, 
			@subCategoryID=null, @userDate=@nowDate, @userEndDate=@endDate, @qty=@totalawardedCount, 
			@dollarAmt=@totalAwardedamount, @description=@historyDesc, @linkMemberID=null, 
			@usageCSRID=@storeSiteResourceID, @usageResourceType='Store', @usageType='order', 
			@usageItemID=@orderID, @enteredByMemberID=@enteredByMemberID, @historyID=@newMemberHistoryID OUTPUT:
			
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
