ALTER PROC dbo.cms_createFeaturedImageThumbnail
@featureImageID int,
@featureImageSizeID int,
@width int,
@height int,
@cropX1 int,
@cropX2 int,
@cropY1 int,
@cropY2 int,
@addedByMemberID int,
@featureImageThumbnailID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @featureImageThumbnailID = NULL;
	DECLARE @nowDate datetime = GETDATE();

	declare @environmentID int,  @environmentName varchar(50);
	select @environmentName = tier from dbo.fn_getServerSettings();
	select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

	INSERT INTO dbo.cms_featuredImageThumbnails (featureImageID, featureImageSizeID, width, height, cropX1, cropX2, cropY1, cropY2, isActive, dateCreated, dateModified, addedByMemberID, environmentID)
	VALUES (@featureImageID, @featureImageSizeID, @width, @height, @cropX1, @cropX2, @cropY1, @cropY2, 1, @nowDate, @nowDate, @addedByMemberID, @environmentID);
		SET @featureImageThumbnailID = SCOPE_IDENTITY();

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
