CREATE TRIGGER trg_tr_limitsInsert ON dbo.tr_limits
AFTER INSERT 
AS

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN

	-- when adding a GL to a limit schedule, flag all transactions in this GL in the schedule date range.
	INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
	select t.transactionID, ls.orgID, t.recordedOnSiteID
	from Inserted as I
	INNER JOIN dbo.tr_limitSchedules as ls on ls.scheduleID = I.scheduleID
	INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = ls.orgID and t.creditGLAccountID = I.glaccountID
	WHERE t.dateRecorded between ls.startdate and ls.enddate;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
ALTER TABLE [dbo].[tr_limits] ENABLE TRIGGER [trg_tr_limitsInsert]
GO
