<cfsavecontent variable="local.SponsorSelectorJS">
	<cfoutput>
	<script language="javascript">
		function loadSponsors_#arguments.selectorID#() {
			let loadSponsorsResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					doloadSponsors_#arguments.selectorID#(r.arrincludedsponsors,r.arravailablesponsors,r.arrsponsorgroupings);
				} else {
					let reloadHTML = '<div class="text-center mt-5"><span class="d-block text-danger mb-2">Sorry, we were unable to load the data.</span><i class="fa-solid fa-rotate-right fa-2x cursor-pointer" onclick="loadSponsors_#arguments.selectorID#()"></i><span class="d-block">Reload</span></div>';
					$('##availSponsorWidgetContainer#arguments.selectorID#, ##incSponsorWidgetContainer#arguments.selectorID#').html(reloadHTML);
				}
			};

			$('##incSponsorWidgetContainer#arguments.selectorID#, ##availSponsorWidgetContainer#arguments.selectorID#').html(mca_getLoadingHTML());
			$('##SponsorsWidgetContainer#arguments.selectorID# .SponsorCountSpan').toggleClass('d-none', true);
			$('##SponsorsWidgetContainer#arguments.selectorID# .SponsorCountLoadingSpan').toggleClass('d-none', false);
			let objParams = { referenceType:'#arguments.referenceType#', referenceID:#arguments.referenceID# };
			TS_AJX('SPONSORS','getSponsors',objParams,loadSponsorsResult,loadSponsorsResult,60000,loadSponsorsResult);
		}
		function doloadSponsors_#arguments.selectorID#(arrIncludedSponsors,arrAvailableSponsors,arrSponsorGroupings) {
			let availSponsorListSource = $('##mc_AvailSponsorList_#arguments.selectorID#').html().replace(/\n/g,'');
			let availSponsorTemplate = Handlebars.compile(availSponsorListSource);
			$('##availSponsorWidgetContainer#arguments.selectorID#').html(availSponsorTemplate({arrAvailableSponsors:arrAvailableSponsors}));
			mcActivateTooltip($('##availSponsorWidgetContainer#arguments.selectorID#'));

			arrSponsorGroupings = arrSponsorGroupings || [];

			let processedGroupings = arrSponsorGroupings.map(function(group, index) {
				let sponsorsInGroup = arrIncludedSponsors.filter(function(sponsor) {
					let sponsorGroupId = sponsor.sponsorgroupingid || 0;
					let groupId = group.sponsorgroupingid || 0;
					return sponsorGroupId === groupId;
				});

				let actualGroups = arrSponsorGroupings.filter(g => (g.sponsorgroupingid || 0) !== 0);
				let actualGroupIndex = actualGroups.findIndex(g => g.sponsorgroupingid === group.sponsorgroupingid);
				let isFirstActualGroup = actualGroupIndex === 0;
				let isLastActualGroup = actualGroupIndex === actualGroups.length - 1;

				return {
					...group,
					sponsorcount: sponsorsInGroup.length,
					arrsponsors: sponsorsInGroup,
					isfirstactualgroup: isFirstActualGroup,
					islastactualgroup: isLastActualGroup,
					isdefaultgroup: (group.sponsorgroupingid || 0) === 0
				};
			});
			let sponsorsCount = arrIncludedSponsors.length;
			let actualGroupsCount = processedGroupings.filter(g => (g.sponsorgroupingid || 0) !== 0).length;
			let incSponsorSource = $('##mc_sponsorGroupings_#arguments.selectorID#').html().replace(/\n/g,'');
			let incSponsorTemplate = Handlebars.compile(incSponsorSource);
			$('##incSponsorWidgetContainer#arguments.selectorID#').html(incSponsorTemplate({
				arrSponsorGroupings: processedGroupings.filter(item => !(item.sponsorgroupingid === 0 && item.arrsponsors.length === 0)), /* remove no-grouping entry if no children */
				actualGroupsCount: actualGroupsCount,
				sponsorsCount: sponsorsCount
			}));
			mcActivateTooltip($('##incSponsorWidgetContainer#arguments.selectorID#'));
			$('##SponsorsWidgetContainer#arguments.selectorID# .selSponsorCount').text(sponsorsCount);
			$('##SponsorsWidgetContainer#arguments.selectorID# .availSponsorCount').text(arrAvailableSponsors.length);
			$('##SponsorsWidgetContainer#arguments.selectorID# .SponsorCountLoadingSpan').toggleClass('d-none', true);
			$('##SponsorsWidgetContainer#arguments.selectorID# .SponsorCountSpan').toggleClass('d-none', false);
			if (typeof window['disableSponsor_#arguments.selectorID#'] == 'function'){
				window['disableSponsor_#arguments.selectorID#'](sponsorsCount + actualGroupsCount);
			}
		}
		<cfif NOT arguments.readOnly>
			function editSponsor_#arguments.selectorID#(id,editSelected=0) {
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: (id == 0 ? 'Add' : 'Edit') + ' Sponsor' ,
					iframe: true,
					contenturl: '#local.editSponsorLink#&sponsorID=' + id + '&editSelected=' + (editSelected == 1 ? 'true' : 'false'),
					strmodalfooter: {
						classlist: 'd-flex',
						showclose: true,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary ml-auto',
						extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.saveSponsorForm',
						extrabuttonlabel: (id == 0 ? 'Add' : 'Save') +  ' Sponsor',
					}
				});
			}
			function associateSponsor_#arguments.selectorID#(sid) {
				MCModalUtils.showModal({
					isslideout: true,
					size: 'md',
					title: 'Associate Sponsor with a Sponsor Grouping',
					iframe: true,
					contenturl: '#local.associateSponsorLink#&sponsorID=' + sid,
					strmodalfooter: {
						classlist: 'd-flex',
						showclose: true,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary ml-auto',
						extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.associateSponsorWithGroup',
						extrabuttonlabel: 'Associate Sponsor'
					}
				});
			}
			function deassociateSponsor_#arguments.selectorID#(suid) {
				var deassociateSponsorResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						loadSponsors_#arguments.selectorID#();
						
					} else {
						alert('Unable to deassociate this sponsor from a sponsor grouping.');
						delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					}
				};

				let delBtn = $('##btnDelSponsor#arguments.selectorID#_'+suid);
				mca_initConfirmButton(delBtn, function(){
					var objParams = { sponsorUsageID:suid, referenceType:'#arguments.referenceType#', referenceID:#arguments.referenceID# };
					TS_AJX('SPONSORS','deassociateSponsor',objParams,deassociateSponsorResult,deassociateSponsorResult,10000,deassociateSponsorResult);
				});
			}
			function moveSponsorUsageRow_#arguments.selectorID#(suid,dir) {
				var moveSponsorResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						loadSponsors_#arguments.selectorID#();
					}
				};
				var objParams = { sponsorUsageID:suid, referenceType:'#arguments.referenceType#', referenceID:#arguments.referenceID#, dir:dir };
				TS_AJX('SPONSORS','moveSponsor',objParams,moveSponsorResult,moveSponsorResult,10000,moveSponsorResult);
			}
			function editSponsorImage_#arguments.selectorID#(sid,title,frmLink) {
				MCModalUtils.hideModal();
				$('##MCModal').on('hidden.bs.modal', function() {
					MCModalUtils.showModal({
						isslideout: true,
						modaloptions: {
							backdrop: 'static',
							keyboard: false
						},
						size: 'lg',
						title: title,
						iframe: true,
						contenturl: frmLink,
						strmodalfooter : {
							classlist: 'd-flex',
							showclose: true,
							showextrabutton: true,
							extrabuttonclass: 'btn-primary ml-auto',
							extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmFeaturedImage :submit").click',
							extrabuttonlabel: 'Upload',
							extrabuttoniconclass:'fa-light fa-file-arrow-up'
						}
					});
					$('##MCModal').on('hidden.bs.modal', function() { editSponsor_#arguments.selectorID#(sid); });
				});
			}
			function editSponsorGrouping_#arguments.selectorID#(groupingID, currentName) {
				MCModalUtils.showModal({
					isslideout: true,
					size: 'md',
					title: 'Edit Sponsor Grouping',
					iframe: true,
					contenturl: '#local.editSponsorGroupLink#&referenceType=#arguments.referenceType#&referenceID=#arguments.referenceID#&sponsorGroupingID=' + groupingID,
					strmodalfooter: {
						classlist: 'd-flex',
						showclose: true,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary ml-auto',
						extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmEditSponsorGroup :submit").click',
						extrabuttonlabel: 'Save'
					}
				});
			}
			function moveSponsorGrouping_#arguments.selectorID#(groupingID, dir) {
				var moveGroupingResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						loadSponsors_#arguments.selectorID#();
					} else {
						alert('Unable to move sponsor grouping.');
					}
				};
				var objParams = {
					sponsorGroupingID: groupingID,
					dir: dir,
					referenceType: '#arguments.referenceType#',
					referenceID: #arguments.referenceID#
				};
				TS_AJX('SPONSORS','moveSponsorGrouping',objParams,moveGroupingResult,moveGroupingResult,10000,moveGroupingResult);
			}
			function removeSponsorGrouping_#arguments.selectorID#(groupingID) {
				var removeGroupingResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						loadSponsors_#arguments.selectorID#();
					} else {
						delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						alert('Unable to remove sponsor grouping. Ensure no sponsors are assigned to this grouping.');
					}
				};

				let delBtn = $('##btnRemoveGroup#arguments.selectorID#_'+groupingID);
				mca_initConfirmButton(delBtn, function(){
					var objParams = { sponsorGroupingID: groupingID };
					TS_AJX('SPONSORS','deleteSponsorGrouping',objParams,removeGroupingResult,removeGroupingResult,10000,removeGroupingResult);
				});
			}
		</cfif>
	
		$(function() {
			Handlebars.registerPartial('mc_incSponsors_#arguments.selectorID#', $('##mc_incSponsors_#arguments.selectorID#').html());
			loadSponsors_#arguments.selectorID#();
		});
	</script>
	<style>
		##SponsorsWidgetContainer#arguments.selectorID# .btn-xs.btnSponsor { padding-right: 0.32rem; padding-left: 0.32rem; margin: 0.15rem }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.SponsorSelectorJS#">

<cfoutput>
<div id="SponsorsWidgetContainer#arguments.selectorID#" class="card card-box">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-sm">
			Selected Sponsors
		</div>
		<span class="SponsorCountSpan small d-none"><span class="selSponsorCount pr-1"></span>selected</span>
		<span class="SponsorCountLoadingSpan small">loading...</span>
	</div>
	<div class="card-body p-0">
		<div id="incSponsorWidgetContainer#arguments.selectorID#" style="height:#arguments.selectedSponsorsCardHeight#px;overflow-y:auto;" class="p-2 bg-secondary"></div>
		<div class="accordion" id="availSponsorAccordion_#arguments.selectorID#">
			<div class="card card-box rounded-bottom mb-0">
				<div class="card-header bg-light rounded-0" id="heading_#arguments.selectorID#">
					<button class="btn btn-link d-flex align-items-center justify-content-between font-size-sm collapsed" type="button" data-toggle="collapse" data-target="##collapse_#arguments.selectorID#" aria-expanded="false" aria-controls="collapse_#arguments.selectorID#">
						<span class="SponsorCountSpan d-none"><span class="availSponsorCount pr-1"></span>Additional Sponsors Available</span>
						<span class="SponsorCountLoadingSpan">loading...</span>
						<span>
							<i class="fa-solid fa-caret-up font-size-xl"></i>
						</span>
					</button>
				</div>
				<div id="collapse_#arguments.selectorID#" class="collapse" aria-labelledby="heading_#arguments.selectorID#" data-parent="##availSponsorAccordion_#arguments.selectorID#" style="">
					<div class="card">
						<div class="card-header py-2">
							<div class="card-header--title font-size-xs text-grey">
								Choose from the following sponsors:
							</div>
							<div class="card-header--actions">
								<cfif NOT arguments.readOnly>
									<a href="##" name="btnCreateSponsor" onclick="editSponsor_#arguments.selectorID#(0,1);return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Sponsor">
										<i class="fa-regular fa-circle-plus fa-lg"></i>
									</a>
								</cfif>
							</div>
						</div>
						<div class="card-body bg-secondary p-2" id="availSponsorWidgetContainer#arguments.selectorID#" style="height:250px;overflow-y:auto;"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script id="mc_sponsorGroupings_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##compare sponsorsCount '==' 0}}
		<div class="text-center py-3 text-dim">No Sponsors Selected.</div>
	{{/compare}}
	{{##compare actualGroupsCount '>=' 1}}
		{{##each arrSponsorGroupings}}
			<div class="bg-secondary{{##compare arrsponsors.length '>' 0}} mb-3{{/compare}}">
				<div class="d-flex justify-content-between align-items-center">
					<div><i class="fa-regular fa-tag font-size-md text-warning mr-2"></i><b class="font-size-sm">{{sponsorgrouping}}</b></div>
					<cfif NOT arguments.readOnly>
						<div class="col-auto">
							{{##unless isdefaultgroup}}
								{{##if isfirstactualgroup}}
									<a href="##" class="btn btn-xs btn-outline-dark invisible btnSponsor"><i class="fa-solid fa-up"></i></a>
								{{else}}
									<a href="##" onclick="$(this).tooltip('hide');moveSponsorGrouping_#arguments.selectorID#({{sponsorgroupingid}},'up');return false;"
										class="btn btn-xs btn-outline-dark btnSponsor" data-toggle="tooltip" title="Move Sponsor Grouping Up">
										<i class="fa-solid fa-up"></i>
									</a>
								{{/if}}
								{{##if islastactualgroup}}
									<a href="##" class="btn btn-xs btn-outline-dark invisible btnSponsor"><i class="fa-solid fa-down"></i></a>
								{{else}}
									<a href="##" onclick="$(this).tooltip('hide');moveSponsorGrouping_#arguments.selectorID#({{sponsorgroupingid}},'down');return false;"
										class="btn btn-xs btn-outline-dark btnSponsor" data-toggle="tooltip" title="Move Sponsor Grouping Down">
										<i class="fa-solid fa-down"></i>
									</a>
								{{/if}}
								<a href="##" onclick="$(this).tooltip('hide');editSponsorGrouping_#arguments.selectorID#({{sponsorgroupingid}},'{{sponsorgrouping}}');return false;"
									class="btn btn-xs btn-outline-primary btnSponsor" data-toggle="tooltip" title="Edit Sponsor Grouping">
									<i class="fa-solid fa-pencil"></i>
								</a>
								<a href="##" id="btnRemoveGroup#arguments.selectorID#_{{sponsorgroupingid}}"
									onclick="$(this).tooltip('hide');removeSponsorGrouping_#arguments.selectorID#({{sponsorgroupingid}});return false;"
									class="btn btn-xs btn-outline-danger btnSponsor{{##if sponsorcount}} invisible{{/if}}"
									data-toggle="tooltip" title="{{##if sponsorcount}}Cannot remove sponsor grouping with sponsors{{else}}Remove Sponsor Grouping{{/if}}" data-confirm="0">
									<i class="fa-solid fa-trash-can"></i>
								</a>
							{{/unless}}
						</div>
					</cfif>
				</div>
				{{##if arrsponsors}}
					<ul class="list-group mt-2">
						{{> mc_incSponsors_#arguments.selectorID# arrsponsors=arrsponsors }}
					</ul>
				{{/if}}
			</div>
		{{/each}}
	{{/compare}}
	{{##compare actualGroupsCount '==' 0}}
		{{##each arrSponsorGroupings}}
			{{> mc_incSponsors_#arguments.selectorID# arrsponsors=arrsponsors }}
		{{/each}}
	{{/compare}}
</script>
<script id="mc_incSponsors_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##each arrsponsors}}
		<li class="list-group-item py-1" id="sponsorUsageRow_{{sponsorusageid}}" data-sponsorid="{{sponsorid}}" data-groupid="{{sponsorgroupingid}}">
			<div class="row no-gutters align-items-center">
				<div class="col font-size-sm">{{sponsorname}}</div>
				<cfif NOT arguments.readOnly>
					<div class="col-auto pl-2">
						{{##compare @index '==' 0}}
							<a href="##" class="btn btn-xs btn-outline-dark invisible btnSponsor"><i class="fa-solid fa-up"></i></a>
						{{/compare}}
						{{##compare @index '!=' 0}}
							<a href="##" onclick="$(this).tooltip('hide');moveSponsorUsageRow_#arguments.selectorID#({{sponsorusageid}},'up');return false;"
								class="btn btn-xs btn-outline-dark btnSponsor" data-toggle="tooltip" title="Move Sponsor Up">
								<i class="fa-solid fa-up"></i>
							</a>
						{{/compare}}
						{{##compare (math @index "+" 1) '==' ../arrsponsors.length}}
							<a href="##" class="btn btn-xs btn-outline-dark invisible btnSponsor"><i class="fa-solid fa-down"></i></a>
						{{/compare}}
						{{##compare (math @index "+" 1) '!=' ../arrsponsors.length}}
							<a href="##" onclick="$(this).tooltip('hide');moveSponsorUsageRow_#arguments.selectorID#({{sponsorusageid}},'down');return false;"
								class="btn btn-xs btn-outline-dark btnSponsor" data-toggle="tooltip" title="Move Sponsor Down">
								<i class="fa-solid fa-down"></i>
							</a>
						{{/compare}}
						<a href="##" onclick="$(this).tooltip('hide');editSponsor_#arguments.selectorID#({{sponsorid}},1);return false;"
							class="btn btn-xs btn-outline-primary btnSponsor" data-toggle="tooltip" title="Edit Sponsor">
							<i class="fa-solid fa-pencil"></i>
						</a>
						<a href="##" id="btnDelSponsor#arguments.selectorID#_{{sponsorusageid}}"
							onclick="$(this).tooltip('hide');deassociateSponsor_#arguments.selectorID#({{sponsorusageid}});return false;"
							class="btn btn-xs btn-outline-danger btnSponsor" data-toggle="tooltip" title="Remove Sponsor" data-confirm="0">
							<i class="fa-solid fa-trash-can"></i>
						</a>
					</div>
				</cfif>
			</div>
		</li>
	{{/each}}
</script>
<script id="mc_AvailSponsorList_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arrAvailableSponsors}}
		<ul class="list-group mt-2">
		{{##each arrAvailableSponsors}}
			<li class="list-group-item py-1">
				<div class="row no-gutters align-items-center">
					<div class="col font-size-sm">{{sponsorname}}</div>
					<div class="col-auto pl-2">
						<cfif NOT arguments.readOnly>
							<a href="##" id="btnAssociateSponsor_#arguments.selectorID#_{{sponsorid}}" onclick="$(this).tooltip('hide');associateSponsor_#arguments.selectorID#({{sponsorid}});return false;" class="btn btn-xs btn-outline-success btnSponsor" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Add Sponsor">
								<i class="fa-solid fa-plus"></i>
							</a>
							<a href="##" onclick="$(this).tooltip('hide');editSponsor_#arguments.selectorID#({{sponsorid}});return false;" class="btn btn-xs btn-outline-primary btnSponsor" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Sponsor">
								<i class="fa-solid fa-pencil"></i>
							</a>
						</cfif>
					</div>
				</div>
			</li>
		{{/each}}
		</ul>
	{{else}}
		<div class="text-center py-3 text-dim">No Sponsors Available.</div>
	{{/if}}
</script>
</cfoutput>