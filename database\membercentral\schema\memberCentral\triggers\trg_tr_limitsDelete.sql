CREATE TRIGGER trg_tr_limitsDelete ON dbo.tr_limits
AFTER DELETE 
AS

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM deleted) RETURN

	-- when remove a limited GL from a schedule, flag all transactions in any of the the other GLs of the schedule in the schedule date range. 
	-- can clear the flag from all transactions in this GL in the date range. Remove the link between transaction and schedule.
	INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
	select t.transactionID, ls.orgID, t.recordedOnSiteID
	from deleted as D
	INNER JOIN dbo.tr_limitSchedules as ls on ls.scheduleID = D.scheduleID
	INNER JOIN dbo.tr_limits as lAll on lAll.scheduleID = ls.scheduleID and lAll.limitID <> D.limitID
	INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = ls.orgID and t.creditGLAccountID = lAll.glaccountID
	WHERE t.dateRecorded between ls.startdate and ls.enddate;

	DELETE tls
	FROM deleted as D
	INNER JOIN dbo.tr_limitSchedules as ls on ls.scheduleID = D.scheduleID
	INNER JOIN dbo.tr_transactionLimitSchedules as tls on tls.orgID = ls.orgID and tls.scheduleID = ls.scheduleID
	INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = ls.orgID and t.creditGLAccountID = D.glaccountID
	WHERE t.dateRecorded between ls.startdate and ls.enddate;
		
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
ALTER TABLE [dbo].[tr_limits] ENABLE TRIGGER [trg_tr_limitsDelete]
GO
