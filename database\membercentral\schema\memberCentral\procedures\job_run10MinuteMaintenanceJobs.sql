ALTER PROC dbo.job_run10MinuteMaintenanceJobs
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tier varchar(12), @jobRunOrder tinyint, @procName varchar(100), @start datetime, @totalMS int;
	DECLARE @tblJobs TABLE (runorder tinyint, procName varchar(100), prodOnly bit);

	SELECT @tier=tier from dbo.fn_getServerSettings();

	/* setup jobs */
	INSERT INTO @tblJobs (runorder, procName, prodOnly)
	VALUES 
		(1, 'rpt_queueScheduledReports', 0),
		(2, 'platformQueue.dbo.up_checkQueues', 0),
		(3, 'ams_syncCertifiedEmails', 1),
		(4, 'ams_importPartialMemberData_lyrischange', 0),
		(5, 'searchMC.dbo.up_updateContentLanguages', 1);

	select @jobRunOrder = min(runorder) 
	from @tblJobs
	where (prodOnly = 0 OR (prodOnly = 1 AND @tier = 'Production'));

	while @jobRunOrder is not null begin
		select @procName = procName from @tblJobs where runOrder = @jobRunOrder;

		BEGIN TRY
			SET XACT_ABORT OFF;	

			SET @start = getdate();
			EXEC @procName;
			SET @totalMS = datediff(ms,@start,getdate());

			INSERT INTO platformStatsMC.dbo.job_runtimeLog (procname, timeMS) 
			VALUES (@procName, @totalMS);	

			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH

		-- delay inside the loop to address apparent parallel issue
		WAITFOR DELAY '00:00:02';

		select @jobRunOrder = min(runorder) 
		from @tblJobs
		where (prodOnly = 0 OR (prodOnly = 1 AND @tier = 'Production'))
		and runOrder > @jobRunOrder;
	end

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
