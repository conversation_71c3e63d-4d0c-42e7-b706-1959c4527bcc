ALTER PROC dbo.queue_MemberGroupPrints_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	select 'memberGroupPrints', qs.queueStatus, count(qi.itemid), min(qi.dateUpdated), 1
	from dbo.queue_memberGroupPrints as qi
	inner join dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID
	group by qs.queueStatus
	having count(qi.itemid) > 0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
