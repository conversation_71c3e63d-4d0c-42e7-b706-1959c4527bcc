ALTER PROC dbo.queue_paperStatements_grabForProcessing
@batchSize int,
@filePathUNC varchar(400),
@filePath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @subTypeOrdering TABLE (detailID INT, typeID int, typeSortOrder int);
	declare @queueTypeID INT, @readyStatusID int, @grabProcessingStatusID int, @siteID int, @orgID int, @jobUID uniqueidentifier = NEWID();
	EXEC dbo.queue_getQueueTypeID @queueType='PaperStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_paperstatements') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_paperstatements;
	IF OBJECT_ID('tempdb..#mcSubscribersForAcct') IS NOT NULL
		DROP TABLE #mcSubscribersForAcct;
	IF OBJECT_ID('tempdb..#mcSubscriberTransactions') IS NOT NULL
		DROP TABLE #mcSubscriberTransactions;
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs;
	IF OBJECT_ID('tempdb..#qrySubsTransactions') IS NOT NULL 
		DROP TABLE #qrySubsTransactions;
	IF OBJECT_ID('tempdb..#qrySubsFees') IS NOT NULL 
		DROP TABLE #qrySubsFees;
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers;
	IF OBJECT_ID('tempdb..#tmpSaleTransIDs') IS NOT NULL
		DROP TABLE #tmpSaleTransIDs;
	IF OBJECT_ID('tempdb..#tmpAllocPayments') IS NOT NULL
		DROP TABLE #tmpAllocPayments;
	CREATE TABLE #tmpTblQueueItems_paperstatements (detailID int, itemID INT, recordedByMemberID int, siteID int, 
		memberID int, memberNumber varchar(50), firstname varchar(75), middleName varchar(25), lastname varchar(75), 
		company varchar(200), address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(75), 
		stateProv varchar(4), postalCode varchar(25), country varchar(100), xmlFieldSets varchar(max), xmlConfigParam varchar(max), 
		xmlMembers varchar(max));
	CREATE TABLE #qrySubs (detailID INT, subscriberID int, memberID int, subscriptionID int, typeID int,
		typeName varchar(100), subscriptionName varchar(300), rateName varchar(200), frequencyName varchar(50),
		[status] varchar(1), directLinkCode varchar(8), statusName varchar(50), subStartDate datetime, subEndDate datetime, graceEndDate datetime,
		parentSubscriberID int, rootSubscriberID int, PCFree bit, modifiedRate decimal(18,2), lastPrice decimal(18,2), rfid int, thePath varchar(max), 
		subAmount decimal(18,2), subAmountTax decimal(18,2), subAmountDue decimal(18,2), subAmountPaid decimal(18,2), subscriberPath varchar(200), 
		saleTransactionID int, glaccountID int, invoiceContentVersionID int, invoiceProfileID int, 
		invoiceProfileImageExt varchar(5));
	CREATE TABLE #qrySubsTransactions (subscriberID int, transactionID int, transactionIDForSubscriberSale int, statusID int, detail varchar(500), 
		amount decimal(18,2), dateRecorded datetime, transactionDate datetime, assignedToMemberID int, recordedByMemberID int,
		statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);
	CREATE TABLE #qrySubsFees (subscriberID int index ix_qrySubsFees_subscriberID, transactionIDForSubscriberSale int, 
		messageContentVersionID int, totalSubFee decimal(18,2), totalSubFeePaid decimal(18,2), subFee decimal(18,2), subFeePaid decimal(18,2), 
		taxFee decimal(18,2), taxFeePaid decimal(18,2));
	CREATE TABLE #mcSubscribersForAcct (subscriberID int PRIMARY KEY);
	CREATE TABLE #mcSubscriberTransactions (subscriberID int, transactionID int, transactionIDForSubscriberSale int, 
		statusID int, detail varchar(500), amount decimal(18,2), dateRecorded datetime, transactionDate datetime, 
		assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, 
		creditGLAccountID int, INDEX IDX_mcSubscriberTransactions_subscriberID_transactionID (subscriberID, transactionID));
	CREATE TABLE #qryAllFirmMembers (detailID INT, childMemberID int, childMemberNumber varchar(50), 
		prefix varchar(50), firstname varchar(75), middleName varchar(25), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(75), stateProv varchar(4),
		postalCode varchar(25), country varchar(100));
	CREATE TABLE #tmpSaleTransIDs (transactionID int);
	CREATE TABLE #tmpAllocPayments (saleTransactionID int, paymentTransactionID int, allocatedAmount decimal(18,2));
	
	-- dequeue in order of dateAdded. get @batchsize members
	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @grabProcessingStatusID
		OUTPUT inserted.detailID, null, null, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_paperstatements
	from dbo.queue_paperStatementsDetail as qi
	inner join (
		select top(@BatchSize) qi2.detailID 
		from dbo.queue_paperStatementsDetail as qi2
		where qi2.queueStatusID = @readyStatusID
		order by qi2.detailID
		) as batch on batch.detailID = qi.detailID
	where qi.queueStatusID = @readyStatusID;


	-- get data
	update tmp
	set tmp.itemID = psd.itemID,
		tmp.memberID = psd.memberID,
		tmp.recordedByMemberID = ps.recordedByMemberID,
		tmp.siteID = ps.siteID
	from #tmpTblQueueItems_paperstatements as tmp
	inner join dbo.queue_paperStatementsDetail as psd on psd.detailID = tmp.detailID
	inner join dbo.queue_paperStatements as ps on ps.itemID = psd.itemID;

	select top 1 @siteID = s.siteID, @orgID = s.orgID
	from #tmpTblQueueItems_paperstatements as tmp
	inner join membercentral.dbo.sites as s on s.siteID = tmp.siteID;

	-- get address info for member accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.middleName = m.middleName,
		tmp.lastname = m.lastname, 
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = ma.stateCode, 
		tmp.postalCode = ma.postalCode,
		tmp.country = ma.countryName
	from #tmpTblQueueItems_paperstatements as tmp
	inner join membercentral.dbo.ams_members as m on m.orgID = @orgID
		and m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddressTags as matag 
		inner join membercentral.dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
			and matagt.addressTagTypeID = matag.addressTagTypeID
			and matagt.addressTagType = 'Billing'
		inner join membercentral.dbo.ams_memberAddresses as ma on ma.orgID = @orgID
			and ma.memberID = matag.memberID
			and ma.addressTypeID = matag.addressTypeID
	on matag.orgID = @orgID and matag.memberID = m.memberID;

	-- get all members
	insert into #qryAllFirmMembers (detailID, childMemberID)
	select distinct tmp.detailID, m.activeMemberID
	from #tmpTblQueueItems_paperstatements as tmp
	inner join dbo.queue_paperStatementsDetail as qid on qid.detailID = tmp.detailID
	inner join membercentral.dbo.sub_subscribers as ss on ss.orgID = @orgID and ss.subscriberID = qid.rootSubscriberID
	inner join membercentral.dbo.ams_members as m on m.orgID = @orgID and m.memberID = ss.memberID;

	update fm 
	set childMemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		middleName = m.middleName,
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = ma.stateCode, 
		postalCode = ma.postalCode,
		country = ma.countryName
	from #qryAllFirmMembers as fm
	inner join membercentral.dbo.ams_members as m on m.orgID = @orgID
		and m.memberID = fm.childMemberID
	left outer join membercentral.dbo.ams_memberAddressTags as matag 
		inner join membercentral.dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
			and matagt.addressTagTypeID = matag.addressTagTypeID
			and matagt.addressTagType = 'Billing'
		inner join membercentral.dbo.ams_memberAddresses as ma on ma.orgID = @orgID
			and ma.memberID = matag.memberID
			and ma.addressTypeID = matag.addressTypeID
	on matag.orgID = @orgID and matag.memberID = m.memberID;

	-- get all subs
	insert into #qrySubs (detailID, subscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, rateName, 
		frequencyName, [status], directLinkCode, statusName, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		rootSubscriberID, PCFree, modifiedRate, lastPrice, subscriberPath, glaccountID)
	select distinct tmp.detailID, ss.subscriberID, m.activeMemberID, ss.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, 
		r.rateName, f.frequencyName, st.statusCode, ss.directLinkCode, st.statusName, ss.subStartDate, ss.subEndDate, ss.graceEndDate, 
		ss.parentSubscriberID, ss.rfid, ss.rootSubscriberID, ss.PCFree, ss.modifiedRate, ss.lastprice, ss.subscriberPath, ss.glaccountID
	from #tmpTblQueueItems_paperstatements as tmp
	inner join dbo.queue_paperStatementsDetail as qid on qid.detailID = tmp.detailID
	inner join membercentral.dbo.sub_subscribers as rootss on rootss.orgID = @orgID and rootss.subscriberID = qid.rootSubscriberID
	inner join membercentral.dbo.sub_subscribers as ss on ss.orgID = @orgID and rootss.subscriberID = ss.rootSubscriberID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.orgID = @orgID and sub.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.siteid = @siteID and sub.typeiD = t.typeID
	inner join membercentral.dbo.ams_members as m on m.orgID = @orgID and ss.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as st on st.statusID = ss.statusID 
		and st.statuscode in ('A','I','O','P')
	inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID 
	inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID;

	-- populate mcSubscriberTransactions
	INSERT INTO #mcSubscribersForAcct (subscriberID)
	select distinct subscriberID 
	from #qrySubs;

	EXEC membercentral.dbo.sub_getSubscriberTransactions_Full @orgID=@orgID;

	-- added the tr_invoicetransactions join here to limit to just transactions that appear on invoices. The DIT were causing doubled amounts.
	insert into #qrySubsTransactions (subscriberID, transactionID, transactionIDForSubscriberSale, statusID, detail, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, typeID, 
		debitGLAccountID, creditGLAccountID)
	select qs.subscriberID, stFull.transactionID, stFull.transactionIDForSubscriberSale, stFull.statusID, stFull.detail, 
		stFull.amount, stFull.dateRecorded, stFull.transactionDate, stFull.assignedToMemberID, stFull.recordedByMemberID, 
		stFull.statsSessionID, stFull.typeID, stFull.debitGLAccountID, stFull.creditGLAccountID
	from #qrySubs as qs
	inner join #mcSubscriberTransactions as stFull on stFull.subscriberID = qs.subscriberID
	inner join membercentral.dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = stFull.transactionID;

	insert into #qrySubsFees (subscriberID, transactionIDForSubscriberSale, messageContentVersionID, totalSubFee, 
		totalSubFeePaid, subFee, subFeePaid, taxFee, taxFeePaid)
	select qs.subscriberID, stFull.transactionIDForSubscriberSale, min(it.messageContentVersionID) as messageContentVersionID,
		sum(tsFull.cache_amountAfterAdjustment) as totalSubFee, sum(tsFull.cache_activePaymentAllocatedAmount) as totalSubFeePaid,
		sum(case when t.typeID = 7 then 0 else tsFull.cache_amountAfterAdjustment end) as subFee,
		sum(case when t.typeID = 7 then 0 else tsFull.cache_activePaymentAllocatedAmount end) as subFeePaid,
		sum(case when t.typeID = 7 then tsFull.cache_amountAfterAdjustment else 0 end) as taxFee,
		sum(case when t.typeID = 7 then tsFull.cache_activePaymentAllocatedAmount else 0 end) as taxFeePaid
	from #qrySubs as qs
	inner join #qrySubsTransactions as stFull on stFull.subscriberID = qs.subscriberID
	cross apply membercentral.dbo.fn_tr_transactionSalesWithDIT(@orgID,stFull.transactionID) as tsFull
	inner join membercentral.dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = tsFull.transactionID
	inner join membercentral.dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = stFull.transactionIDForSubscriberSale
	group by qs.subscriberID, stFull.transactionIDForSubscriberSale;

	update qs 
	set saleTransactionID = qsf.transactionIDForSubscriberSale,
		subAmount = qsf.subFee,
		subAmountTax = qsf.taxFee,
		subAmountDue = qsf.totalSubFee - qsf.totalSubFeePaid,
		subAmountPaid = qsf.totalSubFeePaid,
		invoiceContentVersionID = qsf.messageContentVersionID
	from #qrySubs as qs
	inner join (
		select subscriberID, min(transactionIDForSubscriberSale) as transactionIDForSubscriberSale, sum(subfee) as subfee, sum(taxfee) as taxfee, sum(totalSubFee) as totalSubFee, sum(totalSubFeePaid) as totalSubFeePaid, min(messageContentVersionID) as messageContentVersionID
		from #qrySubsFees
		group by subscriberID
	) as qsf on qsf.subscriberID = qs.subscriberID;

	-- update amount and amountdue columns for billed subscriptions
	update #qrySubs
	set subAmount = 0,
		subAmountDue = 0,
		subAmountTax = 0,
		subAmountPaid = 0
	where [status] = 'O'
	and PCFree = 1;		

	update qs 
	set qs.subAmount = qs.modifiedRate, 
		qs.subAmountTax = 
			(select isnull(sum(tax.taxAmount),0) as taxAmt
			from membercentral.dbo.ams_memberAddresses as ma
			inner join membercentral.dbo.ams_memberAddressTags as matag on matag.orgID = @orgID and matag.memberID = ma.memberID and matag.addressTypeID = ma.addressTypeID
			inner join membercentral.dbo.ams_memberAddressTagTypes as matagt on matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
			cross apply membercentral.dbo.fn_tr_getTaxForUncommittedSale(qs.glaccountID, qs.modifiedRate, getdate(), ma.stateID) as tax
			where ma.orgID = @orgID
			and ma.memberid = qs.memberID), 
		qs.subAmountPaid = 0
	from #qrySubs as qs
	where qs.[status] = 'O'
	and qs.PCFree = 0
	and qs.modifiedRate is not null;

	update qs 
	set qs.subAmount = qs.lastprice, 
		qs.subAmountTax = 
			(select isnull(sum(tax.taxAmount),0) as taxAmt
			from membercentral.dbo.ams_memberAddresses as ma
			inner join membercentral.dbo.ams_memberAddressTags as matag on matag.orgID = @orgID and matag.memberID = ma.memberID and matag.addressTypeID = ma.addressTypeID
			inner join membercentral.dbo.ams_memberAddressTagTypes as matagt on matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
			cross apply membercentral.dbo.fn_tr_getTaxForUncommittedSale(qs.glaccountID, qs.lastprice, getdate(), ma.stateID) as tax
			where ma.orgID = @orgID
			and ma.memberid = qs.memberID), 
		qs.subAmountPaid = 0
	from #qrySubs as qs
	where qs.[status] = 'O'
	and qs.PCFree = 0
	and qs.modifiedRate is null;

	update #qrySubs
	set subAmountDue = subAmount + subAmountTax
	where [status] = 'O'
	and PCFree = 0;

	declare @tblAllParams TABLE (itemID int, reportParam varchar(100), paramvalue varchar(max));

	insert into @tblAllParams (itemID, reportParam, paramvalue)
	select distinct tmp.itemID,reportParamCol, reportParamVal
	from #tmpTblQueueItems_paperstatements as tmp
	inner join dbo.queue_paperStatements as qid on qid.itemID = tmp.itemID
	cross apply	(
		VALUES 
			('renewalstatementtitle', qid.title),
			('frmsubscribertopcontent', qid.topContent),
			('frmdisplayzerodollaraddons', CAST(qid.showZeroDollarAddons AS VARCHAR)),
			('frmrenewonlineoptions', CAST(qid.showRenewOnlineLink AS VARCHAR)),
			('frmsubscriberbottomcontent', qid.bottomContent),
			('invoiceheaderimgurl', qid.invoiceheaderimgurl),
			('renewalsuburl', qid.renewalsuburl)
	) as KeyValueList(reportParamCol, reportParamVal);
	
	-- if any tax for the firm, update setting flag 
	update params
	set params.paramValue = 1
	from @tblAllParams as params
	inner join (
		select distinct tmp.itemID
		from #tmpTblQueueItems_paperstatements as tmp
		inner join #qrySubs as qs on qs.detailID = tmp.detailID
		where qs.subAmountTax > 0
	) as tmp on tmp.itemID = params.itemID
	where params.reportParam = 'showTax';

	-- get config params for each item
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_paperstatements as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramValue
			from @tblAllParams as tmp2
			where tmp2.itemID = tmp.itemID
			for xml path('param'), root('params'), type
		),'<params/>') as varchar(max)) as configXML
	) as config;

	-- set typeSortOrder based on billed total by subscription type
	insert into @subTypeOrdering (detailID, typeID, typeSortOrder)
	select tmp.detailID, tmp.typeID, row_number() over (partition by tmp.detailID order by tmp.subAmountTotal desc, typename desc)
	from (
		select qs.detailID, qs.typeID, qs.typeName, sum(subAmount) as subAmountTotal
		from #qrySubs as qs
		group by qs.detailID, qs.typeID, qs.typeName
	) as tmp;

	-- update invoiceProfileID and invoice image (billed subs have no transactions to join against)
	update qs 
	set invoiceProfileID = inp.profileID, 
		invoiceProfileImageExt = inp.imageExt
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.orgID = @orgID and gl.glaccountID = qs.glaccountID
	inner join membercentral.dbo.tr_invoiceProfiles as inp on inp.orgID = @orgID and inp.profileID = gl.invoiceProfileID;

	-- update invoiceContentVersionID for subs with no transactions
	update qs 
	set invoiceContentVersionID = cv.contentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.sub_types t on t.typeID = qs.typeID
	inner join membercentral.dbo.sites s on s.siteID = t.siteID
	inner join membercentral.dbo.tr_glaccounts as gl on gl.orgID = s.orgID and gl.glaccountID = qs.glaccountID
		and qs.saleTransactionID is null
	inner join membercentral.dbo.cms_content as c on c.contentID = gl.invoiceContentID
	inner join membercentral.dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
		and cl.languageID = s.defaultLanguageID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
		and cv.isActive = 1;

	-- get sub tree totals
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals;
	CREATE TABLE #tblSubTreeTotals (detailID int, rootsubscriberID int, tree_subAmount decimal(18,2), tree_subAmountTax decimal(18,2),
		tree_subAmountDue decimal(18,2), tree_subAmountPaid decimal(18,2));

	insert into #tblSubTreeTotals (detailID, rootsubscriberID, tree_subAmount, tree_subAmountTax, tree_subAmountDue, tree_subAmountPaid)
	select detailID, rootSubscriberID, sum(subAmount) as tree_subAmount, sum(subAmountTax) as tree_subAmountTax, sum(subAmountDue) as tree_subAmountDue, sum(subAmountPaid) as tree_subAmountPaid
	from #qrySubs 
	group by detailID, rootSubscriberID;

	-- get sub payments
	IF OBJECT_ID('tempdb..#tblSubPayments1') IS NOT NULL 
		DROP TABLE #tblSubPayments1;
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments;
	CREATE TABLE #tblSubPayments1 (detailID int, subscriberID int, rootsubscriberID int, paymentTransactionID int, allocatedAmount decimal(18,2));
	CREATE TABLE #tblSubPayments (detailID int, subscriberID int, rootsubscriberID int, transactionID int, 
		allocatedAmount decimal(18,2), detail varchar(500), depositdate datetime);

	INSERT INTO #tmpSaleTransIDs (transactionID)
	SELECT DISTINCT transactionID
	FROM #qrySubsTransactions;

	INSERT INTO #tmpAllocPayments (saleTransactionID, paymentTransactionID, allocatedAmount)
	EXEC membercentral.dbo.tr_getAllocatedPaymentsofSale @orgID=@orgID, @limitToSaleTransactionID=NULL;

	INSERT INTO #tblSubPayments1 (detailID, subscriberID, rootsubscriberID, paymentTransactionID, allocatedAmount)
	select qs.detailID, qs.subscriberID, qs.rootsubscriberID, p.paymentTransactionID, p.allocatedAmount
	from #qrySubs as qs
	inner join #qrySubsTransactions as stFull on stFull.subscriberID = qs.subscriberID
	inner join #tmpAllocPayments as p on p.saleTransactionID = stFull.transactionID;

	insert into #tblSubPayments (detailID, subscriberID, rootsubscriberID, transactionID, allocatedAmount, detail, depositdate)
	select s1.detailID, s1.subscriberID, s1.rootsubscriberID, s1.paymentTransactionID, s1.allocatedAmount, t.detail, b.depositDate
	from #tblSubPayments1 as s1
	inner join membercentral.dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = s1.paymentTransactionID
	inner join membercentral.dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = t.transactionID
	inner join membercentral.dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID;


	-- get sub invoices
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices;
	CREATE TABLE #tblSubInvoices (detailID int, subscriberID int, rootsubscriberID int, invoiceID int,
		invoiceCode char(8), datebilled datetime, datedue datetime, statusID int, [status] varchar(10), amount decimal(18,2),
		amountDue decimal(18,2), payProfileDesc varchar(100), invoiceNumber varchar(20));

	insert into #tblSubInvoices (detailID, subscriberID, rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue,
		statusID, [status], amount, amountDue,payProfileDesc, invoiceNumber)
	select qs.detailID, qs.subscriberID, qs.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, ist.statusID, 
		ist.status, sum(it.cache_invoiceAmountAfterAdjustment) as amount, 
		sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount) as amountDue,
		payProfileDesc = g.gatewayclass + ' - (' + mpp.detail + ')',
		invoiceNumber = i.fullInvoiceNumber
	from #qrySubs as qs
	inner join #qrySubsTransactions as stFull on stFull.subscriberID = qs.subscriberID
	inner join membercentral.dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = stFull.transactionID
	inner join membercentral.dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join membercentral.dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join membercentral.dbo.organizations as o on o.orgID = i.orgID
	inner join membercentral.dbo.tr_invoiceStatuses as ist on ist.statusID = i.statusID and i.statusID <> 1
	left outer join membercentral.dbo.ams_memberPaymentProfiles as mpp
		inner join membercentral.dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join membercentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		on mpp.payProfileID = i.payProfileID
	group by qs.detailID, qs.subscriberID, qs.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, 
		ist.statusID, ist.status, g.gatewayclass, mpp.detail, i.fullInvoiceNumber;

	-- populate table var of contentVersions referenced by qrySubs
	declare @invoiceContent table (contentVersionID int PRIMARY KEY, rawcontent varchar(max));
	declare @invoiceContentandSubtrees table (contentVersionID int, rootSubscriberID int, messageNumber int);

	insert into @invoiceContent (contentVersionID, rawcontent)
	select distinct cv.contentVersionID, cv.rawcontent
	from #qrySubs as qs
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qs.invoiceContentVersionID;

	insert into @invoiceContentandSubtrees (contentVersionID, rootSubscriberID, messageNumber)
	select ic.contentVersionID, qs.rootsubscriberID, row_number() over (partition by qs.rootsubscriberID order by min(qs.subscriberPath))
	from #qrySubs as qs
	inner join @invoiceContent as ic on ic.contentVersionID = qs.invoiceContentVersionID
	group by ic.contentVersionID, qs.rootsubscriberID;

	-- firms xml. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlMembers = members.memberXML
	from #tmpTblQueueItems_paperstatements as tmp
	cross apply (
		select cast(isnull(
			( select
				account.childmemberid as '@childmemberID',
				account.childMemberNumber as '@childmembernumber',
				isnull(company.memberID,0) as '@firmmemberid',
				case when exists (select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.detailID = tmp.detailID) then 1 else 0 end as '@hassubscription',
				case when exists(select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.detailID = tmp.detailID and st2.status in('A','P')) then '' else isnull((select top 1 isnull(st2.directLinkCode,'') from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.detailID = tmp.detailID order by st2.parentSubscriberID),'')  end  as '@renewalcode',
				case when exists(select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.detailID = tmp.detailID and st2.status in('A','P')) then '' else case when isnull((select top 1 st2.directLinkCode from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.detailID = tmp.detailID order by st2.parentSubscriberID), '') <> '' then 'file://' + @filePath + cast(tmp.itemID as varchar(50)) + '/' + cast(NEWID() as varchar(50)) + '.png' else '' end end as '@renewalsubqrcodeimgurl',
				account.firstname as '@firstname', 
				account.lastname as '@lastname',
				account.prefix as '@prefix',
				account.suffix as '@suffix',
				case when  nullif(account.prefix,'') is not null then account.prefix + ' ' else '' end +
				account.firstName + ' ' +
				case when  nullif(account.middleName,'') is not null then account.middleName + ' ' else ''end +
				account.lastName + ' ' +
				case when  nullif(account.suffix,'') is not null then account.suffix + ' ' else '' end as '@namestring',			
				account.company as '@company',
				account.address1 as '@address1' ,
				account.address2 as '@address2' ,
				account.address3 as '@address3' ,
				account.city as '@city' ,
				account.stateProv as '@state' ,
				account.postalCode as '@postalcode',
				account.country as '@country',
				( select
					subscriptionTree.rootsubscriberid as '@rootsubscriberid',
					case when subscriptionTree.rootsubscriberid is null then null else account.childmemberid end as '@activeMemberID',
					case when subscriptionTree.rootsubscriberid is null then null else account.lastname + ', ' + account.firstname end as '@namestring',
					subscriptionTree.subscriptionname as '@subscriptionname',
					qst.tree_subAmount as '@subamount_total',
					qst.tree_subAmountTax as '@subamounttax_total',
					qst.tree_subAmountPaid as '@subamountpaid_total',
					qst.tree_subAmountDue as '@subamountdue_total',
					CONVERT(VARCHAR(8),subscriptionTree.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriptionTree.subenddate, 1) as '@subtreedaterange',
					cast(subscriptionTree.invoiceProfileID as varchar(10)) + '.' + subscriptionTree.invoiceProfileImageExt as '@subtreeinvoiceprofileimage',
					( select
						subscriber.subscriberid as '@subscriberID',
						account.childmemberid as '@activeMemberID',
						subscriber.subscriptionid as '@subscriptionid',
						subscriber.typeid as '@typeid',
						subscriber.typename as '@typename',
						subscriber.subscriptionname as '@subscriptionname',
						subscriber.ratename as '@ratename',
						subscriber.subscriptionname + ' - ' + subscriber.ratename as '@itemdesc',
						subscriber.frequencyname as '@frequencyname',
						subscriber.status as '@status',
						subscriber.statusname as '@statusname',
						subscriber.substartdate as '@substartdate',
						subscriber.subenddate as '@subenddate',
						CONVERT(VARCHAR(8),subscriber.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriber.subenddate, 1) as '@subdaterange',
						subscriber.graceenddate as '@graceenddate',
						subscriber.parentsubscriberid as '@parentsubscriberid',
						subscriber.rootsubscriberid as '@rootsubscriberid',
						subscriber.rfid as '@rfid',
						subscriber.thepath as '@thepath',
						subscriber.lastprice as '@lastprice',
						subscriber.subAmount as '@subamount',
						subscriber.subAmountTax as '@subamounttax',
						subscriber.subAmountPaid as '@subamountpaid',
						subscriber.subAmountDue as '@subamountdue',
						subscriber.subscriberPath as '@subscriberpath',
						subscriber.invoiceContentVersionID as '@invoicecontentversionid',
						icst.messageNumber as '@invoicecontentfootnotenumber',
						sto.typeSortOrder as '@coversheettypesortorder'
					from #qrySubs as subscriber
					inner join @subTypeOrdering sto on sto.detailID = subscriber.detailID
						and sto.typeID = subscriber.typeID
					left outer join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
						and subscriber.invoiceContentVersionID = icst.contentVersionID
					where subscriber.detailID = tmp.detailID
					and subscriber.rootSubscriberID = subscriptionTree.subscriberID
					and ((select top 1 cast(isnull(params.paramvalue, '1') as INT) from @tblAllParams as params where params.reportParam = 'frmdisplayzerodollaraddons' and params.itemID = tmp.itemID) <> 0 or (subscriber.subAmount > 0 or subscriber.parentSubscriberID is null or subscriber.parentSubscriberID = 0))
					order by subscriber.subscriberPath
					for xml path('subscriber'), type
					),
					( select
						rootsubscriberID as '@rootsubscriberID',
						invoiceID as '@invoiceid',
						invoiceNumber as '@invoicenumber',
						invoiceCode as '@invoicecode',
						datebilled as '@datebilled',
						datedue as '@datedue',
						statusID as '@statusid',
						[status] as '@status',
						sum(amount) as '@amount',
						sum(amountDue) as '@amountDue',
						isnull(payProfileDesc,'') as '@payprofiledesc'
					from #tblSubInvoices as si
					where si.detailID = tmp.detailID
					and si.rootSubscriberID = subscriptionTree.rootsubscriberID
					group by rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue, statusID, [status], payProfileDesc, invoiceNumber
					order by datedue
					for xml path ('invoice'),root('invoices'), type
					),
					( select
						rootsubscriberID as '@rootsubscriberID',
						transactionID as '@transactionID',
						depositdate as '@datebilled',
						detail as '@detail',
						sum(allocatedAmount) as '@allocatedAmount'
					from #tblSubPayments sp
					where sp.detailID = tmp.detailID
					and sp.rootSubscriberID = subscriptionTree.rootsubscriberID
					group by rootsubscriberID, transactionID, detail, depositdate
					order by depositdate
					for xml path('payment'), root('payments'), type
					),
					( select
						subscriber.rootSubscriberID as '@rootsubscriberID',
						icst.messageNumber as '@invoicecontentfootnotenumber',
						ic.contentversionid as '@contentversionid',
						ic.rawcontent as '@rawcontent'
					from #qrySubs as subscriber
					inner join @invoiceContent as ic on ic.contentVersionID = subscriber.invoiceContentVersionID
						and subscriber.detailID = tmp.detailID
						and subscriber.rootSubscriberID = subscriptionTree.subscriberID
					inner join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
						and icst.contentVersionID = ic.contentVersionID
					group by subscriber.rootSubscriberID, icst.messageNumber, ic.contentversionid, ic.rawcontent
					order by icst.messageNumber
					for xml path('invoicemessage'), root('invoicemessages'), type
					)
					from #qrySubs as subscriptionTree
					inner join #tblSubTreeTotals as qst 
						on qst.rootSubscriberID = subscriptionTree.rootSubscriberID
						and qst.detailID = subscriptionTree.detailID
					where subscriptionTree.detailID = tmp.detailID
					and subscriptionTree.memberID = account.childMemberID
					and subscriptionTree.subscriberID = subscriptionTree.rootsubscriberID
					order by subscriptionTree.rootsubscriberid
					for xml path('subscriptiontree'), type
				)
			from #qryAllFirmMembers as account
			inner join #tmpTblQueueItems_paperstatements as company on account.detailID = company.detailID
				and company.detailID = tmp.detailID
			order by account.lastname, account.firstname, account.childMemberID
			for xml path('account'),  root('accounts'), type				
		),'<accounts/>') as varchar(max)) as memberXML
	) as members;

	-- create directories to store xmls
	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs;
	select distinct itemID, membercentral.dbo.fn_createDirectory(@filePathUNC + cast(itemID as varchar(50))) as cdResult
	into #tblDirs
	from #tmpTblQueueItems_paperstatements
	where membercentral.dbo.fn_DirectoryExists(@filePathUNC + cast(itemID as varchar(50))) = 0;

	-- final data
	select  detailID, itemID, siteID, memberNumber, firstName, lastName, company, xmlFieldSets, cast(xmlConfigParam as varchar(max)) as xmlConfigParam, 
		membercentral.dbo.fn_writefile(@filePathUNC + cast(itemID as varchar(50)) + '\paperstatement' + cast(detailID as varchar(50)) + '.xml',xmlMembers,1) as writeFileResult
	from #tmpTblQueueItems_paperstatements
	order by detailID;

	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs;
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices;
	IF OBJECT_ID('tempdb..#tblSubPayments1') IS NOT NULL 
		DROP TABLE #tblSubPayments1;
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments;
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals;
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs;
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers;
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_paperstatements') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_paperstatements;
	IF OBJECT_ID('tempdb..#mcSubscribersForAcct') IS NOT NULL
		DROP TABLE #mcSubscribersForAcct;
	IF OBJECT_ID('tempdb..#mcSubscriberTransactions') IS NOT NULL
		DROP TABLE #mcSubscriberTransactions;
	IF OBJECT_ID('tempdb..#tmpSaleTransIDs') IS NOT NULL
		DROP TABLE #tmpSaleTransIDs;
	IF OBJECT_ID('tempdb..#tmpAllocPayments') IS NOT NULL
		DROP TABLE #tmpAllocPayments;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
