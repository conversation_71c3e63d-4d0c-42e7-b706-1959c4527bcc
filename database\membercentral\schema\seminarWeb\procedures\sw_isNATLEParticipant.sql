ALTER PROC dbo.sw_isNATLEParticipant
@orgcode varchar(10),
@isNATLE bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @count INT;
	SET @isNATLE = 0;

	SELECT @count = count(p.participantID)
	FROM dbo.tblParticipants p
	INNER JOIN dbo.tblNationalProgramParticipants npp ON p.participantID = npp.participantID
	INNER JOIN dbo.tblNationalPrograms np ON npp.programID = np.programID
	WHERE p.orgcode = @orgcode
	AND np.programName = 'NATLE'
	AND p.isActive = 1;

	IF @count > 0
		SET @isNATLE = 1;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
