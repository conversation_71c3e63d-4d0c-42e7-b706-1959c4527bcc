ALTER PROC dbo.sms_checkSMSSendingQueue

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @readyStatusID int, @grabbedStatusID int, @cancelledStatusID int, @processingStatusID int,
		@tier varchar(12), @errorSubject VARCHAR(400), @checkDate datetime,
		@numFound int = 0, @anyCancelled bit = 0, @now datetime,  @errmsg varchar(max);

	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	SELECT @readyStatusID = statusID FROM dbo.sms_messageDeliveryStatuses WHERE [status] = 'readyForProcessing';
	SELECT @grabbedStatusID = statusID FROM dbo.sms_messageDeliveryStatuses WHERE [status] = 'grabbedForProcessing';
	SELECT @processingStatusID = statusID FROM dbo.sms_messageDeliveryStatuses WHERE [status] = 'processing';
	SELECT @cancelledStatusID = statusID FROM dbo.sms_messageDeliveryStatuses WHERE [status] = 'cancelledBeforeSending';

	--  items marked as processing with datelastupdated older than 15 minutes. Auto cancel.
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		DECLARE @recipientsToUpdate1 TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();
		SET @checkDate = dateadd(minute,-15,getdate());
		SET @anyCancelled = 0;
		
		INSERT INTO @recipientsToUpdate1 (recipientID)
		SELECT mr.recipientID
		FROM dbo.sms_messageRecipients AS mr
		INNER JOIN dbo.sms_messages AS m ON mr.siteID = m.siteID
			AND m.[status] = 'A'
			AND m.messageID = mr.messageID
		INNER JOIN dbo.sms_messageRecipientDeliveryQueue AS dq ON dq.recipientID = mr.recipientID
		WHERE dq.deliveryStatusID = @processingStatusID
		AND dq.dateLastUpdated < @checkDate;

		SET @numFound = @@ROWCOUNT;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0 BEGIN
			UPDATE mr WITH (UPDLOCK, HOLDLOCK)
			SET mr.deliveryStatusID = @cancelledStatusID
			FROM @recipientsToUpdate1 AS temp
			INNER JOIN dbo.sms_messageRecipients AS mr ON mr.recipientID = temp.recipientID
			WHERE mr.deliveryStatusID = @processingStatusID;

			UPDATE dq WITH (UPDLOCK, HOLDLOCK)
			SET dq.deliveryStatusID = @cancelledStatusID,
				dq.dateLastUpdated = @now
			FROM @recipientsToUpdate1 AS temp
			INNER JOIN dbo.sms_messageRecipientDeliveryQueue AS dq ON dq.recipientID = temp.recipientID
			WHERE dq.deliveryStatusID = @processingStatusID;

			IF @@ROWCOUNT > 0
				SET @anyCancelled = 1;
		END

		IF @anyCancelled > 0 AND @tier = 'Production' BEGIN
			SET @errorSubject = 'Investigation Needed: SMS Sending queue had old recipients marked as processing. The recipients have been cancelled.';
			SET @errmsg = 'There were recipients marked as Processing that hadn''t been updated for 15 minutes. They have been cancelled.';
			EXEC dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH

	-- items grabbedForProcessing with datelastupdated more than 10 mins ago
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
		DECLARE @recipientsToUpdate2 TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();
		SET @checkDate = dateadd(minute,-10,@now);

		INSERT INTO @recipientsToUpdate2 (recipientID)
		SELECT mr.recipientID
		FROM dbo.sms_messageRecipients AS mr
		INNER JOIN dbo.sms_messages AS m ON mr.siteID = m.siteID
			AND m.[status] = 'A'
			AND m.messageID = mr.messageID
		INNER JOIN dbo.sms_messageRecipientDeliveryQueue AS dq ON dq.recipientID = mr.recipientID
		WHERE dq.deliveryStatusID = @grabbedStatusID
		AND dq.dateLastUpdated < @checkDate;

		SET @numFound = @@ROWCOUNT;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0 BEGIN
			UPDATE mr WITH (UPDLOCK, HOLDLOCK)
			SET deliveryStatusID = @readyStatusID
			FROM @recipientsToUpdate2 AS temp
			INNER JOIN dbo.sms_messageRecipients AS mr ON mr.recipientID = temp.recipientID
			WHERE mr.deliveryStatusID = @grabbedStatusID;

			UPDATE dq WITH (UPDLOCK, HOLDLOCK)
			SET dq.deliveryStatusID = @readyStatusID,
				dq.dateLastUpdated = @now
			FROM @recipientsToUpdate2 AS temp
			INNER JOIN dbo.sms_messageRecipientDeliveryQueue AS dq ON dq.recipientID = temp.recipientID
			WHERE dq.deliveryStatusID = @grabbedStatusID;
		END

		IF @numFound > 0 AND @tier = 'Production' BEGIN
			SET @errorSubject = 'SMS Sending queue had recipients marked as grabbedForProcessing with datelastupdated older than 10 minutes.';
			SET @errmsg = 'There were '+CAST(@numFound AS varchar)+ ' recipient(s) in grabbedForProcessing with datelastupdated older than 10 minutes. We assume that the sending process died. They have been marked as readyForProcessing.';
			EXEC dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
