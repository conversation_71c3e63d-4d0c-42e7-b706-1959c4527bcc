<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.23" id="1">
    <property name="createdBy">Eclipse BIRT Designer Version 4.6.0.v201606072122</property>
    <list-property name="userProperties">
        <structure>
            <property name="name">Subscribers DataCube.subscribers.x</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">Subscribers DataCube.subscribers.y</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">Subscribers DataCube.AccountInfo.NewTabularHierarchy1.x</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">Subscribers DataCube.AccountInfo.NewTabularHierarchy1.y</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">Subscribers DataCube.subscribers.width</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">Subscribers DataCube.subscribers.height</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">Subscribers DataCube.AccountInfo.NewTabularHierarchy1.width</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">Subscribers DataCube.AccountInfo.NewTabularHierarchy1.height</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">joinedSubsCube.joinSubscribers.x</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">joinedSubsCube.joinSubscribers.y</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">FirmSubscriberSummary.subscribers.x</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">FirmSubscriberSummary.subscribers.y</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">FirmSubscriberSummary.AccountInfo1.NewTabularHierarchy11.x</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">FirmSubscriberSummary.AccountInfo1.NewTabularHierarchy11.y</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">FirmSubscriberSummary.subscribers.width</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">FirmSubscriberSummary.subscribers.height</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">FirmSubscriberSummary.AccountInfo1.NewTabularHierarchy11.width</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
        <structure>
            <property name="name">FirmSubscriberSummary.AccountInfo1.NewTabularHierarchy11.height</property>
            <property name="type">integer</property>
            <property name="isVisible">false</property>
        </structure>
    </list-property>
    <property name="Subscribers DataCube.subscribers.x">306</property>
    <property name="Subscribers DataCube.subscribers.y">28</property>
    <property name="Subscribers DataCube.AccountInfo.NewTabularHierarchy1.x">42</property>
    <property name="Subscribers DataCube.AccountInfo.NewTabularHierarchy1.y">21</property>
    <property name="Subscribers DataCube.subscribers.width">150</property>
    <property name="Subscribers DataCube.subscribers.height">200</property>
    <property name="Subscribers DataCube.AccountInfo.NewTabularHierarchy1.width">150</property>
    <property name="Subscribers DataCube.AccountInfo.NewTabularHierarchy1.height">200</property>
    <property name="joinedSubsCube.joinSubscribers.x">135</property>
    <property name="joinedSubsCube.joinSubscribers.y">80</property>
    <property name="FirmSubscriberSummary.subscribers.x">395</property>
    <property name="FirmSubscriberSummary.subscribers.y">141</property>
    <property name="FirmSubscriberSummary.AccountInfo1.NewTabularHierarchy11.x">122</property>
    <property name="FirmSubscriberSummary.AccountInfo1.NewTabularHierarchy11.y">123</property>
    <property name="FirmSubscriberSummary.subscribers.width">150</property>
    <property name="FirmSubscriberSummary.subscribers.height">200</property>
    <property name="FirmSubscriberSummary.AccountInfo1.NewTabularHierarchy11.width">150</property>
    <property name="FirmSubscriberSummary.AccountInfo1.NewTabularHierarchy11.height">200</property>
    <list-property name="propertyBindings">
        <structure>
            <property name="name">FILELIST</property>
            <property name="id">64</property>
            <expression name="value" type="javascript">params["xmlFilePath"].value</expression>
        </structure>
        <structure>
            <property name="name">ENCODINGLIST</property>
            <property name="id">64</property>
        </structure>
        <structure>
            <property name="name">SCHEMAFILELIST</property>
            <property name="id">64</property>
        </structure>
        <structure>
            <property name="name">OdaConnProfileName</property>
            <property name="id">64</property>
        </structure>
        <structure>
            <property name="name">OdaConnProfileStorePath</property>
            <property name="id">64</property>
        </structure>
    </list-property>
    <property name="units">in</property>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">96</property>
    <parameters>
        <scalar-parameter name="xmlFilePath" id="63">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">C:/backendroot/models/reports/subscriptions/test.xml</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="invoiceheaderimgurl" id="553">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">https://www.membercentral.com/userassets/common/invoices/</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="renewalstatementtitle" id="824">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">Renewal Statement</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">></property>
                <property name="pattern">></property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="renewalsuburl" id="1074">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="frmrenewonlineoptions" id="1075">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">boolean</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">false</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">check-box</property>
            <structure name="format"/>
        </scalar-parameter>
        <scalar-parameter name="frmsubscribertopcontent" id="1053">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="frmsubscriberbottomcontent" id="1054">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant"></value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="showTax" id="1032">
            <property name="valueType">static</property>
            <property name="isRequired">false</property>
            <property name="dataType">boolean</property>
            <property name="distinct">true</property>
            <simple-property-list name="defaultValue">
                <value type="constant">false</value>
            </simple-property-list>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">check-box</property>
            <structure name="format"/>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.datatools.enablement.oda.xml" name="xmlDataSource" id="64">
            <property name="FILELIST">C:/backendroot/models/reports/subscriptions/paperStatementTest.xml</property>
        </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="accounts" id="73">
            <property name="nullsOrdering">nulls lowest</property>
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">childmemberID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">childmemberID</text-property>
                </structure>
                <structure>
                    <property name="columnName">firmmemberid</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">firmmemberid</text-property>
                </structure>
                <structure>
                    <property name="columnName">renewalcode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">renewalcode</text-property>
                </structure>
                <structure>
                    <property name="columnName">hassubscription</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">hassubscription</text-property>
                </structure>
                <structure>
                    <property name="columnName">firstname</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">firstname</text-property>
                </structure>
                <structure>
                    <property name="columnName">lastname</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">lastname</text-property>
                </structure>
                <structure>
                    <property name="columnName">suffix</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">suffix</text-property>
                </structure>
                <structure>
                    <property name="columnName">company</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">company</text-property>
                </structure>
                <structure>
                    <property name="columnName">namestring</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">namestring</text-property>
                </structure>
                <structure>
                    <property name="columnName">address1</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">address1</text-property>
                </structure>
                <structure>
                    <property name="columnName">address2</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">address2</text-property>
                </structure>
                <structure>
                    <property name="columnName">city</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">city</text-property>
                </structure>
                <structure>
                    <property name="columnName">state</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">state</text-property>
                </structure>
                <structure>
                    <property name="columnName">postalcode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">postalcode</text-property>
                </structure>
                <structure>
                    <property name="columnName">country</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">country</text-property>
                </structure>
                <structure>
                    <property name="columnName">address3</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">address3</text-property>
                </structure>
                <structure>
                    <property name="columnName">childmembernumber</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">childmembernumber</text-property>
                </structure>
                <structure>
                    <property name="columnName">renewalsubqrcodeimgurl</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">renewalsubqrcodeimgurl</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">childmemberID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">firmmemberid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">renewalcode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">hassubscription</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">firstname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">lastname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">suffix</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">company</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">namestring</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">address1</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">address2</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">city</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">state</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">postalcode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">country</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">address3</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">childmembernumber</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">renewalsubqrcodeimgurl</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">xmlDataSource</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">childmemberID</property>
                    <property name="nativeName">childmemberID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">firmmemberid</property>
                    <property name="nativeName">firmmemberid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">renewalcode</property>
                    <property name="nativeName">renewalcode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">hassubscription</property>
                    <property name="nativeName">hassubscription</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">firstname</property>
                    <property name="nativeName">firstname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">lastname</property>
                    <property name="nativeName">lastname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">suffix</property>
                    <property name="nativeName">suffix</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">company</property>
                    <property name="nativeName">company</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">namestring</property>
                    <property name="nativeName">namestring</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">address1</property>
                    <property name="nativeName">address1</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">address2</property>
                    <property name="nativeName">address2</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">city</property>
                    <property name="nativeName">city</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">state</property>
                    <property name="nativeName">state</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">postalcode</property>
                    <property name="nativeName">postalcode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">country</property>
                    <property name="nativeName">country</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">address3</property>
                    <property name="nativeName">address3</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">childmembernumber</property>
                    <property name="nativeName">childmembernumber</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">renewalsubqrcodeimgurl</property>
                    <property name="nativeName">renewalsubqrcodeimgurl</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/accounts/account]#:#{childmemberID;STRING;/@childmemberID},{firmmemberid;STRING;/@firmmemberid},{renewalcode;STRING;/@renewalcode},{hassubscription;STRING;/@hassubscription},{firstname;STRING;/@firstname},{lastname;STRING;/@lastname},{suffix;STRING;/@suffix},{company;STRING;/@company},{namestring;STRING;/@namestring},{address1;STRING;/@address1},{address2;STRING;/@address2},{city;STRING;/@city},{state;STRING;/@state},{postalcode;STRING;/@postalcode},{country;STRING;/@country},{address3;STRING;/@address3},{childmembernumber;STRING;/@childmembernumber},{renewalsubqrcodeimgurl;STRING;/@renewalsubqrcodeimgurl}]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>childmemberID</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>childmemberID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>firmmemberid</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>firmmemberid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>hassubscription</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>hassubscription</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>firstname</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>firstname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>lastname</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>lastname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>suffix</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>suffix</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>company</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>company</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>25</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="subscribers" id="74">
            <property name="nullsOrdering">nulls lowest</property>
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">subscriberID</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriberID</text-property>
                </structure>
                <structure>
                    <property name="columnName">activeMemberID</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">activeMemberID</text-property>
                </structure>
                <structure>
                    <property name="columnName">subscriptionid</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriptionid</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">typeid</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">typeid</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">typename</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">typename</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">subscriptionname</property>
                    <property name="analysis">attribute</property>
                    <text-property name="displayName">Item Description</text-property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriptionname</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">ratename</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ratename</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">itemdesc</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">itemdesc</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">frequencyname</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">frequencyname</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">status</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">status</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">statusname</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">statusname</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">substartdate</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">substartdate</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">subenddate</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subenddate</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">graceenddate</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">graceenddate</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">rootsubscriberid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">rootsubscriberid</text-property>
                </structure>
                <structure>
                    <property name="columnName">rfid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">rfid</text-property>
                </structure>
                <structure>
                    <property name="columnName">lastprice</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">lastprice</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamount</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">Billed</text-property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamount</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamountpaid</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">Paid</text-property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamountpaid</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamountdue</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">Due</text-property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamountdue</text-property>
                </structure>
                <structure>
                    <property name="columnName">subscriberpath</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriberpath</text-property>
                </structure>
                <structure>
                    <property name="columnName">parentsubscriberid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">parentsubscriberid</text-property>
                </structure>
                <structure>
                    <property name="columnName">subdaterange</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subdaterange</text-property>
                </structure>
                <structure>
                    <property name="columnName">invoicecontentversionid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">invoicecontentversionid</text-property>
                </structure>
                <structure>
                    <property name="columnName">invoicecontentfootnotenumber</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">invoicecontentfootnotenumber</text-property>
                </structure>
                <structure>
                    <property name="columnName">coversheettypesortorder</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">coversheettypesortorder</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamounttax</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">Tax</text-property>
                    <text-property name="heading">subamounttax</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">subscriberID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">activeMemberID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">subscriptionid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">typeid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">typename</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">subscriptionname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">ratename</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">itemdesc</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">frequencyname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">status</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">statusname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">substartdate</property>
                        <property name="dataType">date</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">subenddate</property>
                        <property name="dataType">date</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">graceenddate</property>
                        <property name="dataType">date</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">rootsubscriberid</property>
                        <property name="dataType">integer</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">rfid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">lastprice</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">subamount</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">subamountpaid</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">subamountdue</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">subscriberpath</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">parentsubscriberid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">subdaterange</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">invoicecontentversionid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">invoicecontentfootnotenumber</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">coversheettypesortorder</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">subamounttax</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">xmlDataSource</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">subscriberID</property>
                    <property name="nativeName">subscriberID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">activeMemberID</property>
                    <property name="nativeName">activeMemberID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">subscriptionid</property>
                    <property name="nativeName">subscriptionid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">typeid</property>
                    <property name="nativeName">typeid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">typename</property>
                    <property name="nativeName">typename</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">subscriptionname</property>
                    <property name="nativeName">subscriptionname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">ratename</property>
                    <property name="nativeName">ratename</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">itemdesc</property>
                    <property name="nativeName">itemdesc</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">frequencyname</property>
                    <property name="nativeName">frequencyname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">status</property>
                    <property name="nativeName">status</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">statusname</property>
                    <property name="nativeName">statusname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">substartdate</property>
                    <property name="nativeName">substartdate</property>
                    <property name="dataType">date</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">subenddate</property>
                    <property name="nativeName">subenddate</property>
                    <property name="dataType">date</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">graceenddate</property>
                    <property name="nativeName">graceenddate</property>
                    <property name="dataType">date</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">rootsubscriberid</property>
                    <property name="nativeName">rootsubscriberid</property>
                    <property name="dataType">integer</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">rfid</property>
                    <property name="nativeName">rfid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">lastprice</property>
                    <property name="nativeName">lastprice</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">subamount</property>
                    <property name="nativeName">subamount</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">subamountpaid</property>
                    <property name="nativeName">subamountpaid</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">subamountdue</property>
                    <property name="nativeName">subamountdue</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">subscriberpath</property>
                    <property name="nativeName">subscriberpath</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">parentsubscriberid</property>
                    <property name="nativeName">parentsubscriberid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">subdaterange</property>
                    <property name="nativeName">subdaterange</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">invoicecontentversionid</property>
                    <property name="nativeName">invoicecontentversionid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">invoicecontentfootnotenumber</property>
                    <property name="nativeName">invoicecontentfootnotenumber</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">coversheettypesortorder</property>
                    <property name="nativeName">coversheettypesortorder</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">subamounttax</property>
                    <property name="nativeName">subamounttax</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/accounts/account/subscriptiontree/subscriber]#:#{subscriberID;STRING;/@subscriberID},{activeMemberID;STRING;/@activeMemberID},{subscriptionid;STRING;/@subscriptionid},{typeid;STRING;/@typeid},{typename;STRING;/@typename},{subscriptionname;STRING;/@subscriptionname},{ratename;STRING;/@ratename},{itemdesc;STRING;/@itemdesc},{frequencyname;STRING;/@frequencyname},{status;STRING;/@status},{statusname;STRING;/@statusname},{substartdate;STRING;/@substartdate},{subenddate;STRING;/@subenddate},{graceenddate;STRING;/@graceenddate},{rootsubscriberid;STRING;/@rootsubscriberid},{rfid;STRING;/@rfid},{lastprice;STRING;/@lastprice},{subamount;STRING;/@subamount},{subamountpaid;STRING;/@subamountpaid},{subamountdue;STRING;/@subamountdue},{subscriberpath;STRING;/@subscriberpath},{parentsubscriberid;STRING;/@parentsubscriberid},{subdaterange;STRING;/@subdaterange},{invoicecontentversionid;STRING;/@invoicecontentversionid},{invoicecontentfootnotenumber;STRING;/@invoicecontentfootnotenumber},{coversheettypesortorder;STRING;/@coversheettypesortorder},{subamounttax;STRING;/@subamounttax}]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriberID</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriberID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>activeMemberID</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>activeMemberID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriptionid</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriptionid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>typeid</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>typeid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>typename</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>typename</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriptionname</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriptionname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>ratename</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>ratename</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>itemdesc</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>itemdesc</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>frequencyname</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>frequencyname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>status</design:name>
              <design:position>10</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>status</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>statusname</design:name>
              <design:position>11</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>statusname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>substartdate</design:name>
              <design:position>12</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>substartdate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subenddate</design:name>
              <design:position>13</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subenddate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>graceenddate</design:name>
              <design:position>14</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>graceenddate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>rootsubscriberid</design:name>
              <design:position>15</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>rootsubscriberid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>rfid</design:name>
              <design:position>16</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>rfid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>lastprice</design:name>
              <design:position>17</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>lastprice</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamount</design:name>
              <design:position>18</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamount</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamountpaid</design:name>
              <design:position>19</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamountpaid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamountdue</design:name>
              <design:position>20</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamountdue</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriberpath</design:name>
              <design:position>21</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriberpath</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>parentsubscriberid</design:name>
              <design:position>22</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>parentsubscriberid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>25</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="rootSubscribers" id="236">
            <property name="nullsOrdering">nulls lowest</property>
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">subscriberID</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriberID</text-property>
                </structure>
                <structure>
                    <property name="columnName">activeMemberID</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">activeMemberID</text-property>
                </structure>
                <structure>
                    <property name="columnName">subscriptionid</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriptionid</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">typeid</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">typeid</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">typename</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">typename</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">subscriptionname</property>
                    <property name="analysis">attribute</property>
                    <text-property name="displayName">Item Description</text-property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriptionname</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">ratename</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">ratename</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">itemdesc</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">itemdesc</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">frequencyname</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">frequencyname</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">status</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">status</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">statusname</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">statusname</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">substartdate</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">substartdate</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">subenddate</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subenddate</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">graceenddate</property>
                    <property name="analysis">attribute</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">graceenddate</text-property>
                    <property name="analysisColumn">subscriberID</property>
                </structure>
                <structure>
                    <property name="columnName">rootsubscriberid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">rootsubscriberid</text-property>
                </structure>
                <structure>
                    <property name="columnName">rfid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">rfid</text-property>
                </structure>
                <structure>
                    <property name="columnName">lastprice</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">lastprice</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamount</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">Billed</text-property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamount</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamountpaid</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">Paid</text-property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamountpaid</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamountdue</property>
                    <property name="analysis">measure</property>
                    <text-property name="displayName">Due</text-property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamountdue</text-property>
                </structure>
                <structure>
                    <property name="columnName">subscriberpath</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriberpath</text-property>
                </structure>
                <structure>
                    <property name="columnName">parentsubscriberid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">parentsubscriberid</text-property>
                </structure>
                <structure>
                    <property name="columnName">subdaterange</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subdaterange</text-property>
                </structure>
                <structure>
                    <property name="columnName">invoicecontentversionid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">invoicecontentversionid</text-property>
                </structure>
                <structure>
                    <property name="columnName">invoicecontentfootnotenumber</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">invoicecontentfootnotenumber</text-property>
                </structure>
                <structure>
                    <property name="columnName">subtreeinvoiceprofileimage</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">subtreeinvoiceprofileimage</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamounttax</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">Tax</text-property>
                    <text-property name="heading">subamounttax</text-property>
                </structure>
            </list-property>
            <list-property name="filter">
                <structure>
                    <property name="operator">eq</property>
                    <expression name="expr" type="javascript">row["rootsubscriberid"]</expression>
                    <simple-property-list name="value1">
                        <value type="javascript">row["subscriberID"]</value>
                    </simple-property-list>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">subscriberID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">activeMemberID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">subscriptionid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">typeid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">typename</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">subscriptionname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">ratename</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">itemdesc</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">frequencyname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">status</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">11</property>
                        <property name="name">statusname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">12</property>
                        <property name="name">substartdate</property>
                        <property name="dataType">date</property>
                    </structure>
                    <structure>
                        <property name="position">13</property>
                        <property name="name">subenddate</property>
                        <property name="dataType">date</property>
                    </structure>
                    <structure>
                        <property name="position">14</property>
                        <property name="name">graceenddate</property>
                        <property name="dataType">date</property>
                    </structure>
                    <structure>
                        <property name="position">15</property>
                        <property name="name">rootsubscriberid</property>
                        <property name="dataType">integer</property>
                    </structure>
                    <structure>
                        <property name="position">16</property>
                        <property name="name">rfid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">17</property>
                        <property name="name">lastprice</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">18</property>
                        <property name="name">subamount</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">19</property>
                        <property name="name">subamountpaid</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">20</property>
                        <property name="name">subamountdue</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">21</property>
                        <property name="name">subscriberpath</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">22</property>
                        <property name="name">parentsubscriberid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">23</property>
                        <property name="name">subdaterange</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">24</property>
                        <property name="name">invoicecontentversionid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">25</property>
                        <property name="name">invoicecontentfootnotenumber</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">26</property>
                        <property name="name">subtreeinvoiceprofileimage</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">27</property>
                        <property name="name">subamounttax</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">xmlDataSource</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">subscriberID</property>
                    <property name="nativeName">subscriberID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">activeMemberID</property>
                    <property name="nativeName">activeMemberID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">subscriptionid</property>
                    <property name="nativeName">subscriptionid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">typeid</property>
                    <property name="nativeName">typeid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">typename</property>
                    <property name="nativeName">typename</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">subscriptionname</property>
                    <property name="nativeName">subscriptionname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">ratename</property>
                    <property name="nativeName">ratename</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">itemdesc</property>
                    <property name="nativeName">itemdesc</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">frequencyname</property>
                    <property name="nativeName">frequencyname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">status</property>
                    <property name="nativeName">status</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">11</property>
                    <property name="name">statusname</property>
                    <property name="nativeName">statusname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">12</property>
                    <property name="name">substartdate</property>
                    <property name="nativeName">substartdate</property>
                    <property name="dataType">date</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">13</property>
                    <property name="name">subenddate</property>
                    <property name="nativeName">subenddate</property>
                    <property name="dataType">date</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">14</property>
                    <property name="name">graceenddate</property>
                    <property name="nativeName">graceenddate</property>
                    <property name="dataType">date</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">15</property>
                    <property name="name">rootsubscriberid</property>
                    <property name="nativeName">rootsubscriberid</property>
                    <property name="dataType">integer</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">16</property>
                    <property name="name">rfid</property>
                    <property name="nativeName">rfid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">17</property>
                    <property name="name">lastprice</property>
                    <property name="nativeName">lastprice</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">18</property>
                    <property name="name">subamount</property>
                    <property name="nativeName">subamount</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">19</property>
                    <property name="name">subamountpaid</property>
                    <property name="nativeName">subamountpaid</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">20</property>
                    <property name="name">subamountdue</property>
                    <property name="nativeName">subamountdue</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">21</property>
                    <property name="name">subscriberpath</property>
                    <property name="nativeName">subscriberpath</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">22</property>
                    <property name="name">parentsubscriberid</property>
                    <property name="nativeName">parentsubscriberid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">23</property>
                    <property name="name">subdaterange</property>
                    <property name="nativeName">subdaterange</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">24</property>
                    <property name="name">invoicecontentversionid</property>
                    <property name="nativeName">invoicecontentversionid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">25</property>
                    <property name="name">invoicecontentfootnotenumber</property>
                    <property name="nativeName">invoicecontentfootnotenumber</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">26</property>
                    <property name="name">subtreeinvoiceprofileimage</property>
                    <property name="nativeName">subtreeinvoiceprofileimage</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">27</property>
                    <property name="name">subamounttax</property>
                    <property name="nativeName">subamounttax</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/accounts/account/subscriptiontree/subscriber]#:#{subscriberID;STRING;/@subscriberID},{activeMemberID;STRING;/@activeMemberID},{subscriptionid;STRING;/@subscriptionid},{typeid;STRING;/@typeid},{typename;STRING;/@typename},{subscriptionname;STRING;/@subscriptionname},{ratename;STRING;/@ratename},{itemdesc;STRING;/@itemdesc},{frequencyname;STRING;/@frequencyname},{status;STRING;/@status},{statusname;STRING;/@statusname},{substartdate;STRING;/@substartdate},{subenddate;STRING;/@subenddate},{graceenddate;STRING;/@graceenddate},{rootsubscriberid;STRING;/@rootsubscriberid},{rfid;STRING;/@rfid},{lastprice;STRING;/@lastprice},{subamount;STRING;/@subamount},{subamountpaid;STRING;/@subamountpaid},{subamountdue;STRING;/@subamountdue},{subscriberpath;STRING;/@subscriberpath},{parentsubscriberid;STRING;/@parentsubscriberid},{subdaterange;STRING;/@subdaterange},{invoicecontentversionid;STRING;/@invoicecontentversionid},{invoicecontentfootnotenumber;STRING;/@invoicecontentfootnotenumber},{subtreeinvoiceprofileimage;STRING;../@subtreeinvoiceprofileimage},{subamounttax;STRING;/@subamounttax}]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriberID</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriberID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>activeMemberID</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>activeMemberID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriptionid</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriptionid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>typeid</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>typeid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>typename</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>typename</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriptionname</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriptionname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>ratename</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>ratename</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>itemdesc</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>itemdesc</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>frequencyname</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>frequencyname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>status</design:name>
              <design:position>10</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>status</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>statusname</design:name>
              <design:position>11</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>statusname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>substartdate</design:name>
              <design:position>12</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>substartdate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subenddate</design:name>
              <design:position>13</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subenddate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>graceenddate</design:name>
              <design:position>14</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>graceenddate</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>rootsubscriberid</design:name>
              <design:position>15</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>rootsubscriberid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>rfid</design:name>
              <design:position>16</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>rfid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>lastprice</design:name>
              <design:position>17</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>lastprice</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamount</design:name>
              <design:position>18</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamount</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamountpaid</design:name>
              <design:position>19</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamountpaid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamountdue</design:name>
              <design:position>20</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamountdue</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriberpath</design:name>
              <design:position>21</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriberpath</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>parentsubscriberid</design:name>
              <design:position>22</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>parentsubscriberid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="footnote" id="593">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">invoicecontentfootnotenumber</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">invoicecontentfootnotenumber</text-property>
                </structure>
                <structure>
                    <property name="columnName">contentversionid</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">contentversionid</text-property>
                </structure>
                <structure>
                    <property name="columnName">rawcontent</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">rawcontent</text-property>
                </structure>
                <structure>
                    <property name="columnName">rootsubscriberID</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">rootsubscriberID</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">invoicecontentfootnotenumber</property>
                        <property name="dataType">integer</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">contentversionid</property>
                        <property name="dataType">integer</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">rawcontent</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">rootsubscriberID</property>
                        <property name="dataType">integer</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">xmlDataSource</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">invoicecontentfootnotenumber</property>
                    <property name="nativeName">invoicecontentfootnotenumber</property>
                    <property name="dataType">integer</property>
                    <property name="nativeDataType">4</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">contentversionid</property>
                    <property name="nativeName">contentversionid</property>
                    <property name="dataType">integer</property>
                    <property name="nativeDataType">4</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">rawcontent</property>
                    <property name="nativeName">rawcontent</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">rootsubscriberID</property>
                    <property name="nativeName">rootsubscriberID</property>
                    <property name="dataType">integer</property>
                    <property name="nativeDataType">4</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/accounts/account/subscriptiontree/invoicemessages/invoicemessage]#:#{invoicecontentfootnotenumber;INT;/@invoicecontentfootnotenumber},{contentversionid;INT;/@contentversionid},{rawcontent;STRING;/@rawcontent},{rootsubscriberID;INT;/@rootsubscriberID}]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>invoicecontentfootnotenumber</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>4</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>invoicecontentfootnotenumber</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>contentversionid</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>4</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>contentversionid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>rawcontent</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>rawcontent</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="subscriptionTrees" id="604">
            <property name="nullsOrdering">nulls lowest</property>
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">rootsubscriberid</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">rootsubscriberid</text-property>
                </structure>
                <structure>
                    <property name="columnName">activeMemberID</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">activeMemberID</text-property>
                </structure>
                <structure>
                    <property name="columnName">namestring</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">namestring</text-property>
                </structure>
                <structure>
                    <property name="columnName">subscriptionname</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subscriptionname</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamount_total</property>
                    <property name="analysis">measure</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamount_total</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamountpaid_total</property>
                    <property name="analysis">measure</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamountpaid_total</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamountdue_total</property>
                    <property name="analysis">measure</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subamountdue_total</text-property>
                </structure>
                <structure>
                    <property name="columnName">subtreedaterange</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subtreedaterange</text-property>
                </structure>
                <structure>
                    <property name="columnName">subtreeinvoiceprofileimage</property>
                    <property name="analysis">dimension</property>
                    <property name="onColumnLayout">false</property>
                    <text-property name="heading">subtreeinvoiceprofileimage</text-property>
                </structure>
                <structure>
                    <property name="columnName">subamounttax_total</property>
                    <property name="analysis">measure</property>
                    <text-property name="heading">subamounttax_total</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">rootsubscriberid</property>
                        <property name="dataType">integer</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">activeMemberID</property>
                        <property name="dataType">integer</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">namestring</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">subscriptionname</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">subamount_total</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">subamountpaid_total</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">subamountdue_total</property>
                        <property name="dataType">float</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">subtreedaterange</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">subtreeinvoiceprofileimage</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">subamounttax_total</property>
                        <property name="dataType">float</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">xmlDataSource</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">rootsubscriberid</property>
                    <property name="nativeName">rootsubscriberid</property>
                    <property name="dataType">integer</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">activeMemberID</property>
                    <property name="nativeName">activeMemberID</property>
                    <property name="dataType">integer</property>
                    <property name="nativeDataType">4</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">namestring</property>
                    <property name="nativeName">namestring</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">subscriptionname</property>
                    <property name="nativeName">subscriptionname</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">subamount_total</property>
                    <property name="nativeName">subamount_total</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">8</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">subamountpaid_total</property>
                    <property name="nativeName">subamountpaid_total</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">8</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">subamountdue_total</property>
                    <property name="nativeName">subamountdue_total</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">8</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">subtreedaterange</property>
                    <property name="nativeName">subtreedaterange</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">subtreeinvoiceprofileimage</property>
                    <property name="nativeName">subtreeinvoiceprofileimage</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">subamounttax_total</property>
                    <property name="nativeName">subamounttax_total</property>
                    <property name="dataType">float</property>
                    <property name="nativeDataType">8</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/accounts/account/subscriptiontree]#:#{rootsubscriberid;STRING;/@rootsubscriberid},{activeMemberID;INT;/@activeMemberID},{namestring;STRING;/@namestring},{subscriptionname;STRING;/@subscriptionname},{subamount_total;DOUBLE;/@subamount_total},{subamountpaid_total;DOUBLE;/@subamountpaid_total},{subamountdue_total;DOUBLE;/@subamountdue_total},{subtreedaterange;STRING;/@subtreedaterange},{subtreeinvoiceprofileimage;STRING;/@subtreeinvoiceprofileimage},{subamounttax_total;DOUBLE;/@subamounttax_total}]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>rootsubscriberid</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>rootsubscriberid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>activeMemberID</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>4</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>activeMemberID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>namestring</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>namestring</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subscriptionname</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subscriptionname</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamount_total</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>8</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamount_total</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamountpaid_total</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>8</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamountpaid_total</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subamountdue_total</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>8</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subamountdue_total</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subtreedaterange</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subtreedaterange</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>subtreeinvoiceprofileimage</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>subtreeinvoiceprofileimage</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>25</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="payments" id="828">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">rootsubscriberID</property>
                    <property name="alias">rootsubscriberID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">rootsubscriberID</text-property>
                </structure>
                <structure>
                    <property name="columnName">transactionID</property>
                    <property name="alias">transactionID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">transactionID</text-property>
                </structure>
                <structure>
                    <property name="columnName">datebilled</property>
                    <property name="alias">datebilled</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">datebilled</text-property>
                </structure>
                <structure>
                    <property name="columnName">detail</property>
                    <property name="alias">detail</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">detail</text-property>
                </structure>
                <structure>
                    <property name="columnName">allocatedAmount</property>
                    <property name="alias">allocatedAmount</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">allocatedAmount</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">rootsubscriberID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">transactionID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">datebilled</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">detail</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">allocatedAmount</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">xmlDataSource</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">rootsubscriberID</property>
                    <property name="nativeName">rootsubscriberID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">transactionID</property>
                    <property name="nativeName">transactionID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">datebilled</property>
                    <property name="nativeName">datebilled</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">detail</property>
                    <property name="nativeName">detail</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">allocatedAmount</property>
                    <property name="nativeName">allocatedAmount</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/accounts/account/subscriptiontree/payments/payment]#:#{rootsubscriberID;STRING;/@rootsubscriberID},{transactionID;STRING;/@transactionID},{datebilled;STRING;/@datebilled},{detail;STRING;/@detail},{allocatedAmount;STRING;/@allocatedAmount}]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>rootsubscriberID</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>rootsubscriberID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>transactionID</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>transactionID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>datebilled</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>datebilled</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>detail</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>detail</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>allocatedAmount</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>allocatedAmount</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
        <oda-data-set extensionID="org.eclipse.datatools.enablement.oda.xml.dataSet" name="invoices" id="843">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">rootsubscriberID</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">rootsubscriberID</text-property>
                </structure>
                <structure>
                    <property name="columnName">invoiceid</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">invoiceid</text-property>
                </structure>
                <structure>
                    <property name="columnName">invoicecode</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">invoicecode</text-property>
                </structure>
                <structure>
                    <property name="columnName">datebilled</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">datebilled</text-property>
                </structure>
                <structure>
                    <property name="columnName">datedue</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">datedue</text-property>
                </structure>
                <structure>
                    <property name="columnName">statusid</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">statusid</text-property>
                </structure>
                <structure>
                    <property name="columnName">status</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">status</text-property>
                </structure>
                <structure>
                    <property name="columnName">amount</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">amount</text-property>
                </structure>
                <structure>
                    <property name="columnName">amountDue</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">amountDue</text-property>
                </structure>
                <structure>
                    <property name="columnName">payprofiledesc</property>
                    <property name="analysis">dimension</property>
                    <text-property name="heading">payprofiledesc</text-property>
                </structure>
            </list-property>
            <list-property name="parameters"/>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">rootsubscriberID</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">invoiceid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">invoicecode</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">datebilled</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">datedue</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">statusid</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">status</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">8</property>
                        <property name="name">amount</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">9</property>
                        <property name="name">amountDue</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">10</property>
                        <property name="name">payprofiledesc</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">xmlDataSource</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">rootsubscriberID</property>
                    <property name="nativeName">rootsubscriberID</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">invoiceid</property>
                    <property name="nativeName">invoiceid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">invoicecode</property>
                    <property name="nativeName">invoicecode</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">datebilled</property>
                    <property name="nativeName">datebilled</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">datedue</property>
                    <property name="nativeName">datedue</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">statusid</property>
                    <property name="nativeName">statusid</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">status</property>
                    <property name="nativeName">status</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">8</property>
                    <property name="name">amount</property>
                    <property name="nativeName">amount</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">9</property>
                    <property name="name">amountDue</property>
                    <property name="nativeName">amountDue</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">10</property>
                    <property name="name">payprofiledesc</property>
                    <property name="nativeName">payprofiledesc</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[table0#-TNAME-#table0#:#[/accounts/account/subscriptiontree/invoices/invoice]#:#{rootsubscriberID;STRING;/@rootsubscriberID},{invoiceid;STRING;/@invoiceid},{invoicecode;STRING;/@invoicecode},{datebilled;STRING;/@datebilled},{datedue;STRING;/@datedue},{statusid;STRING;/@statusid},{status;STRING;/@status},{amount;STRING;/@amount},{amountDue;STRING;/@amountDue},{payprofiledesc;STRING;/@payprofiledesc}]]></xml-property>
            <xml-property name="designerValues"><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<model:DesignValues xmlns:design="http://www.eclipse.org/datatools/connectivity/oda/design" xmlns:model="http://www.eclipse.org/birt/report/model/adapter/odaModel">
  <Version>2.0</Version>
  <design:ResultSets derivedMetaData="true">
    <design:resultSetDefinitions>
      <design:resultSetColumns>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>rootsubscriberID</design:name>
              <design:position>1</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>rootsubscriberID</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>invoiceid</design:name>
              <design:position>2</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>invoiceid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>invoicecode</design:name>
              <design:position>3</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>invoicecode</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>datebilled</design:name>
              <design:position>4</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>datebilled</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>datedue</design:name>
              <design:position>5</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>datedue</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>statusid</design:name>
              <design:position>6</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>statusid</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>status</design:name>
              <design:position>7</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>status</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>amount</design:name>
              <design:position>8</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>amount</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
        <design:resultColumnDefinitions>
          <design:attributes>
            <design:identifier>
              <design:name>amountDue</design:name>
              <design:position>9</design:position>
            </design:identifier>
            <design:nativeDataTypeCode>12</design:nativeDataTypeCode>
            <design:precision>-1</design:precision>
            <design:scale>-1</design:scale>
            <design:nullability>Unknown</design:nullability>
          </design:attributes>
          <design:usageHints>
            <design:label>amountDue</design:label>
            <design:formattingHints/>
          </design:usageHints>
        </design:resultColumnDefinitions>
      </design:resultSetColumns>
      <design:criteria/>
    </design:resultSetDefinitions>
  </design:ResultSets>
</model:DesignValues>]]></xml-property>
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>MAX_ROW</name>
                    <value>-1</value>
                </ex-property>
                <ex-property>
                    <name>XML_FILE</name>
                </ex-property>
            </list-property>
        </oda-data-set>
    </data-sets>
    <cubes>
        <tabular-cube name="Subscribers DataCube" id="91">
            <property name="dimensions">
                <tabular-dimension name="SubInfo" id="92">
                    <property name="defaultHierarchy">NewTabularHierarchy</property>
                    <property name="hierarchies">
                        <tabular-hierarchy name="NewTabularHierarchy" id="93">
                            <property name="levels">
                                <tabular-level name="activeMemberID" id="185">
                                    <property name="dataType">string</property>
                                    <property name="columnName">activeMemberID</property>
                                </tabular-level>
                                <tabular-level name="rootsubscriberid" id="186">
                                    <property name="dataType">integer</property>
                                    <property name="columnName">rootsubscriberid</property>
                                </tabular-level>
                                <tabular-level name="substartdate" id="204">
                                    <property name="dataType">date</property>
                                    <property name="columnName">substartdate</property>
                                </tabular-level>
                                <tabular-level name="subenddate" id="203">
                                    <property name="dataType">date</property>
                                    <property name="columnName">subenddate</property>
                                </tabular-level>
                                <tabular-level name="subscriberID" id="187">
                                    <property name="dataType">string</property>
                                    <property name="columnName">subscriberID</property>
                                </tabular-level>
                                <tabular-level name="subscriberpath" id="856">
                                    <property name="dataType">string</property>
                                    <property name="columnName">subscriberpath</property>
                                </tabular-level>
                                <tabular-level name="Item Description" id="94">
                                    <property name="dataType">string</property>
                                    <property name="columnName">itemdesc</property>
                                </tabular-level>
                                <tabular-level name="invoicecontentfootnotenumber" id="575">
                                    <property name="dataType">string</property>
                                    <property name="columnName">invoicecontentfootnotenumber</property>
                                </tabular-level>
                            </property>
                        </tabular-hierarchy>
                    </property>
                </tabular-dimension>
                <tabular-dimension name="AccountInfo" id="95">
                    <property name="defaultHierarchy">NewTabularHierarchy1</property>
                    <property name="hierarchies">
                        <tabular-hierarchy name="NewTabularHierarchy1" id="96">
                            <property name="levels">
                                <tabular-level name="firmmemberid" id="97">
                                    <property name="dataType">string</property>
                                    <property name="columnName">firmmemberid</property>
                                </tabular-level>
                                <tabular-level name="childmemberID" id="103">
                                    <property name="dataType">string</property>
                                    <property name="columnName">childmemberID</property>
                                </tabular-level>
                            </property>
                            <property name="dataSet">accounts</property>
                        </tabular-hierarchy>
                    </property>
                </tabular-dimension>
            </property>
            <property name="measureGroups">
                <tabular-measure-group name="Summary Field" id="98">
                    <property name="measures">
                        <tabular-measure name="TotalBilled" id="99">
                            <property name="function">sum</property>
                            <property name="isCalculated">false</property>
                            <expression name="measureExpression" type="javascript">dataSetRow["subamount"]</expression>
                            <property name="dataType">float</property>
                            <property name="isVisible">true</property>
                        </tabular-measure>
                        <tabular-measure name="TotalPaid" id="100">
                            <property name="function">sum</property>
                            <property name="isCalculated">false</property>
                            <expression name="measureExpression" type="javascript">dataSetRow["subamountpaid"]</expression>
                            <property name="dataType">float</property>
                            <property name="isVisible">true</property>
                        </tabular-measure>
                        <tabular-measure name="TotalDue" id="101">
                            <property name="function">sum</property>
                            <property name="isCalculated">false</property>
                            <expression name="measureExpression" type="javascript">dataSetRow["subamountdue"]</expression>
                            <property name="dataType">float</property>
                            <property name="isVisible">true</property>
                        </tabular-measure>
                        <tabular-measure name="subCount" id="102">
                            <property name="function">count</property>
                            <property name="isCalculated">false</property>
                            <expression name="measureExpression" type="javascript">dataSetRow["subscriberID"]</expression>
                            <property name="dataType">integer</property>
                            <property name="isVisible">true</property>
                        </tabular-measure>
                        <tabular-measure name="TotalTax" id="981">
                            <property name="function">sum</property>
                            <property name="isCalculated">false</property>
                            <expression name="measureExpression" type="javascript">dataSetRow["subamounttax"]</expression>
                            <property name="dataType">float</property>
                            <property name="isVisible">true</property>
                        </tabular-measure>
                    </property>
                </tabular-measure-group>
            </property>
            <property name="dataSet">subscribers</property>
            <list-property name="dimensionConditions">
                <structure>
                    <list-property name="joinConditions">
                        <structure>
                            <property name="cubeKey">activeMemberID</property>
                            <property name="hierarchyKey">childmemberID</property>
                        </structure>
                    </list-property>
                    <property name="hierarchy">NewTabularHierarchy1</property>
                </structure>
            </list-property>
        </tabular-cube>
        <tabular-cube name="subTreesCube" id="606">
            <property name="dimensions">
                <tabular-dimension name="subtree" id="611">
                    <property name="defaultHierarchy">NewTabularHierarchy3</property>
                    <property name="hierarchies">
                        <tabular-hierarchy name="NewTabularHierarchy3" id="612">
                            <property name="levels">
                                <tabular-level name="activeMemberID" id="613">
                                    <property name="dataType">integer</property>
                                    <property name="columnName">activeMemberID</property>
                                </tabular-level>
                                <tabular-level name="namestring" id="618">
                                    <property name="dataType">string</property>
                                    <property name="columnName">namestring</property>
                                </tabular-level>
                                <tabular-level name="subscriptionname" id="619">
                                    <property name="dataType">string</property>
                                    <property name="columnName">subscriptionname</property>
                                </tabular-level>
                                <tabular-level name="rootsubscriberid" id="614">
                                    <property name="dataType">string</property>
                                    <property name="columnName">rootsubscriberid</property>
                                </tabular-level>
                                <tabular-level name="subtreedaterange" id="617">
                                    <property name="dataType">string</property>
                                    <property name="columnName">subtreedaterange</property>
                                </tabular-level>
                            </property>
                        </tabular-hierarchy>
                    </property>
                </tabular-dimension>
            </property>
            <property name="measureGroups">
                <tabular-measure-group name="Summary Field2" id="607">
                    <property name="measures">
                        <tabular-measure name="subamount_total" id="608">
                            <expression name="measureExpression" type="javascript">dataSetRow["subamount_total"]</expression>
                            <property name="dataType">float</property>
                        </tabular-measure>
                        <tabular-measure name="subamountdue_total" id="609">
                            <expression name="measureExpression" type="javascript">dataSetRow["subamountdue_total"]</expression>
                            <property name="dataType">float</property>
                        </tabular-measure>
                        <tabular-measure name="subamountpaid_total" id="610">
                            <expression name="measureExpression" type="javascript">dataSetRow["subamountpaid_total"]</expression>
                            <property name="dataType">float</property>
                        </tabular-measure>
                        <tabular-measure name="subamounttax_total" id="982">
                            <property name="function">sum</property>
                            <property name="isCalculated">false</property>
                            <expression name="measureExpression" type="javascript">dataSetRow["subamounttax_total"]</expression>
                            <property name="dataType">float</property>
                            <property name="isVisible">true</property>
                        </tabular-measure>
                    </property>
                </tabular-measure-group>
                <tabular-measure-group name="Summary Field3" id="615"/>
            </property>
            <property name="dataSet">subscriptionTrees</property>
        </tabular-cube>
    </cubes>
    <styles>
        <style name="report" id="4">
            <property name="fontFamily">sans-serif</property>
            <property name="fontSize">10pt</property>
        </style>
        <style name="crosstab" id="5">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">1pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">1pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">1pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">1pt</property>
            <property name="paddingTop">0pt</property>
            <property name="paddingLeft">0pt</property>
            <property name="paddingBottom">0pt</property>
            <property name="paddingRight">0pt</property>
        </style>
        <style name="table" id="6">
            <property name="borderBottomColor">#CCCCCC</property>
            <property name="borderBottomStyle">solid</property>
            <property name="borderBottomWidth">0pt</property>
            <property name="borderLeftColor">#CCCCCC</property>
            <property name="borderLeftStyle">solid</property>
            <property name="borderLeftWidth">0pt</property>
            <property name="borderRightColor">#CCCCCC</property>
            <property name="borderRightStyle">solid</property>
            <property name="borderRightWidth">0pt</property>
            <property name="borderTopColor">#CCCCCC</property>
            <property name="borderTopStyle">solid</property>
            <property name="borderTopWidth">0pt</property>
        </style>
        <style name="crosstab_currency" id="551">
            <structure name="numberFormat">
                <property name="category">Currency</property>
                <property name="pattern">$#,##0.00{RoundingMode=UP}</property>
            </structure>
        </style>
    </styles>
    <page-setup>
        <simple-master-page name="Invoice" id="516">
            <property name="headerHeight">0.125in</property>
        </simple-master-page>
    </page-setup>
    <body>
        <list name="List-membersForInvoices" id="517">
            <property name="dataSet">accounts</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">childmemberID</property>
                    <text-property name="displayName">childmemberID</text-property>
                    <expression name="expression" type="javascript">dataSetRow["childmemberID"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">firmmemberid</property>
                    <text-property name="displayName">firmmemberid</text-property>
                    <expression name="expression" type="javascript">dataSetRow["firmmemberid"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">hassubscription</property>
                    <text-property name="displayName">hassubscription</text-property>
                    <expression name="expression" type="javascript">dataSetRow["hassubscription"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">firstname</property>
                    <text-property name="displayName">firstname</text-property>
                    <expression name="expression" type="javascript">dataSetRow["firstname"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">lastname</property>
                    <text-property name="displayName">lastname</text-property>
                    <expression name="expression" type="javascript">dataSetRow["lastname"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">suffix</property>
                    <text-property name="displayName">suffix</text-property>
                    <expression name="expression" type="javascript">dataSetRow["suffix"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">company</property>
                    <text-property name="displayName">company</text-property>
                    <expression name="expression" type="javascript">dataSetRow["company"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">namestring</property>
                    <text-property name="displayName">namestring</text-property>
                    <expression name="expression" type="javascript">dataSetRow["namestring"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">address1</property>
                    <text-property name="displayName">address1</text-property>
                    <expression name="expression" type="javascript">dataSetRow["address1"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">address2</property>
                    <text-property name="displayName">address2</text-property>
                    <expression name="expression" type="javascript">dataSetRow["address2"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">city</property>
                    <text-property name="displayName">city</text-property>
                    <expression name="expression" type="javascript">dataSetRow["city"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">state</property>
                    <text-property name="displayName">state</text-property>
                    <expression name="expression" type="javascript">dataSetRow["state"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">postalcode</property>
                    <text-property name="displayName">postalcode</text-property>
                    <expression name="expression" type="javascript">dataSetRow["postalcode"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">country</property>
                    <text-property name="displayName">country</text-property>
                    <expression name="expression" type="javascript">dataSetRow["country"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">address3</property>
                    <text-property name="displayName">address3</text-property>
                    <expression name="expression" type="javascript">dataSetRow["address3"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">childmembernumber</property>
                    <text-property name="displayName">childmembernumber</text-property>
                    <expression name="expression" type="javascript">dataSetRow["childmembernumber"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">renewalcode</property>
                    <text-property name="displayName">renewalcode</text-property>
                    <expression name="expression" type="javascript">dataSetRow["renewalcode"]</expression>
                    <property name="dataType">string</property>
                    <property name="allowExport">true</property>
                </structure>
                <structure>
                    <property name="name">renewalsubqrcodeimgurl</property>
                    <text-property name="displayName">renewalsubqrcodeimgurl</text-property>
                    <expression name="expression" type="javascript">dataSetRow["renewalsubqrcodeimgurl"]</expression>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <list-property name="filter">
                <structure>
                    <property name="operator">eq</property>
                    <expression name="expr" type="javascript">row["hassubscription"]</expression>
                    <simple-property-list name="value1">
                        <value type="javascript">1</value>
                    </simple-property-list>
                    <property name="updateAggregation">true</property>
                </structure>
            </list-property>
            <detail>
                <list name="InvoiceList" id="439">
                    <property name="masterPage">Invoice</property>
                    <property name="pageBreakBefore">always</property>
                    <property name="dataSet">rootSubscribers</property>
                    <list-property name="boundDataColumns">
                        <structure>
                            <property name="name">subscriberID</property>
                            <text-property name="displayName">subscriberID</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subscriberID"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">activeMemberID</property>
                            <text-property name="displayName">activeMemberID</text-property>
                            <expression name="expression" type="javascript">dataSetRow["activeMemberID"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">subscriptionid</property>
                            <text-property name="displayName">subscriptionid</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subscriptionid"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">typeid</property>
                            <text-property name="displayName">typeid</text-property>
                            <expression name="expression" type="javascript">dataSetRow["typeid"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">typename</property>
                            <text-property name="displayName">typename</text-property>
                            <expression name="expression" type="javascript">dataSetRow["typename"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">subscriptionname</property>
                            <text-property name="displayName">Item Description</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subscriptionname"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">ratename</property>
                            <text-property name="displayName">ratename</text-property>
                            <expression name="expression" type="javascript">dataSetRow["ratename"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">itemdesc</property>
                            <text-property name="displayName">itemdesc</text-property>
                            <expression name="expression" type="javascript">dataSetRow["itemdesc"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">frequencyname</property>
                            <text-property name="displayName">frequencyname</text-property>
                            <expression name="expression" type="javascript">dataSetRow["frequencyname"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">status</property>
                            <text-property name="displayName">status</text-property>
                            <expression name="expression" type="javascript">dataSetRow["status"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">statusname</property>
                            <text-property name="displayName">statusname</text-property>
                            <expression name="expression" type="javascript">dataSetRow["statusname"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">substartdate</property>
                            <text-property name="displayName">substartdate</text-property>
                            <expression name="expression" type="javascript">dataSetRow["substartdate"]</expression>
                            <property name="dataType">date</property>
                        </structure>
                        <structure>
                            <property name="name">subenddate</property>
                            <text-property name="displayName">subenddate</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subenddate"]</expression>
                            <property name="dataType">date</property>
                        </structure>
                        <structure>
                            <property name="name">graceenddate</property>
                            <text-property name="displayName">graceenddate</text-property>
                            <expression name="expression" type="javascript">dataSetRow["graceenddate"]</expression>
                            <property name="dataType">date</property>
                        </structure>
                        <structure>
                            <property name="name">rootsubscriberid</property>
                            <text-property name="displayName">rootsubscriberid</text-property>
                            <expression name="expression" type="javascript">dataSetRow["rootsubscriberid"]</expression>
                            <property name="dataType">integer</property>
                        </structure>
                        <structure>
                            <property name="name">rfid</property>
                            <text-property name="displayName">rfid</text-property>
                            <expression name="expression" type="javascript">dataSetRow["rfid"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">lastprice</property>
                            <text-property name="displayName">lastprice</text-property>
                            <expression name="expression" type="javascript">dataSetRow["lastprice"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">subamount</property>
                            <text-property name="displayName">Billed</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subamount"]</expression>
                            <property name="dataType">float</property>
                        </structure>
                        <structure>
                            <property name="name">subamountpaid</property>
                            <text-property name="displayName">Paid</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subamountpaid"]</expression>
                            <property name="dataType">float</property>
                        </structure>
                        <structure>
                            <property name="name">subamountdue</property>
                            <text-property name="displayName">Due</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subamountdue"]</expression>
                            <property name="dataType">float</property>
                        </structure>
                        <structure>
                            <property name="name">subscriberpath</property>
                            <text-property name="displayName">subscriberpath</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subscriberpath"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">parentsubscriberid</property>
                            <text-property name="displayName">parentsubscriberid</text-property>
                            <expression name="expression" type="javascript">dataSetRow["parentsubscriberid"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">subdaterange</property>
                            <text-property name="displayName">subdaterange</text-property>
                            <expression name="expression" type="javascript">dataSetRow["subdaterange"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">invoicecontentversionid</property>
                            <text-property name="displayName">invoicecontentversionid</text-property>
                            <expression name="expression" type="javascript">dataSetRow["invoicecontentversionid"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">invoicecontentfootnotenumber</property>
                            <text-property name="displayName">invoicecontentfootnotenumber</text-property>
                            <expression name="expression" type="javascript">dataSetRow["invoicecontentfootnotenumber"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                        <structure>
                            <property name="name">subtreeinvoiceprofileimage</property>
                            <expression name="expression" type="javascript">dataSetRow["subtreeinvoiceprofileimage"]</expression>
                            <property name="dataType">string</property>
                        </structure>
                    </list-property>
                    <property name="repeatHeader">true</property>
                    <property name="pageBreakInterval">1</property>
                    <list-property name="filter">
                        <structure>
                            <property name="operator">eq</property>
                            <expression name="expr" type="javascript">row["activeMemberID"]</expression>
                            <simple-property-list name="value1">
                                <value type="javascript">row._outer["childmemberID"]</value>
                            </simple-property-list>
                            <property name="updateAggregation">true</property>
                        </structure>
                    </list-property>
                    <header>
                        <grid id="521">
                            <property name="width">7.802083333333333in</property>
                            <column id="522">
                                <property name="width">4.84375in</property>
                            </column>
                            <column id="523">
                                <property name="width">2.9583333333333335in</property>
                            </column>
                            <row id="524">
                                <cell id="525">
                                    <image id="531">
                                        <property name="marginBottom">20px</property>
                                        <property name="height">150px</property>
                                        <property name="width">450px</property>
                                        <property name="source">url</property>
                                        <property name="fitToContainer">true</property>
                                        <property name="proportionalScale">true</property>
                                        <expression name="uri" type="javascript">params["invoiceheaderimgurl"].value + row["subtreeinvoiceprofileimage"];</expression>
                                    </image>
                                </cell>
                                <cell id="526">
                                    <grid id="532">
                                        <property name="borderBottomStyle">solid</property>
                                        <property name="borderBottomWidth">thin</property>
                                        <property name="borderLeftStyle">solid</property>
                                        <property name="borderLeftWidth">thin</property>
                                        <property name="borderRightStyle">solid</property>
                                        <property name="borderRightWidth">thin</property>
                                        <property name="borderTopStyle">solid</property>
                                        <property name="borderTopWidth">thin</property>
                                        <property name="height">1.0520833333333333in</property>
                                        <column id="533"/>
                                        <row id="534">
                                            <property name="overflow">hidden</property>
                                            <property name="height">0.3125in</property>
                                            <cell id="535">
                                                <data id="825">
                                                    <property name="fontSize">14pt</property>
                                                    <property name="fontWeight">bold</property>
                                                    <property name="textAlign">center</property>
                                                    <property name="overflow">auto</property>
                                                    <list-property name="boundDataColumns">
                                                        <structure>
                                                            <property name="name">renewalstatementtitle</property>
                                                            <expression name="expression" type="javascript">params["renewalstatementtitle"]</expression>
                                                            <property name="dataType">string</property>
                                                        </structure>
                                                    </list-property>
                                                    <property name="resultSetColumn">renewalstatementtitle</property>
                                                </data>
                                            </cell>
                                        </row>
                                        <row id="536">
                                            <property name="height">0.7291666666666666in</property>
                                            <cell id="537">
                                                <property name="borderTopStyle">solid</property>
                                                <property name="borderTopWidth">thin</property>
                                                <grid id="539">
                                                    <property name="width">2.8229166666666665in</property>
                                                    <column id="540">
                                                        <property name="width">1.125in</property>
                                                    </column>
                                                    <column id="541">
                                                        <property name="width">1.6979166666666667in</property>
                                                    </column>
                                                    <row id="1091">
                                                        <cell id="1092">
                                                            <label id="1094">
                                                                <property name="fontSize">9pt</property>
                                                                <property name="fontWeight">bold</property>
                                                                <property name="textAlign">right</property>
                                                                <text-property name="text">Account Number</text-property>
                                                            </label>
                                                        </cell>
                                                        <cell id="1093">
                                                            <text-data id="1096">
                                                                <property name="fontSize">9pt</property>
                                                                <expression name="valueExpr">row._outer["childmembernumber"]</expression>
                                                                <property name="contentType">html</property>
                                                            </text-data>
                                                        </cell>
                                                    </row>
                                                    <row id="542">
                                                        <cell id="543">
                                                            <text id="548">
                                                                <property name="fontSize">9pt</property>
                                                                <property name="fontWeight">bold</property>
                                                                <property name="textAlign">right</property>
                                                                <property name="contentType">auto</property>
                                                                <text-property name="content"><![CDATA[Renewal Dates]]></text-property>
                                                            </text>
                                                        </cell>
                                                        <cell id="544">
                                                            <text-data id="554">
                                                                <property name="fontSize">9pt</property>
                                                                <expression name="valueExpr">row["subdaterange"]</expression>
                                                                <property name="contentType">html</property>
                                                            </text-data>
                                                        </cell>
                                                    </row>
                                                    <row id="545">
                                                        <cell id="546">
                                                            <text id="549">
                                                                <property name="fontSize">9pt</property>
                                                                <property name="fontWeight">bold</property>
                                                                <property name="paddingTop">2pt</property>
                                                                <property name="textAlign">right</property>
                                                                <property name="contentType">auto</property>
                                                                <text-property name="content"><![CDATA[Amount Due]]></text-property>
                                                            </text>
                                                        </cell>
                                                        <cell id="547">
                                                            <extended-item extensionName="Crosstab" extensionVersion="3.7.0" id="1047">
                                                                <property name="cube">subTreesCube</property>
                                                                <property name="measureDirection">horizontal</property>
                                                                <property name="header">
                                                                    <extended-item extensionName="CrosstabCell" id="1048">
                                                                        <property name="content">
                                                                            <data id="1051">
                                                                                <property name="fontSize">9pt</property>
                                                                                <property name="borderBottomStyle">none</property>
                                                                                <property name="borderLeftStyle">none</property>
                                                                                <property name="borderRightStyle">none</property>
                                                                                <property name="borderTopStyle">none</property>
                                                                                <property name="paddingTop">1pt</property>
                                                                                <property name="paddingLeft">1pt</property>
                                                                                <structure name="numberFormat">
                                                                                    <property name="category">Currency</property>
                                                                                    <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                                                </structure>
                                                                                <property name="resultSetColumn">subamountduetotal</property>
                                                                            </data>
                                                                        </property>
                                                                    </extended-item>
                                                                </property>
                                                                <property name="hideMeasureHeader">true</property>
                                                                <property name="filter">
                                                                    <filter-condition-element>
                                                                        <expression name="expr" type="javascript">dimension["subtree"]["rootsubscriberid"]</expression>
                                                                        <property name="operator">eq</property>
                                                                        <simple-property-list name="value1">
                                                                            <value type="javascript">row._outer["rootsubscriberid"]</value>
                                                                        </simple-property-list>
                                                                    </filter-condition-element>
                                                                </property>
                                                                <property name="borderTopStyle">none</property>
                                                                <property name="borderLeftStyle">none</property>
                                                                <property name="borderBottomStyle">none</property>
                                                                <property name="borderRightStyle">none</property>
                                                                <list-property name="boundDataColumns">
                                                                    <structure>
                                                                        <property name="name">subamountduetotal</property>
                                                                        <text-property name="displayName">subamountduetotal</text-property>
                                                                        <expression name="expression" type="javascript">measure["subamountdue_total"]</expression>
                                                                        <property name="dataType">float</property>
                                                                    </structure>
                                                                </list-property>
                                                            </extended-item>
                                                        </cell>
                                                    </row>
                                                </grid>
                                            </cell>
                                        </row>
                                    </grid>
                                </cell>
                            </row>
                            <row id="527">
                                <cell id="528">
                                    <text-data id="530">
                                        <property name="fontWeight">bold</property>
                                        <property name="marginLeft">20px</property>
                                        <property name="marginBottom">40px</property>
                                        <property name="paddingLeft">1in</property>
                                        <property name="overflow">visible</property>
                                        <expression name="valueExpr">row._outer["namestring"]&#13;
+ "&lt;br/>" &#13;
+ (row._outer["company"] != undefined &amp;&amp; BirtStr.charLength(BirtStr.trim(row._outer["company"])) != 0 ? BirtStr.trim(row._outer["company"]) + "&lt;br/>"  : "")&#13;
+ BirtStr.trim(row._outer["address1"]) +"&lt;br/>" &#13;
+ (BirtStr.charLength(BirtStr.trim(row._outer["address2"])) > 0 ? BirtStr.trim(row._outer["address2"]) + "&lt;br/>"  : "") &#13;
+ (BirtStr.charLength(BirtStr.trim(row._outer["address3"])) > 0 ? BirtStr.trim(row._outer["address3"]) + "&lt;br/>"  : "") &#13;
+ (BirtStr.charLength(BirtStr.trim(row._outer["city"])) > 0 ? BirtStr.trim(row._outer["city"]) + ", " : "") + BirtStr.trim(row._outer["state"]) + " " + BirtStr.trim(row._outer["postalcode"])</expression>
                                        <property name="contentType">html</property>
                                    </text-data>
                                </cell>
                                <cell id="529">
                                    <property name="textAlign">right</property>
                                    <property name="verticalAlign">bottom</property>
                                    <grid id="1119">
                                        <property name="verticalAlign">middle</property>
                                        <property name="canShrink">false</property>
                                        <property name="width">36%</property>
                                        <list-property name="visibility">
                                            <structure>
                                                <property name="format">all</property>
                                                <expression name="valueExpr" type="javascript">(params["frmrenewonlineoptions"].value == false || (BirtStr.charLength(params["renewalsuburl"]) == 0 || BirtStr.charLength(row._outer["renewalcode"]) == 0 || BirtStr.charLength(row._outer["renewalsubqrcodeimgurl"]) == 0))</expression>
                                            </structure>
                                        </list-property>
                                        <column id="1120">
                                            <property name="textAlign">center</property>
                                            <property name="whiteSpace">nowrap</property>
                                        </column>
                                        <row id="1121">
                                            <property name="textAlign">center</property>
                                            <cell id="1122">
                                                <text id="1108">
                                                    <property name="paddingRight">1pt</property>
                                                    <property name="textAlign">center</property>
                                                    <property name="contentType">auto</property>
                                                    <text-property name="content"><![CDATA[Scan to Access]]></text-property>
                                                </text>
                                            </cell>
                                        </row>
                                        <row id="1123">
                                            <cell id="1124">
                                                <property name="fontSize">10pt</property>
                                                <image id="1101">
                                                    <property name="marginBottom">5px</property>
                                                    <property name="height">100px</property>
                                                    <property name="width">100px</property>
                                                    <property name="source">url</property>
                                                    <property name="fitToContainer">true</property>
                                                    <property name="proportionalScale">true</property>
                                                    <expression name="uri" type="javascript">row._outer["renewalsubqrcodeimgurl"]</expression>
                                                </image>
                                            </cell>
                                        </row>
                                    </grid>
                                </cell>
                            </row>
                        </grid>
                    </header>
                    <detail>
                        <data id="1056">
                            <property name="marginTop">0pt</property>
                            <property name="marginBottom">10px</property>
                            <property name="paddingLeft">10px</property>
                            <property name="paddingRight">10px</property>
                            <property name="textAlign">left</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">( params["frmsubscribertopcontent"].length == 0 )</expression>
                                </structure>
                            </list-property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">frmsubscribertopcontent</property>
                                    <expression name="expression" type="javascript">params["frmsubscribertopcontent"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                            </list-property>
                            <property name="resultSetColumn">frmsubscribertopcontent</property>
                        </data>
                        <grid id="1059">
                            <property name="borderBottomStyle">solid</property>
                            <property name="borderBottomWidth">1px</property>
                            <property name="borderLeftStyle">solid</property>
                            <property name="borderLeftWidth">1px</property>
                            <property name="borderRightStyle">solid</property>
                            <property name="borderRightWidth">1px</property>
                            <property name="borderTopStyle">solid</property>
                            <property name="borderTopWidth">1px</property>
                            <property name="marginLeft">0pt</property>
                            <property name="marginBottom">10px</property>
                            <property name="marginRight">0pt</property>
                            <property name="textAlign">center</property>
                            <property name="canShrink">false</property>
                            <property name="width">100%</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">(params["frmrenewonlineoptions"].value == false || (BirtStr.charLength(params["renewalsuburl"]) == 0 || BirtStr.charLength(row._outer["renewalcode"]) == 0))</expression>
                                </structure>
                            </list-property>
                            <column id="1061">
                                <property name="textAlign">left</property>
                                <property name="width">70%</property>
                            </column>
                            <column id="1062">
                                <property name="textAlign">right</property>
                            </column>
                            <row id="1063">
                                <cell id="1065">
                                    <property name="textAlign">left</property>
                                    <property name="verticalAlign">middle</property>
                                    <text-data id="1073">
                                        <property name="textAlign">left</property>
                                        <property name="display">inline</property>
                                        <expression name="valueExpr" type="javascript">(BirtStr.charLength(params["renewalsuburl"]) > 0 ? "&lt;a href='" + params["renewalsuburl"].value + (BirtStr.charLength(row._outer["renewalcode"]) > 0 ? "/" + row._outer["renewalcode"] : "") + "' target='_blank'>" + params["renewalsuburl"].value + (BirtStr.charLength(row._outer["renewalcode"]) > 0 ? "/" + row._outer["renewalcode"] : "") + "&lt;/a>" : "")</expression>
                                        <property name="contentType">html</property>
                                    </text-data>
                                </cell>
                                <cell id="1066">
                                    <property name="textAlign">right</property>
                                    <property name="verticalAlign">middle</property>
                                    <text-data id="1070">
                                        <property name="textAlign">right</property>
                                        <property name="display">inline</property>
                                        <expression name="valueExpr" type="javascript">(row._outer["renewalcode"] != null &amp;&amp; BirtStr.charLength(row._outer["renewalcode"]) > 0 ? "Subscription Code: " + row._outer["renewalcode"] : "")</expression>
                                        <property name="contentType">html</property>
                                    </text-data>
                                </cell>
                            </row>
                        </grid>
                        <extended-item extensionName="Crosstab" extensionVersion="3.7.0" id="478">
                            <property name="cube">Subscribers DataCube</property>
                            <property name="measures">
                                <extended-item extensionName="MeasureView" id="486">
                                    <property name="measure">TotalPaid</property>
                                    <property name="detail">
                                        <extended-item extensionName="AggregationCell" id="487">
                                            <property name="aggregationOnRow">SubInfo/invoicecontentfootnotenumber</property>
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">none</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">none</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="width">1in</property>
                                            <property name="content">
                                                <data id="488">
                                                    <structure name="numberFormat">
                                                        <property name="category">Currency</property>
                                                        <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                    </structure>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <property name="height">15px</property>
                                                    <property name="resultSetColumn">TotalBilled_SubInfo/invoicecontentfootnotenumber</property>
                                                </data>
                                            </property>
                                        </extended-item>
                                    </property>
                                    <property name="aggregations">
                                        <extended-item extensionName="AggregationCell" id="489">
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">solid</property>
                                            <property name="borderTopWidth">thin</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">none</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="paddingTop">4pt</property>
                                            <property name="content">
                                                <data id="497">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="fontStyle">normal</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="paddingTop">10px</property>
                                                    <structure name="numberFormat">
                                                        <property name="category">Currency</property>
                                                        <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                    </structure>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <property name="resultSetColumn">TotalBilled</property>
                                                </data>
                                            </property>
                                        </extended-item>
                                    </property>
                                    <property name="header">
                                        <extended-item extensionName="CrosstabCell" id="491">
                                            <property name="content">
                                                <label id="492">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="borderLeftStyle">none</property>
                                                    <property name="borderRightStyle">none</property>
                                                    <property name="borderTopStyle">none</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="marginBottom">0px</property>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <text-property name="text">Billed</text-property>
                                                </label>
                                            </property>
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">none</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">solid</property>
                                            <property name="borderBottomWidth">thin</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="width">1in</property>
                                        </extended-item>
                                    </property>
                                </extended-item>
                                <extended-item extensionName="MeasureView" name="detailTotalTax" id="1025">
                                    <property name="measure">TotalTax</property>
                                    <property name="detail">
                                        <extended-item extensionName="AggregationCell" id="1026">
                                            <property name="aggregationOnRow">SubInfo/invoicecontentfootnotenumber</property>
                                            <property name="verticalAlign">bottom</property>
                                            <property name="width">1in</property>
                                            <property name="content">
                                                <data id="1036">
                                                    <structure name="numberFormat">
                                                        <property name="category">Currency</property>
                                                        <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                    </structure>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <property name="height">15px</property>
                                                    <property name="resultSetColumn">TotalTax_SubInfo/invoicecontentfootnotenumber</property>
                                                </data>
                                            </property>
                                        </extended-item>
                                    </property>
                                    <property name="aggregations">
                                        <extended-item extensionName="AggregationCell" id="1027">
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">solid</property>
                                            <property name="borderTopWidth">thin</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">none</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="paddingTop">4pt</property>
                                            <property name="content">
                                                <data id="1035">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="fontStyle">normal</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="paddingTop">10px</property>
                                                    <structure name="numberFormat">
                                                        <property name="category">Currency</property>
                                                        <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                    </structure>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <property name="resultSetColumn">TotalTax</property>
                                                </data>
                                            </property>
                                        </extended-item>
                                    </property>
                                    <property name="header">
                                        <extended-item extensionName="CrosstabCell" id="1030">
                                            <property name="content">
                                                <label id="1037">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="borderLeftStyle">none</property>
                                                    <property name="borderRightStyle">none</property>
                                                    <property name="borderTopStyle">none</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="marginBottom">0px</property>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <text-property name="text">Tax</text-property>
                                                </label>
                                            </property>
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">none</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">solid</property>
                                            <property name="borderBottomWidth">thin</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="width">1in</property>
                                        </extended-item>
                                    </property>
                                </extended-item>
                                <extended-item extensionName="MeasureView" id="493">
                                    <property name="measure">TotalBilled</property>
                                    <property name="detail">
                                        <extended-item extensionName="AggregationCell" id="494">
                                            <property name="aggregationOnRow">SubInfo/invoicecontentfootnotenumber</property>
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">none</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">none</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="width">1in</property>
                                            <property name="content">
                                                <data id="495">
                                                    <structure name="numberFormat">
                                                        <property name="category">Currency</property>
                                                        <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                    </structure>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <property name="resultSetColumn">TotalPaid_SubInfo/invoicecontentfootnotenumber</property>
                                                </data>
                                            </property>
                                        </extended-item>
                                    </property>
                                    <property name="aggregations">
                                        <extended-item extensionName="AggregationCell" id="496">
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">solid</property>
                                            <property name="borderTopWidth">thin</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">none</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="paddingTop">4pt</property>
                                            <property name="content">
                                                <data id="490">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="fontStyle">normal</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="paddingTop">10px</property>
                                                    <structure name="numberFormat">
                                                        <property name="category">Currency</property>
                                                        <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                    </structure>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <property name="resultSetColumn">TotalPaid</property>
                                                </data>
                                            </property>
                                        </extended-item>
                                    </property>
                                    <property name="header">
                                        <extended-item extensionName="CrosstabCell" id="498">
                                            <property name="content">
                                                <label id="499">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="marginBottom">0px</property>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <text-property name="text">Paid</text-property>
                                                </label>
                                            </property>
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">none</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">solid</property>
                                            <property name="borderBottomWidth">thin</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="width">1in</property>
                                        </extended-item>
                                    </property>
                                </extended-item>
                                <extended-item extensionName="MeasureView" id="500">
                                    <property name="measure">TotalDue</property>
                                    <property name="detail">
                                        <extended-item extensionName="AggregationCell" id="501">
                                            <property name="aggregationOnRow">SubInfo/invoicecontentfootnotenumber</property>
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">none</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">none</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="width">1in</property>
                                            <property name="content">
                                                <data id="502">
                                                    <structure name="numberFormat">
                                                        <property name="category">Currency</property>
                                                        <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                    </structure>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <property name="resultSetColumn">TotalDue_SubInfo/invoicecontentfootnotenumber</property>
                                                </data>
                                            </property>
                                        </extended-item>
                                    </property>
                                    <property name="aggregations">
                                        <extended-item extensionName="AggregationCell" id="503">
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">solid</property>
                                            <property name="borderTopWidth">thin</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">none</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="paddingTop">4pt</property>
                                            <property name="content">
                                                <data id="504">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="fontStyle">normal</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="paddingTop">10px</property>
                                                    <structure name="numberFormat">
                                                        <property name="category">Currency</property>
                                                        <property name="pattern">$#,##0.00{RoundingMode=HALF_UP}</property>
                                                    </structure>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <property name="resultSetColumn">TotalDue</property>
                                                </data>
                                            </property>
                                        </extended-item>
                                    </property>
                                    <property name="header">
                                        <extended-item extensionName="CrosstabCell" id="505">
                                            <property name="content">
                                                <label id="506">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="borderLeftStyle">none</property>
                                                    <property name="borderRightStyle">none</property>
                                                    <property name="borderTopStyle">none</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="marginBottom">0px</property>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <text-property name="text">Due</text-property>
                                                </label>
                                            </property>
                                            <property name="verticalAlign">bottom</property>
                                            <property name="borderTopStyle">none</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">solid</property>
                                            <property name="borderBottomWidth">thin</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="width">1in</property>
                                        </extended-item>
                                    </property>
                                </extended-item>
                            </property>
                            <property name="rows">
                                <extended-item extensionName="CrosstabView" id="507">
                                    <property name="grandTotal">
                                        <extended-item extensionName="CrosstabCell" id="508">
                                            <property name="content">
                                                <label id="509">
                                                    <property name="fontWeight">bold</property>
                                                    <property name="marginTop">0px</property>
                                                    <property name="marginBottom">0px</property>
                                                    <property name="paddingTop">10px</property>
                                                    <property name="textAlign">right</property>
                                                    <property name="verticalAlign">bottom</property>
                                                    <text-property name="text">Total:</text-property>
                                                </label>
                                            </property>
                                            <property name="borderTopStyle">solid</property>
                                            <property name="borderTopWidth">thin</property>
                                            <property name="borderLeftStyle">none</property>
                                            <property name="borderBottomStyle">none</property>
                                            <property name="borderRightStyle">none</property>
                                            <property name="paddingTop">4pt</property>
                                        </extended-item>
                                    </property>
                                    <property name="views">
                                        <extended-item extensionName="DimensionView" id="510">
                                            <property name="dimension">SubInfo</property>
                                            <property name="levels">
                                                <extended-item extensionName="LevelView" name="NewLevel View2" id="867">
                                                    <property name="level">SubInfo/subscriberpath</property>
                                                    <property name="member">
                                                        <extended-item extensionName="CrosstabCell" id="868">
                                                            <property name="content">
                                                                <data name="subscriberpath1" id="870">
                                                                    <property name="resultSetColumn">subscriberpath</property>
                                                                </data>
                                                            </property>
                                                            <property name="width">0in</property>
                                                        </extended-item>
                                                    </property>
                                                </extended-item>
                                                <extended-item extensionName="LevelView" name="NewLevel View6" id="511">
                                                    <property name="level">SubInfo/Item Description</property>
                                                    <property name="sort">
                                                        <sort-element>
                                                            <expression name="key" type="javascript">dimension["SubInfo"]["subscriberpath"]</expression>
                                                            <property name="direction">asc</property>
                                                            <property name="strength">-1</property>
                                                        </sort-element>
                                                    </property>
                                                    <property name="member">
                                                        <extended-item extensionName="CrosstabCell" id="512">
                                                            <property name="content">
                                                                <data name="Item Description1" id="513">
                                                                    <property name="resultSetColumn">Item Description</property>
                                                                </data>
                                                            </property>
                                                            <property name="borderTopStyle">none</property>
                                                            <property name="borderLeftStyle">none</property>
                                                            <property name="borderBottomStyle">none</property>
                                                            <property name="borderRightStyle">none</property>
                                                            <property name="paddingBottom">3pt</property>
                                                        </extended-item>
                                                    </property>
                                                </extended-item>
                                                <extended-item extensionName="LevelView" name="NewLevel View5" id="586">
                                                    <property name="level">SubInfo/invoicecontentfootnotenumber</property>
                                                    <property name="member">
                                                        <extended-item extensionName="CrosstabCell" id="587">
                                                            <property name="content">
                                                                <data name="invoicecontentfootnotenumber" id="589">
                                                                    <list-property name="visibility">
                                                                        <structure>
                                                                            <property name="format">all</property>
                                                                            <expression name="valueExpr" type="javascript">true</expression>
                                                                        </structure>
                                                                    </list-property>
                                                                    <property name="resultSetColumn">invoicecontentfootnotenumber</property>
                                                                </data>
                                                            </property>
                                                            <property name="width">0.4791666666666667in</property>
                                                        </extended-item>
                                                    </property>
                                                </extended-item>
                                            </property>
                                        </extended-item>
                                    </property>
                                </extended-item>
                            </property>
                            <property name="header">
                                <extended-item extensionName="CrosstabCell" id="869">
                                    <property name="content">
                                        <label id="871">
                                            <text-property name="text">subscriberpath</text-property>
                                        </label>
                                    </property>
                                </extended-item>
                                <extended-item extensionName="CrosstabCell" id="514">
                                    <property name="content">
                                        <label id="515">
                                            <property name="fontWeight">bold</property>
                                            <property name="marginTop">0px</property>
                                            <property name="marginBottom">0px</property>
                                            <property name="paddingLeft">7px</property>
                                            <text-property name="text">Details</text-property>
                                        </label>
                                    </property>
                                    <property name="borderTopStyle">none</property>
                                    <property name="borderLeftStyle">none</property>
                                    <property name="borderBottomStyle">solid</property>
                                    <property name="borderBottomWidth">thin</property>
                                    <property name="borderRightStyle">none</property>
                                </extended-item>
                                <extended-item extensionName="CrosstabCell" id="588">
                                    <property name="content">
                                        <label id="590">
                                            <text-property name="text"></text-property>
                                        </label>
                                    </property>
                                    <property name="borderBottomStyle">solid</property>
                                    <property name="borderBottomWidth">thin</property>
                                </extended-item>
                            </property>
                            <method name="onPrepare"><![CDATA[
/**
 * Called when crosstab is being prepared.
 *
 * @param crosstab
 *            ICrosstab
 * @param reportContext
 *            IReportContext
 */

function onPrepareCrosstab( crosstab, reportContext ) {
	if (reportContext.getParameterValue("showTax") != true) {
		reportContext.getDesignHandle().findElement("detailTotalTax").drop();
	}
} 
]]></method>
                            <property name="filter">
                                <filter-condition-element>
                                    <expression name="expr" type="javascript">dimension["SubInfo"]["rootsubscriberid"]</expression>
                                    <property name="operator">eq</property>
                                    <simple-property-list name="value1">
                                        <value type="javascript">row._outer["rootsubscriberid"]</value>
                                    </simple-property-list>
                                </filter-condition-element>
                            </property>
                            <property name="borderTopStyle">none</property>
                            <property name="borderTopWidth">0pt</property>
                            <property name="borderTopColor">white</property>
                            <property name="borderLeftStyle">none</property>
                            <property name="borderLeftWidth">0pt</property>
                            <property name="borderLeftColor">white</property>
                            <property name="borderBottomStyle">none</property>
                            <property name="borderBottomWidth">0pt</property>
                            <property name="borderBottomColor">white</property>
                            <property name="borderRightStyle">none</property>
                            <property name="borderRightWidth">0pt</property>
                            <property name="borderRightColor">white</property>
                            <property name="marginBottom">30px</property>
                            <structure name="toc"/>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">Item Description</property>
                                    <expression name="expression" type="javascript">dimension["SubInfo"]["Item Description"] + (BirtStr.charLength(dimension["SubInfo"]["invoicecontentfootnotenumber"]) > 0 ? "  (" + dimension["SubInfo"]["invoicecontentfootnotenumber"] + ")" : "")</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalBilled_SubInfo/Item Description</property>
                                    <expression name="expression">measure["TotalBilled"]</expression>
                                    <property name="dataType">float</property>
                                    <simple-property-list name="aggregateOn">
                                        <value>SubInfo/Item Description</value>
                                    </simple-property-list>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalPaid_SubInfo/Item Description</property>
                                    <expression name="expression">measure["TotalPaid"]</expression>
                                    <property name="dataType">float</property>
                                    <simple-property-list name="aggregateOn">
                                        <value>SubInfo/Item Description</value>
                                    </simple-property-list>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalDue_SubInfo/Item Description</property>
                                    <expression name="expression">measure["TotalDue"]</expression>
                                    <property name="dataType">float</property>
                                    <simple-property-list name="aggregateOn">
                                        <value>SubInfo/Item Description</value>
                                    </simple-property-list>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalPaid</property>
                                    <expression name="expression">measure["TotalPaid"]</expression>
                                    <property name="dataType">float</property>
                                    <property name="aggregateFunction">sum</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalBilled</property>
                                    <expression name="expression">measure["TotalBilled"]</expression>
                                    <property name="dataType">float</property>
                                    <property name="aggregateFunction">sum</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalDue</property>
                                    <expression name="expression">measure["TotalDue"]</expression>
                                    <property name="dataType">float</property>
                                    <property name="aggregateFunction">sum</property>
                                </structure>
                                <structure>
                                    <property name="name">subCount</property>
                                    <expression name="expression">measure["subCount"]</expression>
                                    <property name="dataType">integer</property>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">subCount_SubInfo/Item Description</property>
                                    <expression name="expression">measure["subCount"]</expression>
                                    <property name="dataType">integer</property>
                                    <simple-property-list name="aggregateOn">
                                        <value>SubInfo/Item Description</value>
                                    </simple-property-list>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">invoicecontentfootnotenumber</property>
                                    <expression name="expression">dimension["SubInfo"]["invoicecontentfootnotenumber"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalPaid_SubInfo/invoicecontentfootnotenumber</property>
                                    <expression name="expression">measure["TotalPaid"]</expression>
                                    <property name="dataType">float</property>
                                    <simple-property-list name="aggregateOn">
                                        <value>SubInfo/invoicecontentfootnotenumber</value>
                                    </simple-property-list>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalBilled_SubInfo/invoicecontentfootnotenumber</property>
                                    <expression name="expression">measure["TotalBilled"]</expression>
                                    <property name="dataType">float</property>
                                    <simple-property-list name="aggregateOn">
                                        <value>SubInfo/invoicecontentfootnotenumber</value>
                                    </simple-property-list>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalDue_SubInfo/invoicecontentfootnotenumber</property>
                                    <expression name="expression">measure["TotalDue"]</expression>
                                    <property name="dataType">float</property>
                                    <simple-property-list name="aggregateOn">
                                        <value>SubInfo/invoicecontentfootnotenumber</value>
                                    </simple-property-list>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">subscriberpath</property>
                                    <expression name="expression">dimension["SubInfo"]["subscriberpath"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalTax</property>
                                    <expression name="expression">measure["TotalTax"]</expression>
                                    <property name="dataType">float</property>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                                <structure>
                                    <property name="name">TotalTax_SubInfo/invoicecontentfootnotenumber</property>
                                    <expression name="expression">measure["TotalTax"]</expression>
                                    <property name="dataType">float</property>
                                    <simple-property-list name="aggregateOn">
                                        <value>SubInfo/invoicecontentfootnotenumber</value>
                                    </simple-property-list>
                                    <property name="aggregateFunction">SUM</property>
                                </structure>
                            </list-property>
                        </extended-item>
                        <data id="1057">
                            <property name="marginBottom">10px</property>
                            <property name="paddingLeft">10px</property>
                            <property name="paddingRight">10px</property>
                            <property name="textAlign">left</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">( params["frmsubscriberbottomcontent"].length == 0 )</expression>
                                </structure>
                            </list-property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">frmsubscriberbottomcontent</property>
                                    <expression name="expression" type="javascript">params["frmsubscriberbottomcontent"]</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                            </list-property>
                            <structure name="toc"/>
                            <property name="resultSetColumn">frmsubscriberbottomcontent</property>
                        </data>
                    </detail>
                    <footer>
                        <list name="paymentList" id="829">
                            <property name="marginBottom">20px</property>
                            <property name="marginRight">0pt</property>
                            <property name="dataSet">payments</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">(row["Aggregation"] == 0)</expression>
                                </structure>
                            </list-property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">rootsubscriberID</property>
                                    <text-property name="displayName">rootsubscriberID</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["rootsubscriberID"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">transactionID</property>
                                    <text-property name="displayName">transactionID</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["transactionID"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">datebilled</property>
                                    <text-property name="displayName">datebilled</text-property>
                                    <expression name="expression" type="javascript">BirtDateTime.month( dataSetRow["datebilled"])  +"/" +  BirtDateTime.day( dataSetRow["datebilled"] ) +"/" +  BirtDateTime.year( dataSetRow["datebilled"] ) + &#13;
"   " + dataSetRow["detail"]</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                                <structure>
                                    <property name="name">detail</property>
                                    <text-property name="displayName">detail</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["detail"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">allocatedAmount</property>
                                    <text-property name="displayName">allocatedAmount</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["allocatedAmount"]</expression>
                                    <property name="dataType">float</property>
                                </structure>
                                <structure>
                                    <property name="name">allocatedAmountTotal</property>
                                    <text-property name="displayName">allocatedAmountTotal</text-property>
                                    <property name="dataType">float</property>
                                    <property name="aggregateFunction">SUM</property>
                                    <list-property name="arguments">
                                        <structure>
                                            <property name="name">Expression</property>
                                            <expression name="value" type="javascript">dataSetRow["allocatedAmount"]</expression>
                                        </structure>
                                    </list-property>
                                    <property name="allowExport">true</property>
                                </structure>
                                <structure>
                                    <property name="name">Aggregation</property>
                                    <property name="dataType">integer</property>
                                    <property name="aggregateFunction">COUNT</property>
                                    <list-property name="arguments">
                                        <structure>
                                            <property name="name">Expression</property>
                                            <expression name="value" type="javascript">dataSetRow["transactionID"]</expression>
                                        </structure>
                                    </list-property>
                                    <property name="allowExport">true</property>
                                </structure>
                            </list-property>
                            <list-property name="filter">
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["rootsubscriberID"]</expression>
                                    <simple-property-list name="value1">
                                        <value type="javascript">row._outer["rootsubscriberid"]</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                            </list-property>
                            <header>
                                <text id="841">
                                    <property name="fontWeight">bold</property>
                                    <property name="borderBottomStyle">solid</property>
                                    <property name="borderBottomWidth">thin</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Applied Payments]]></text-property>
                                </text>
                            </header>
                            <detail>
                                <grid id="830">
                                    <property name="marginTop">3px</property>
                                    <property name="marginBottom">1px</property>
                                    <property name="width">7.65625in</property>
                                    <column id="831">
                                        <property name="width">6.229166666666667in</property>
                                    </column>
                                    <column id="833">
                                        <property name="textAlign">right</property>
                                        <property name="width">0.84375in</property>
                                    </column>
                                    <row id="834">
                                        <cell id="835">
                                            <data id="838">
                                                <property name="resultSetColumn">datebilled</property>
                                            </data>
                                        </cell>
                                        <cell id="837">
                                            <data id="840">
                                                <property name="fontWeight">normal</property>
                                                <property name="marginRight">-15px</property>
                                                <structure name="numberFormat">
                                                    <property name="category">Currency</property>
                                                    <property name="pattern">$#,##0.00;$(#,##0.00){RoundingMode=HALF_UP}</property>
                                                </structure>
                                                <property name="textAlign">right</property>
                                                <property name="resultSetColumn">allocatedAmount</property>
                                            </data>
                                        </cell>
                                    </row>
                                </grid>
                            </detail>
                            <footer>
                                <text-data id="842">
                                    <property name="fontWeight">bold</property>
                                    <property name="borderTopStyle">solid</property>
                                    <property name="borderTopWidth">thin</property>
                                    <property name="marginTop">0pt</property>
                                    <property name="marginRight">0px</property>
                                    <property name="paddingTop">10px</property>
                                    <property name="paddingRight">18px</property>
                                    <property name="textAlign">right</property>
                                    <property name="height">0.5in</property>
                                    <expression name="valueExpr">function CommaFormatted(amount){  &#13;
	var delimiter = ","; // replace comma if desired  &#13;
	var a = amount.split('.',2)  &#13;
	var d = a[1];  &#13;
	var i = parseInt(a[0]);  &#13;
	if(isNaN(i)) { return ''; }  &#13;
	var minus = '';  &#13;
	if(i &lt; 0) { minus = '-'; }  &#13;
	i = Math.abs(i);  &#13;
	var n = new String(i);  &#13;
	var a = [];  &#13;
	while(n.length > 3)  &#13;
	{  &#13;
	var nn = n.substr(n.length-3);  &#13;
	a.unshift(nn);  &#13;
	n = n.substr(0,n.length-3);  &#13;
	}  &#13;
	if(n.length > 0) { a.unshift(n); }  &#13;
	n = a.join(delimiter);  &#13;
	if(d.length &lt; 1) { amount = n; }  &#13;
	else { amount = n + '.' + d; }  &#13;
	amount = minus + amount;  &#13;
	return amount;  &#13;
}  &#13;
&#13;
var subamountdue = (row["allocatedAmountTotal"] != null ? row["allocatedAmountTotal"]  : 0);&#13;
&#13;
var amount=subamountdue.toFixed(2);  &#13;
"$" + CommaFormatted(amount);</expression>
                                    <property name="contentType">html</property>
                                </text-data>
                            </footer>
                        </list>
                        <list name="remainingInvoiceList" id="844">
                            <property name="dataSet">invoices</property>
                            <list-property name="visibility">
                                <structure>
                                    <property name="format">all</property>
                                    <expression name="valueExpr" type="javascript">(row["Aggregation"] == 0)</expression>
                                </structure>
                            </list-property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">rootsubscriberID</property>
                                    <text-property name="displayName">rootsubscriberID</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["rootsubscriberID"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">invoiceid</property>
                                    <text-property name="displayName">invoiceid</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["invoiceid"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">invoicecode</property>
                                    <text-property name="displayName">invoicecode</text-property>
                                    <expression name="expression" type="javascript">BirtDateTime.month( dataSetRow["datedue"] )  +"/" +  BirtDateTime.day( dataSetRow["datedue"] ) +"/" +  BirtDateTime.year( dataSetRow["datedue"] ) + &#13;
"   " + dataSetRow["invoicecode"] + (BirtStr.charLength( dataSetRow["payprofiledesc"] ) > 0 ? "  (Scheduled Payment - " + dataSetRow["payprofiledesc"] + ")" : "")</expression>
                                    <property name="dataType">string</property>
                                    <property name="allowExport">true</property>
                                </structure>
                                <structure>
                                    <property name="name">datebilled</property>
                                    <text-property name="displayName">datebilled</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["datebilled"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">datedue</property>
                                    <text-property name="displayName">datedue</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["datedue"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">statusid</property>
                                    <text-property name="displayName">statusid</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["statusid"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">status</property>
                                    <text-property name="displayName">status</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["status"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">amount</property>
                                    <text-property name="displayName">amount</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["amount"]</expression>
                                    <property name="dataType">float</property>
                                </structure>
                                <structure>
                                    <property name="name">amountDue</property>
                                    <text-property name="displayName">amountDue</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["amountDue"]</expression>
                                    <property name="dataType">float</property>
                                </structure>
                                <structure>
                                    <property name="name">Aggregation</property>
                                    <property name="dataType">integer</property>
                                    <property name="aggregateFunction">COUNT</property>
                                    <list-property name="arguments">
                                        <structure>
                                            <property name="name">Expression</property>
                                            <expression name="value" type="javascript">dataSetRow["invoicecode"]</expression>
                                        </structure>
                                    </list-property>
                                    <property name="allowExport">true</property>
                                </structure>
                                <structure>
                                    <property name="name">amountDueTotal</property>
                                    <text-property name="displayName">amountDueTotal</text-property>
                                    <property name="dataType">float</property>
                                    <property name="aggregateFunction">SUM</property>
                                    <list-property name="arguments">
                                        <structure>
                                            <property name="name">Expression</property>
                                            <expression name="value" type="javascript">dataSetRow["amountDue"]</expression>
                                        </structure>
                                    </list-property>
                                    <property name="allowExport">true</property>
                                </structure>
                                <structure>
                                    <property name="name">invoicecode_1</property>
                                    <text-property name="displayName">invoicecode</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["invoicecode"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">amount_1</property>
                                    <text-property name="displayName">amount</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["amount"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">amountDue_1</property>
                                    <text-property name="displayName">amountDue</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["amountDue"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">payprofiledesc</property>
                                    <text-property name="displayName">payprofiledesc</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["payprofiledesc"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                            </list-property>
                            <list-property name="filter">
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["rootsubscriberID"]</expression>
                                    <simple-property-list name="value1">
                                        <value type="javascript">row._outer["rootsubscriberid"]</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                                <structure>
                                    <property name="operator">not-match</property>
                                    <expression name="expr" type="javascript">row["status"]</expression>
                                    <simple-property-list name="value1">
                                        <value type="javascript">"Paid"</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                            </list-property>
                            <header>
                                <text id="845">
                                    <property name="fontWeight">bold</property>
                                    <property name="borderBottomStyle">solid</property>
                                    <property name="borderBottomWidth">thin</property>
                                    <property name="contentType">auto</property>
                                    <text-property name="content"><![CDATA[Remaining Invoices]]></text-property>
                                </text>
                            </header>
                            <detail>
                                <grid id="846">
                                    <property name="marginTop">3px</property>
                                    <property name="marginBottom">1px</property>
                                    <property name="width">7.65625in</property>
                                    <column id="847">
                                        <property name="width">6.510416666666667in</property>
                                    </column>
                                    <column id="848">
                                        <property name="width">1.1458333333333333in</property>
                                    </column>
                                    <row id="849">
                                        <cell id="850">
                                            <data id="852">
                                                <property name="resultSetColumn">invoicecode</property>
                                            </data>
                                        </cell>
                                        <cell id="851">
                                            <data id="853">
                                                <property name="fontWeight">normal</property>
                                                <property name="marginRight">-15px</property>
                                                <structure name="numberFormat">
                                                    <property name="category">Currency</property>
                                                    <property name="pattern">$#,##0.00;$(#,##0.00){RoundingMode=HALF_UP}</property>
                                                </structure>
                                                <property name="textAlign">right</property>
                                                <property name="resultSetColumn">amountDue</property>
                                            </data>
                                        </cell>
                                    </row>
                                </grid>
                            </detail>
                            <footer>
                                <text-data id="855">
                                    <property name="fontWeight">bold</property>
                                    <property name="borderTopStyle">solid</property>
                                    <property name="borderTopWidth">thin</property>
                                    <property name="marginTop">0pt</property>
                                    <property name="marginRight">0px</property>
                                    <property name="paddingTop">10px</property>
                                    <property name="paddingRight">18px</property>
                                    <property name="textAlign">right</property>
                                    <property name="height">0.5in</property>
                                    <expression name="valueExpr">function CommaFormatted(amount){  &#13;
	var delimiter = ","; // replace comma if desired  &#13;
	var a = amount.split('.',2)  &#13;
	var d = a[1];  &#13;
	var i = parseInt(a[0]);  &#13;
	if(isNaN(i)) { return ''; }  &#13;
	var minus = '';  &#13;
	if(i &lt; 0) { minus = '-'; }  &#13;
	i = Math.abs(i);  &#13;
	var n = new String(i);  &#13;
	var a = [];  &#13;
	while(n.length > 3)  &#13;
	{  &#13;
	var nn = n.substr(n.length-3);  &#13;
	a.unshift(nn);  &#13;
	n = n.substr(0,n.length-3);  &#13;
	}  &#13;
	if(n.length > 0) { a.unshift(n); }  &#13;
	n = a.join(delimiter);  &#13;
	if(d.length &lt; 1) { amount = n; }  &#13;
	else { amount = n + '.' + d; }  &#13;
	amount = minus + amount;  &#13;
	return amount;  &#13;
}  &#13;
var subamountdue = (row["amountDueTotal"] != null ? row["amountDueTotal"]  : 0);&#13;
&#13;
var amount=subamountdue.toFixed(2);  &#13;
"$" + CommaFormatted(amount);</expression>
                                    <property name="contentType">html</property>
                                </text-data>
                            </footer>
                        </list>
                        <list name="footnoteList" id="594">
                            <property name="dataSet">footnote</property>
                            <list-property name="boundDataColumns">
                                <structure>
                                    <property name="name">invoicecontentfootnotenumber</property>
                                    <text-property name="displayName">invoicecontentfootnotenumber</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["invoicecontentfootnotenumber"]</expression>
                                    <property name="dataType">integer</property>
                                    <property name="allowExport">true</property>
                                </structure>
                                <structure>
                                    <property name="name">contentversionid</property>
                                    <text-property name="displayName">contentversionid</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["contentversionid"]</expression>
                                    <property name="dataType">integer</property>
                                </structure>
                                <structure>
                                    <property name="name">rawcontent</property>
                                    <text-property name="displayName">rawcontent</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["rawcontent"]</expression>
                                    <property name="dataType">string</property>
                                </structure>
                                <structure>
                                    <property name="name">rootsubscriberID</property>
                                    <text-property name="displayName">rootsubscriberID</text-property>
                                    <expression name="expression" type="javascript">dataSetRow["rootsubscriberID"]</expression>
                                    <property name="dataType">integer</property>
                                </structure>
                            </list-property>
                            <list-property name="filter">
                                <structure>
                                    <property name="operator">eq</property>
                                    <expression name="expr" type="javascript">row["rootsubscriberID"]</expression>
                                    <simple-property-list name="value1">
                                        <value type="javascript">row._outer["rootsubscriberid"]</value>
                                    </simple-property-list>
                                    <property name="updateAggregation">true</property>
                                </structure>
                            </list-property>
                            <detail>
                                <grid id="596">
                                    <property name="width">7.65625in</property>
                                    <column id="597">
                                        <property name="width">0.22916666666666666in</property>
                                    </column>
                                    <column id="598">
                                        <property name="width">7.427083333333333in</property>
                                    </column>
                                    <row id="599">
                                        <cell id="600">
                                            <data id="602">
                                                <property name="resultSetColumn">invoicecontentfootnotenumber</property>
                                            </data>
                                        </cell>
                                        <cell id="601">
                                            <data id="603">
                                                <property name="resultSetColumn">rawcontent</property>
                                            </data>
                                        </cell>
                                    </row>
                                </grid>
                            </detail>
                        </list>
                    </footer>
                </list>
            </detail>
        </list>
    </body>
    <list-property name="images">
        <structure>
            <property name="name">spacer.gif</property>
            <property name="type">image/gif</property>
            <property name="data">
                R0lGODlhAQABAJH/AP///wAAAP///wAAACH/C0FET0JFOklSMS4wAt7tACH5BAEAAAIALAAAAAABAAEA
                AAICVAEAOw==
</property>
        </structure>
    </list-property>
</report>
