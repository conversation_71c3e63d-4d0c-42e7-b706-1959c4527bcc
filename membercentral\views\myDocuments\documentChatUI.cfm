<cfset local.dataStruct= attributes.data>
<cfif structKeyExists(application.objCMS,"getPlatformCache<PERSON>usterKey")>
    <cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfelse>
    <cfset local.assetCachingKey = "">
</cfif><cfoutput>

    <!DOCTYPE html>
    <html lang="en">
        <head>
            <cftry>
                #application.objCMS.getFrontendErrorTrackingCode()#
                <cfcatch type="any"></cfcatch>
            </cftry>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>#local.dataStruct.qryAIExpert.first_name# #local.dataStruct.qryAIExpert.last_name# - TrialSmith Expert Chat</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.7/css/bootstrap.min.css" rel="stylesheet">
            <style>
                html, body {
                    margin:0;
                    padding:0;
                    width: 100%;
                    height: 100%;
                }

                body {
                    display: flex;
                    flex-direction: column;
                    min-height: 100vh;
                    overflow-y: hidden;
                }
            </style>
            <script src="/sitecomponents/COMMON/webcomponents/trialsmith-document-chat/trialsmith-chat-component.umd.js#local.assetCachingKey#"></script>
        </head>
        <body class="d-flex flex-column">
            <header class="bg-light p-3">
                <h1>
                    #local.dataStruct.qryAIExpert.first_name# #local.dataStruct.qryAIExpert.last_name# <small>TrialSmith Expert Chat</small>
                    <a type="button" class="btn btn-primary" href="/?pg=myDocuments&tab=PTSAI">Back to AI Research Projects</a>
                </h1>
            </header>

            <main class="d-flex flex-column flex-grow-1 p-3">
                <cfif structKeyExists(local.dataStruct,'qryAIExpert')>
                    <nav>
                        <div class="nav nav-tabs" id="nav-tab" role="tablist">
                            <button class="nav-link" id="nav-profile-tab" data-bs-toggle="tab" data-bs-target="##nav-profile" type="button" role="tab" aria-controls="nav-profile" aria-selected="false">Add Depositions</button>
                            <button class="nav-link active" id="nav-home-tab" data-bs-toggle="tab" data-bs-target="##chatinterface" type="button" role="tab" aria-controls="nav-home" aria-selected="true">Ask Questions</button>
                          <button class="nav-link" id="nav-contact-tab" data-bs-toggle="tab" data-bs-target="##nav-contact" type="button" role="tab" aria-controls="nav-contact" aria-selected="false">Review Notes</button>
                        </div>
                      </nav>
                      <div class="tab-content flex-grow-1 d-flex flex-column" id="nav-tabContent">
                        <div class="tab-pane flex-column flex-grow-1 fade show active" id="chatinterface" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                            <trialsmith-chat
                                api-base-url="/trialsmithdocumentchatapi"
                                project-id="#local.dataStruct.qryAIExpert.agentUserUID#"
                                webviewer-lib-path="/sitecomponents/COMMON/javascript/webviewer/11.5.0/"
                            >
                                <suggestion-item
                                    key="suggestion-1"
                                    prompt="This expert has been hired by the defense and my goal is to impeach him. Give me - a concise synopsis of this expert's areas of expertise and qualifications in bulleted list format - the top 5 areas discussed that are related to his expertise"
                                    label="Top Areas Discussed">
                                </suggestion-item>

                                <suggestion-item
                                    key="suggestion-2"
                                    prompt="I'm researching this expert and I would like information about each of the cases represented by the transcripts. Give me a list of the case citations dates of the cases."
                                    label="Case List">
                                </suggestion-item>
                            </trialsmith-chat>
                        </div>
                        <div class="tab-pane flex-column flex-grow-1 fade" id="nav-profile" role="tabpanel" aria-labelledby="nav-profile-tab" tabindex="0">
                            <h1>Coming Soon</h1>
                        </div>
                        <div class="tab-pane flex-column flex-grow-1 fade" id="nav-contact" role="tabpanel" aria-labelledby="nav-contact-tab" tabindex="0">
                            <h1>Coming Soon</h1>
                        </div>
                        <div class="tab-pane flex-column flex-grow-1 fade" id="nav-disabled" role="tabpanel" aria-labelledby="nav-disabled-tab" tabindex="0">
                            <h1>Coming Soon</h1>
                        </div>
                      </div>

                <cfelse>
                    <div class="tsAppHeading">AI Expert Not Found</div>
                    <br/>
                    <div class="tsAppBodyText">The AI Expert was not found. <a href="javascript:self.close();">Close this window</a> to return to your purchased AI Experts.</div>
                </cfif>
            </main>

            <footer class="bg-light p-3 mt-auto">
                <p>Footer</p>
            </footer>

            <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.7/js/bootstrap.bundle.min.js"></script>
        </body>
    </html>
</cfoutput>
