ALTER PROC dbo.mp_distributeAuthCIMChecks
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- reset authorizeMPCheckWeekNum to 0 for all initially
	UPDATE dbo.mp_profiles
	SET authorizeMPCheckWeekNum = 0
	WHERE gatewayID = 10;

	-- Step 1: Calculate the actual workload for each individual profileID.
	-- Step 2: Rank every profile from heaviest to lightest. profileID is a tie-breaker to ensure the order is 100% deterministic.
	-- Step 3: Assign a week number using a round-robin (modulo) on the rank.
	WITH MPProfileWorkloads AS (
		SELECT mp.profileID, COUNT(mpp.profileID) AS cofCount
		FROM dbo.mp_profiles AS mp
		LEFT OUTER JOIN dbo.ams_memberPaymentProfiles AS mpp ON mpp.profileID = mp.profileID 
			AND mpp.[status] = 'A'
		WHERE mp.gatewayID = 10
		AND mp.[status] = 'A' 
		GROUP BY mp.profileID
	), RankedProfiles AS (
		SELECT profileID, ROW_NUMBER() OVER (ORDER BY cofCount DESC, profileID ASC) AS rn
		FROM MPProfileWorkloads
	)
	UPDATE mp
	SET mp.authorizeMPCheckWeekNum = (rp.rn - 1) % 4
	FROM dbo.mp_profiles as mp
	INNER JOIN RankedProfiles as rp on rp.profileID = mp.profileID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
