ALTER PROC dbo.queue_webhooks_prioritize

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = GETDATE();
	EXEC dbo.queue_getQueueTypeID @queueType='webhook', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#webhookPriority') IS NOT NULL
		DROP TABLE #webhookPriority;
	CREATE TABLE #webhookPriority (itemID int, SQSQueueName varchar(80), totalQueued int, minutesInQueue int, [priority] int);

	INSERT INTO #webhookPriority (itemID, SQSQueueName, minutesInQueue)
	SELECT itemID, SQSQueueName, minsInQueue = DATEDIFF(minute,dateUpdated,@nowDate)
	FROM dbo.queue_webhook
	WHERE statusID IN (@statusReady,@statusGrabbed);

	UPDATE hp 
	SET hp.totalQueued = tmp.totalQueued
	FROM #webhookPriority AS hp
	INNER JOIN (
		SELECT SQSQueueName, COUNT(*) AS totalQueued
		FROM #webhookPriority
		GROUP BY SQSQueueName
	) AS tmp ON tmp.SQSQueueName = hp.SQSQueueName;

	UPDATE #webhookPriority 
	SET [priority] = CASE 
			WHEN totalQueued = 1 THEN -100
			WHEN minutesInQueue > 360 THEN (totalQueued / 50) + 1
			WHEN minutesInQueue > 90 THEN (totalQueued / 25) - 10
			WHEN minutesInQueue > 30 THEN (totalQueued / 25) + 2
			WHEN totalQueued < 500 THEN (totalQueued / 25)
			ELSE (totalQueued / 25) + 10
		END;

	 -- delete rows where the priority hasn't changed
	DELETE tmp
	FROM #webhookPriority AS tmp
	INNER JOIN dbo.queue_webhook AS qid ON tmp.itemID = qid.itemID
	WHERE tmp.[priority] = qid.queuePriority;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- update queuePriority
	UPDATE qid
	SET qid.queuePriority = tmp.[priority]
	FROM dbo.queue_webhook AS qid
	INNER JOIN #webhookPriority AS tmp ON tmp.itemID = qid.itemID;

	IF OBJECT_ID('tempdb..#webhookPriority') IS NOT NULL
		DROP TABLE #webhookPriority;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
