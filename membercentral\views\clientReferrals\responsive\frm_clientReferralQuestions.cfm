<cfinclude template="frm_clientReferralQuestions_header.cfm">

<cfoutput>
<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

<a href="##divMCClientReferralsContainer" accesskey="2" tabindex="0" title="Skip to Main Content"></a>
<div id="divMCClientReferralsContainer">
	
	<cfif len(trim(local.strData.feFormInstructionsContent))>
		<div class="row-fluid control-group mainInstructions" id="mainInstructions">				
			#local.strData.feFormInstructionsContent#							
		</div>
	</cfif>
	<cfif structKeyExists(local.strData,"saleError") AND  val(local.strData.saleError)>
		<div class="row-fluid" id="saleError">
			<div class="alert alert-danger" role="alert"><span>Oops! Something went wrong. Please try after sometime.</span><button type="button" class="close hide" data-dismiss="alert" aria-label="Close" >&times;</button></div>
		</div>
	</cfif>

	<cfform name="frmClient" id="frmClient" method="POST" class="form-horizontal" >
		<cfinput type="hidden" name="repParentID" id="repParentID" value="#local.strData.repParentID#">
		<cfinput type="hidden" name="paymentRequired" id="paymentRequired" value="#local.strData.paymentRequired#">
		<cfinput type="hidden" name="questionAnswerPath" id="questionAnswerPath" value="">
		<cfif local.strData.strRefQuestionAnswers.hasvalidanswers>
			<cfinput type="hidden" name="panelID1" id="panelID1" value="#local.strData.panelid1#">
			<cfinput type="hidden" name="subPanelID1" id="subPanelID1" value="#local.strData.subPanelID1#">
		</cfif>
		
		<div id="step1form" class="step1Form step1Form">
			<cfif local.strData.showContactFormFirst>
				<div id="contactInfoLocation"></div>
			</cfif>

			<cfif local.strData.strRefQuestionAnswers.hasvalidanswers>
				<cfinclude template="frm_clientReferralQuestions_questionAnswers.cfm">
			<cfelse>
				<cfinclude template="frm_clientReferralQuestions_subPanels.cfm">
			</cfif>

			<div class="row-fluid hide legalIssueDesc step1Form" id="legalIssue">
				<div class="questionLRISWrapper">
					<fieldset>
						<legend id="toHide"><cfif len(trim(local.strData.referralSettingsQry.feLegalIssueDescTitle))>#trim(local.strData.referralSettingsQry.feLegalIssueDescTitle)#<cfelse>Tell us more about your legal issue  </cfif></legend>
						<legend id="toShow" class="hide">Description of Legal issue <i id="editForm_legalIssue" class="icon icon-pencil hide editForm_legalIssue" data-id="legalIssue" role="button" aria-label="Edit Legal Issue Description"></i></legend>
						<div class="innerLRISContentWrapper">
							<div id="legalIssueView" class="hide"></div>
							<div id="legalIssueForm">
								<div>
									<div id="legalIssueErr" role="alert"><span></span><button type="button" class="close hide" data-dismiss="alert" aria-label="Close" >&times;</button></div>
									<cfif local.strData.dspLegalDescription><p class="info">A description on your issue is required to process.</p></cfif>
									<cfif len(trim(local.strData.feLegalDescInstructContent))>
										<div class="row-fluid control-group">				
											<div class="">
											#local.strData.feLegalDescInstructContent#
											</div>				
										</div>
									</cfif>
									<div class="row-fluid control-group" data-label="Description of Legal Issue">
										<label for="issueDesc" class="control-label "></label>
										<div class="span12">
											<textarea name="issueDesc" id="issueDesc" style="width:90%;" rows="5" aria-label="Add description">#ReReplace(local.strData.issueDesc, "<[^<|>]+?>", "","ALL")#</textarea>
											<cfif val(local.strData.referralSettingsQry.feLegalDescLimitWords)>
												<div style="width:90%;">
													<div style="font-size:small;text-align:right;">Word Count:<span id="legalDescWordcount">0</span></div>
													<div id="legalDescWordLimitExceedAlert" class="alert alert-danger" style="display:none">#htmlEditFormat(local.strData.referralSettingsQry.feLegalDescLimitExceedMsg)#</div>
												</div>
											</cfif>
										</div>
									</div>
								</div>

								<div class="form-group clearfix">
									<div class="span12 text-right">
										<button class="btn btn-primary btnContinueLegalIssue" type="button" role="button" >Continue </button>
										<cfif NOT local.strData.dspLegalDescription>
											<button class="btn btn-default btnLegalIssueFilterSkip" type="button" role="button" >Skip</button>
										</cfif>
										<cfif local.strData.strRefQuestionAnswers.hasvalidanswers>
											<button class="btn btn-default btnPrevQuest" type="button" role="button" >Go to Previous Question</button>
											<button class="btn btn-default btnGoToTop" type="button" role="button" >Go to Top</button>
										</cfif>
									</div>
								</div>
							</div>
						</div>
					</fieldset>
				</div>
			</div>
		
			<cfset variables.multiSelectAdditionalFiltersInit = "">
			<cfif val(local.strData.additionalFiltersFieldSet)  OR local.strData.qryGetClassifications.recordCount GT 0>
				<div class="row-fluid hide additionalFilters step1Form" id="additionalFilters">
					<div class="questionLRISWrapper">
						<fieldset>
							<legend><cfif len(trim(local.strData.referralSettingsQry.feAdditionalFiltersTitle))>#trim(local.strData.referralSettingsQry.feAdditionalFiltersTitle)#<cfelse>Additional Filters and Criteria</cfif> <i id="editForm_additionalFilters" class="icon icon-pencil hide editForm_additionalFilters" data-id="additionalFilters" tabindex="0" role="button" aria-label="Edit <cfif len(trim(local.strData.referralSettingsQry.feAdditionalFiltersTitle))>#trim(local.strData.referralSettingsQry.feAdditionalFiltersTitle)#<cfelse>Additional Filters and Criteria</cfif>"></i></legend>
							<div class="innerLRISContentWrapper">
								<div id="additionalFiltersErr" role="alert"><span></span><button type="button" class="close hide" data-dismiss="alert" aria-label="Close">&times;</button></div>
								<div id="additionalFiltersView" class="hide criteriaDiv"></div>

								<div id="additionalFiltersForm">
									<div class="row-fluid control-group">				
										<div>
											<p  class="info">These questions will help us narrow down the list of attorneys that can assist you.</p>
										</div>				
									</div>

									<cfif ArrayLen(local.strData.xmlFields.xmlRoot.xmlChildren) GT 0 || local.strData.qryGetClassifications.recordCount GT 0>
										<div class="">	
											<cfif ArrayLen(local.strData.xmlFields.xmlRoot.xmlChildren)>
												<div class="row-fluid control-group">
													<div class="controls">
														* indicates a required field
													</div>
												</div>		
												<cfloop array="#local.strData.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
												
													<cfset local.thisFieldValue = evaluate("local.strData.#local.thisfield.xmlattributes.fieldCode#") />
													<div class="row-fluid control-group" data-label="#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#" data-proximity="<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode) or findNoCase('_proximity',local.thisfield.xmlattributes.dbField)>true</cfif>">
														<label for="#local.thisfield.xmlattributes.fieldCode#" class="control-label ">#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#:<cfif local.thisfield.xmlattributes.isRequired is 1>*</cfif></label>
														<div class="controls">
															<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
																<cfcase value="TEXTBOX">
																	<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode) or findNoCase('_proximity',local.thisfield.xmlattributes.dbField)>
																		<cfset local.thisRadiusValue = evaluate("local.strData.#local.thisfield.xmlattributes.fieldCode#_radius") />	
																		Within 
																		<cfselect name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" style="width:auto!important">
																			<cfloop list="5,10,25,50,100" index="local.thisrad">
																				<cfoutput><option value="#local.thisrad#" <cfif listFindNoCase(local.thisRadiusValue,local.thisrad)>selected="selected"</cfif>>#local.thisrad#</option></cfoutput>
																			</cfloop>
																		</cfselect>
																		miles of #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#
																		<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisFieldValue#" autocomplete="off">
																	<cfelse>
																		<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisFieldValue#" autocomplete="off">
																	</cfif>
																</cfcase>
																<cfcase value="RADIO">
																	<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
																		<cfswitch expression="#local.thisfield.xmlattributes.dataTypeCode#">
																		<cfcase value="STRING">
																			<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
																		</cfcase>
																		<cfcase value="DECIMAL2">
																			<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
																		</cfcase>
																		<cfcase value="INTEGER">
																			<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
																		</cfcase>
																		<cfcase value="DATE">
																			<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
																		</cfcase>
																		<cfcase value="BIT">
																			<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
																		</cfcase>
																		<cfdefaultcase>
																			<cfset local.thisOptColValue = "">
																		</cfdefaultcase>
																		</cfswitch>
																		<label class="radio">
																			<cfinput type="radio" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisOptColValue#"><cfoutput><cfif local.thisfield.xmlattributes.dataTypeCode eq "BIT">#YesNoFormat(local.thisOptColValue)#<cfelse>#local.thisOptColValue#</cfif></cfoutput><br/>
																		</label>
																	</cfloop>
																</cfcase>
																<cfcase value="SELECT,CHECKBOX">
																	<cfif local.thisfield.xmlattributes.allowMultiple>
																		<cfsavecontent variable="variables.multiSelectAdditionalFiltersInit">
																			<cfoutput>
																				#variables.multiSelectAdditionalFiltersInit#
																			</cfoutput>
																		</cfsavecontent>
																		<cfsavecontent variable="variables.jQueryMultiselect">
																			<cfoutput>
																			<script type="text/javascript">
																				$(function(){
																					$("###local.thisfield.xmlattributes.fieldCode#").multiselect({
																						header: "Choose options below",
																						selectedList: 10,
																						minWidth: 200
																					});
																				});
																			</script>	
																			</cfoutput>
																		</cfsavecontent>
																		<cfhtmlhead text="#application.objCommon.minText(variables.jQueryMultiselect)#">
																	</cfif>
																	<cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
																		<cfset local.qryStates = application.objMember.getStates(local.strData.orgID)>
																		<cfselect name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" >
																			<option value=""></option>
																			<cfoutput query="local.qryStates" group="countryID" >
																				<optgroup label="#local.qryStates.country#">
																				<cfoutput>
																					<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
																				</cfoutput>
																				</optgroup>
																			</cfoutput>
																		</cfselect>
																	<cfelseif listFindNoCase("m_recordtypeid,m_membertypeid,m_status", local.thisfield.xmlattributes.fieldCode)>
																		<cfselect name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" >
																			<option value=""></option>
																			<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
																				<cfoutput><option value="#local.thisOpt.xmlAttributes.valueID#" <cfif listFindNoCase(local.thisFieldValue,local.thisOpt.xmlAttributes.valueID)>selected="selected"</cfif>>#local.thisOpt.xmlAttributes.columnValueString#</option></cfoutput>
																			</cfloop>
																		</cfselect>
																	<cfelse>
																		<cfselect name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" multiple="#local.thisfield.xmlattributes.allowMultiple#" >
																			<cfif not local.thisfield.xmlattributes.allowMultiple><option value=""></option></cfif>
																			<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
																				<cfswitch expression="#local.thisfield.xmlattributes.dataTypeCode#">
																				<cfcase value="STRING">
																					<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
																					<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
																				</cfcase>
																				<cfcase value="DECIMAL2">
																					<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
																					<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
																				</cfcase>
																				<cfcase value="INTEGER">
																					<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
																					<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
																				</cfcase>
																				<cfcase value="DATE">
																					<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
																					<cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
																				</cfcase>
																				<cfcase value="BIT">
																					<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
																					<cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
																				</cfcase>
																				<cfdefaultcase>
																					<cfset local.thisOptColValue = "">
																					<cfset local.thisOptColDisplay = "">
																				</cfdefaultcase>
																				</cfswitch>
																				<cfoutput><option value="#local.thisOptColValue#" <cfif listFindNoCase(local.thisFieldValue,local.thisOptColValue)>selected="selected"</cfif>>#local.thisOptColDisplay#</option></cfoutput>
																			</cfloop>
																		</cfselect>
																	</cfif>
																</cfcase>
																<cfcase value="DATE">
																	<nobr>
																	<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="" >
																	<cfinput type="button" name="btnClear#local.thisfield.xmlattributes.fieldCode#" class="btn btn-default" id="btnClear#local.thisfield.xmlattributes.fieldCode#" value="clear">
																	</nobr>
																	<cfsavecontent variable="local.datejs">
																		<cfoutput>
																			<script language="javascript">
																				$(function() { 
																					mca_setupDatePickerField('#local.thisfield.xmlattributes.fieldCode#');
																					$("##btnClear#local.thisfield.xmlattributes.fieldCode#").click( function(e) { mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#');return false; } );
																				});
																			</script>
																			<style type="text/css">
																				###local.thisfield.xmlattributes.fieldCode# { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
																			</style>
																		</cfoutput>
																	</cfsavecontent>
																	<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
																</cfcase>
															</cfswitch>				
														</div>
													</div>
												</cfloop>
											</cfif>
											<cfif local.strData.qryGetClassifications.recordCount>
												<cfoutput>
													<cfloop query="local.strData.qryGetClassifications">
														<cfif val(local.strData.qryGetClassifications.allowSearch)>
															<div class="row-fluid control-group" data-label="<cfif len(trim(local.strData.qryGetClassifications.name))>#local.strData.qryGetClassifications.name#<cfelse>#local.strData.qryGetClassifications.groupSetName#</cfif>">
																<label for="mg_gid_#local.strData.qryGetClassifications.groupSetID#" class="control-label "><cfif len(trim(local.strData.qryGetClassifications.name))>#local.strData.qryGetClassifications.name#<cfelse>#local.strData.qryGetClassifications.groupSetName#</cfif>:</label>
																<div class="controls">
															
																	<cfset local.qryGetGroupSetGroup = local.strData.objAdminReferrals.getGroupSetGroup(local.strData.qryGetClassifications.groupSetID) />
																	<cfset local.thisGroupIDValue = evaluate("local.strData.mg_gid_#local.strData.qryGetClassifications.groupSetID#") />
																	<select name="mg_gid_#local.strData.qryGetClassifications.groupSetID#" id="mg_gid_#local.strData.qryGetClassifications.groupSetID#" multiple="multiple" >
																		<cfloop query="local.qryGetGroupSetGroup">
																			<option value="#local.qryGetGroupSetGroup.groupsetGroupID#" <cfif listFind(local.thisGroupIDValue,local.qryGetGroupSetGroup.groupsetGroupID)>selected="selected"</cfif>>#local.qryGetGroupSetGroup.labelOverride#</option>
																		</cfloop>
																	</select>
																</div>
															</div>			
													
															<cfsavecontent variable="variables.jQueryMultiselectGroup">
																<cfoutput>
																	<script type="text/javascript">
																		$(function(){
																			$("##mg_gid_#local.strData.qryGetClassifications.groupSetID#").multiselect({
																				header: "Choose options below",
																				selectedList: 10,
																				minWidth: 200
																			});
																		});
																	</script>	
																</cfoutput>
															</cfsavecontent>
															<cfhtmlhead text="#application.objCommon.minText(variables.jQueryMultiselectGroup)#">			
														</cfif>
													</cfloop>
												</cfoutput>
											</cfif>
										</div>
									</cfif>	
							
									<div class="form-group clearfix">
										<div class="span12 text-center">
											<button class="btn btn-primary btnContinueAddFilter" type="button" role="button">Continue</button> 
											<button class="btn btn-default btnAdditionalFilterSkip" type="button" role="button">Skip</button> 
										</div>
									</div>
								</div>
							</div>
						</fieldset>
					</div>
				</div>
			</cfif>

			<div class="row-fluid hide step2btnWrapper" id="step2btnWrapper" style="text-align:center;padding-top:20px;">
				<button class="btn btn-primary btnStep2" type="button" role="button">Next Step</button> 
			</div>
		</div>
		
		<script type="text/javascript">
			function additionalFiltersMultiSelectInit(){
				#variables.multiSelectAdditionalFiltersInit#
			}
		</script>
		  
		
		<div id="noReferralInfo" class="hide step1Form">
			
			<div class="row-fluid control-group stepInstructions">				
				No member found							
			</div>	
			<div class="row-fluid">
				<div class="questionLRISWrapper">
					<fieldset>
					<legend><cfif len(trim(local.strData.referralSettingsQry.feReferralInforMatchTitle))>#trim(local.strData.referralSettingsQry.feReferralInforMatchTitle)#<cfelse>Referral Information</cfif></legend>
					<div class="innerLRISContentWrapper" tabindex="0">   
						<p class="info" >#local.strData.feNoResultsInfoContent#</p>
						<br>
						<div class="control-group clearfix">
							
							<div class="span12 text-center">
								<button class="btn btn-default btnGoToStep1" type="button" role="button">Previous </button>
								<button class="btn btn-primary btnReviewSubmit" type="button" role="button">Submit for Review </button>
							</div>
						</div>
					</div>
					</fieldset>
				</div>
			</div>
		</div>

		<div id="contactInfo-secondary" class="hide step1Form">
			<div class="row-fluid">
				<div class="questionLRISWrapper">
					<fieldset>
						<legend>Contact Information <i id="editForm_contactInfo-secondary" class="icon icon-pencil hide editForm_contactInfo-secondary" data-id="contactInfo-secondary" tabindex="0" role="button" aria-label="Edit Contact Information"></i></legend>
						<div class="innerLRISContentWrapper">
							<div id="secondaryContactErr" role="alert"><span></span><button type="button" class="close hide" data-dismiss="alert" tabindex="0" aria-label="Close">&times;</button></div>
							<div id="contactInfo-secondaryView" class="hide"></div>
							<div id="contactInfo-secondaryForm">
								<div class="row-fluid control-group">
									<div class="controls">
										* indicates a required field
									</div>
								</div>
								<div class="row-fluid control-group" data-label="First Name">
									<label for="firstNameSecondary" class="control-label ">First Name:*</label>
									<div class="controls">
										<input name="firstNameSecondary"  id="firstNameSecondary" type="text"  maxlength="75" value="" />
									</div>
								</div>

								<div class="row-fluid control-group"  data-label="Last Name">
									<label for="lastNameSecondary" class="control-label ">Last Name:*</label>
									<div class="controls">
										<input name="lastNameSecondary"  id="lastNameSecondary" type="text" maxlength="75" value="#local.strData.lastName#" />
									</div>
								</div>

								<div class="row-fluid control-group"  data-label="E-mail">
									<label for="emailSecondary" class="control-label ">E-mail:*</label>
									<div class="controls">
										<input name="emailSecondary"  id="emailSecondary" type="text" maxlength="255" value=""  />
									</div>
								</div>

								<div class="row-fluid control-group"  data-label="Verify E-mail">
									<label for="verifyEmailSecondary" class="control-label ">Verify E-mail:*</label>
									<div class="controls">
										<input name="verifyEmailSecondary"  id="verifyEmailSecondary" type="text" maxlength="255" value=""  />
									</div>
								</div>

								<div class="row-fluid control-group"  data-label="Telephone">
									<label for="homePhone" class="control-label ">Telephone:</label>
									<div class="controls">
										<input name="homePhoneSecondary"  id="homePhoneSecondary" type="text" maxlength="40" value=""  />
										<label class="checkbox"><input type="checkbox" name="isCellPhone" id="isCellPhone" value="1" role="checkbox"> This is a cell phone</label>
										<input type="hidden" name="homePhoneSecondaryE164" id="homePhoneSecondaryE164" value="">
										<input type="hidden" name="homePhoneSecondaryNational" id="homePhoneSecondaryNational" value="">
									</div>
								</div>
								
								<div class="row-fluid control-group" data-label="Best time to call">
									<label for="bestcalltime" class="control-label ">Best time to call:</label>
									<div class="controls">
										<select name="bestcalltime" id="bestcalltime">
											<option value="">Select Time</option>
											<option value="Morning">Morning</option>
											<option value="Afternoon">Afternoon</option>
											<option value="Night">Night</option>
										</select>
									</div>
								</div>
								<div class="form-group clearfix">
									<div class="span12 text-center">
										<button class="btn btn-default btnGoToStep1FromSecondaryContact" type="button" role="button">Previous </button>
										<button class="btn btn-primary btnSecondaryContactSubmit btnSubmit" type="button" role="button">Submit</button>
									</div>
								</div>
							</div>
						</div>
					</fieldset>
				</div>
			</div>
		</div>

		<div id="referralInfo" class="hide step1Form">
		</div>

		<div id="contactInfo-primary" class="<cfif NOT local.strData.showContactFormFirst>hide</cfif> step1Form">
			<cfif len(trim(local.strData.feFormInstructionsStep3Content))>
				<div class="row-fluid stepInstructions">				
					#local.strData.feFormInstructionsStep3Content#							
				</div>
			</br>
			</cfif>	
			<div class="row-fluid">
				<div class="questionLRISWrapper">
					<fieldset>
						<legend>Contact Information <i id="editForm_contactInfo-primary" class="icon icon-pencil hide editForm_contactInfo-primary" data-id="contactInfo-primary" role="button" tabindex="0" role="button" aria-label="Edit Contact Information"></i></legend>
						<div class="innerLRISContentWrapper">
							<div id="primaryContactErr" role="alert"><span></span><button type="button" class="close hide" data-dismiss="alert" aria-label="Close">&times;</button></div>
							<div id="contactInfo-primaryView" class="hide"></div>
							<div id="contactInfo-primaryForm">
								<cfif local.strData.isFrontEndDisplay['First Name']>
									<div class="row-fluid control-group" data-label="First Name">
										<label for="firstName" class="control-label ">First Name:<cfif local.strData.isRequired['First Name']>*</cfif></label>
										<div class="controls">
											<cfinput name="firstName"  id="firstName" type="text" class="input-large"  maxlength="75" value="#local.strData.firstName#" />
										</div>
									</div>
								</cfif>
								<cfif local.strData.isFrontEndDisplay['Middle Name']>
									<div class="row-fluid control-group" data-label="Middle Name">
										<label for="middleName" class="control-label ">Middle Name:<cfif local.strData.isRequired['Middle Name']>*</cfif></label>
										<div class="controls">
											<cfinput name="middleName"  id="middleName" type="text" class="input-large" maxlength="25" value="#local.strData.middleName#" />
										</div>
									</div>
								</cfif>
								<cfif local.strData.isFrontEndDisplay['Last Name']>
									<div class="row-fluid control-group"  data-label="Last Name">
										<label for="lastName" class="control-label ">Last Name:<cfif local.strData.isRequired['Last Name']>*</cfif></label>
										<div class="controls">
											<cfinput name="lastName"  id="lastName" type="text" class="input-large" maxlength="75" value="#local.strData.lastName#"  />
										</div>
									</div>
								</cfif>
								<cfif local.strData.isFrontEndDisplay['Business']>
									<div class="row-fluid control-group" data-label="Business">
										<label for="businessName" class="control-label ">Business:<cfif local.strData.isRequired['Business']>*</cfif></label>
										<div class="controls">
											<cfinput name="businessName"  id="businessName" type="text" class="input-large" maxlength="100" value="#local.strData.businessName#"  />
										</div>
									</div>
								</cfif>
								<cfif local.strData.isFrontEndDisplay['Address 1']>
									<div class="row-fluid control-group" data-label="Address line 1">
										<label for="address1" class="control-label ">Address 1:<cfif local.strData.isRequired['Address 1']>*</cfif></label>
										<div class="controls">
											<cfinput name="address1"  id="address1" type="text" class="input-large" maxlength="100" value="#local.strData.address1#"  />
										</div>
									</div>
								</cfif>
								<cfif local.strData.isFrontEndDisplay['Address 2']>
									<div class="row-fluid control-group" data-label="Address line 2">
										<label for="address2" class="control-label ">Address 2:<cfif local.strData.isRequired['Address 2']>*</cfif></label>
										<div class="controls">
											<cfinput name="address2"  id="address2" type="text" class="input-large" maxlength="100" value="#local.strData.address2#"  />
										</div>
									</div>
								</cfif>
								<cfif local.strData.isFrontEndDisplay['City']>
									<div class="row-fluid control-group" data-label="City">
										<label for="city" class="control-label ">City:<cfif local.strData.isRequired['City']>*</cfif></label>
										<div class="controls">
											<cfinput name="city"  id="city" type="text" class="input-large" maxlength="100" value="#local.strData.city#"  />
										</div>
									</div>
								</cfif>
							
								<cfif local.strData.isFrontEndDisplay['State']>
									<div class="row-fluid control-group" data-label="State">
										<label for="state" class="control-label ">State:<cfif local.strData.isRequired['State']>*</cfif></label>
										<div class="controls">					
											<select name="state" id="state" class=" input-large">
												<option value=""></option>
																		
												<cfoutput query="local.strData.qryStates" group="countryID">
													<optgroup label="#local.strData.qryStates.country#">
														<cfoutput>
															<option value="#local.strData.qryStates.stateid#" <cfif val(local.strData.qryStates.stateid) eq val(local.strData.state)>selected</cfif>>#local.strData.qryStates.stateName# &nbsp;</option>
														</cfoutput>
													</optgroup>
												</cfoutput>
												
											</select>
										</div>
									</div>
								</cfif>

								<cfif local.strData.isFrontEndDisplay['Zip Code']>
									<div class="row-fluid control-group" data-label="Zip Code">
										<label for="postalCode" class="control-label ">Zip Code:<cfif local.strData.isRequired['Zip Code']>*</cfif></label>
										<div class="controls">
											<cfinput name="postalCode"  id="postalCode" type="text" class="input-large" maxlength="25" value="#local.strData.postalCode#"  />
										</div>
									</div>
								</cfif>

								<cfif local.strData.isFrontEndDisplay['Email']>
									<div class="row-fluid control-group" data-label="Email">
										<label for="email" class="control-label ">E-mail:<cfif local.strData.isRequired['Email']>*</cfif></label>
										<div class="controls">
											<cfinput name="email"  id="email" type="text" class="input-large" maxlength="255" value="#local.strData.email#"  />
										</div>
									</div>
									<div class="row-fluid control-group" data-label="Verify Your Email">
										<label for="verifyEmail" class="control-label ">Verify Your Email:<cfif local.strData.isRequired['Email']>*</cfif></label>
										<div class="controls">
											<cfinput name="verifyEmail" id="verifyEmail" type="text" class="input-large" maxlength="255" value="" />
										</div>
									</div>
								</cfif>

								<cfif local.strData.isFrontEndDisplay['Home Phone ##']>
									<div class="row-fluid control-group" data-label="Home Phone ##">
										<label for="homePhone" class="control-label ">Home Phone ##:<cfif local.strData.isRequired['Home Phone ##']>*</cfif></label>
										<div class="controls">
											<input name="homePhone"  autocomplete="off" id="homePhone" type="text" class="input-large" maxlength="40" value="#local.strData.homePhone#"  />
											<input type="hidden" name="homePhoneE164" id="homePhoneE164" value="#local.strData.homePhoneE164#">
											<input type="hidden" name="homePhoneNational" id="homePhoneNational" value="#local.strData.homePhone#">
										</div>
									</div>
								</cfif>

								<cfif local.strData.isFrontEndDisplay['Cell Phone ##']>
									<div class="row-fluid control-group" data-label="Cell Phone ##">
										<label for="cellPhone" class="control-label ">Cell Phone ##:<cfif local.strData.isRequired['Cell Phone ##']>*</cfif></label>
										<div class="controls">
											<cfinput name="cellPhone"  id="cellPhone" type="text" class="input-large" maxlength="40" value="#local.strData.cellPhone#"  />
											<input type="hidden" name="cellPhoneE164" id="cellPhoneE164" value="#local.strData.cellPhoneE164#">
											<input type="hidden" name="cellPhoneNational" id="cellPhoneNational" value="#local.strData.cellPhone#">
										</div>
									</div>
								</cfif>

								<cfif local.strData.isFrontEndDisplay['Alternate Phone ##']>
									<div class="row-fluid control-group" data-label="Alternate Phone ##">
										<label for="alternatePhone" class="control-label ">Alternate Phone ##:<cfif local.strData.isRequired['Alternate Phone ##']>*</cfif></label>
										<div class="controls">
											<cfinput name="alternatePhone"  id="alternatePhone" type="text" class="input-large" maxlength="40"  value="#local.strData.alternatePhone#"  />
											<input type="hidden" name="alternatePhoneE164" id="alternatePhoneE164" value="#local.strData.alternatePhoneE164#">
											<input type="hidden" name="alternatePhoneNational" id="alternatePhoneNational" value="#local.strData.alternatePhone#">
										</div>
									</div>
								</cfif>								

								<cfif local.strData.qryGetLanguages.recordCount GT 1>
									<div class="row-fluid control-group" data-label="Preferred language">
										<label for="communicateLanguageID" class="control-label ">What is your preferred language?:</label>
										<div class="controls">
											<select name="communicateLanguageID" id="communicateLanguageID" >
												<option value="">Select Language</option>
												<cfloop query="local.strData.qryGetLanguages">
													<option value="#local.strData.qryGetLanguages.languageID#" <cfif (local.strData.qryGetLanguages.languageID eq local.strData.communicateLanguageID) OR (not val(local.strData.communicateLanguageID) and  val(local.strData.qryGetLanguages.isDefault))>selected</cfif>>#local.strData.qryGetLanguages.languageName#</option>
												</cfloop>
											</select>
										</div>
									</div>
								<cfelse>
									<input type="hidden" name="communicateLanguageID" id="communicateLanguageID" value="#local.strData.qryGetLanguages.languageID#">
								</cfif>

								<cfif local.strData.feDspSurveyOption>
									<div class="row-fluid control-group" data-label="Would you like to receive surveys regarding the case?">
										<label for="sendSurvey" class="control-label ">Would you like to receive surveys regarding the case?:</label>
										<div class="controls">
											<label for="sendSurvey"><input type="checkbox" name="sendSurvey" id="sendSurvey" value="1" class="input-large" role="checkbox" <cfif local.strData.sendSurvey OR local.strData.feSurveyOptionDefaultYes>checked</cfif>> Yes</label>
										</div>
									</div>
								<cfelse>
									<input type="hidden" name="sendSurvey" id="sendSurvey" value="0">
								</cfif>

								<cfif local.strData.feDspBlogOption>
									<div class="row-fluid control-group" data-label="Would you like to receive e-mails regarding Newsletters and/or Blog updates?">
										<label for="sendNewsBlog" class="control-label ">Would you like to receive e-mails regarding Newsletters and/or Blog updates?:</label>
										<div class="controls">
											<label for="sendNewsBlog"><input type="checkbox" name="sendNewsBlog" id="sendNewsBlog" value="1"  class="input-large" role="checkbox" <cfif local.strData.sendNewsBlog>checked</cfif>> Yes</label>
										</div>
									</div>
								<cfelse>
									<input type="hidden" name="sendNewsBlog" id="sendNewsBlog" value="0">
								</cfif>		

								<cfif attributes.data.qryActiveSMSTemplate.recordcount and attributes.data.qryActiveSMSTemplate.status eq 'A'>
									<div class="row-fluid control-group textRefWrap smsReferralClient" data-label="Text Me Referral tO">
										<label for="smsReferralClient" class="control-label ">Text Me My Referrals:</label>
										<div class="controls">
											<i>In addition to email, would you also like to receive your referral information via text? 
											Selecting phone numbers below indicates that you agree to receive your referral text messages from [[organizationName]]. You may reply STOP at any time to cancel.
											</i>
											<br/><br/>
											<select class="input-large" name="smsClientNumbers" id="smsClientNumbers" multiple="multiple" >
												
											</select>
										</div>
									</div>
								</cfif>

								<div class="form-group clearfix">
								<div class="<cfif NOT local.strData.showContactFormFirst> span12 text-center<cfelse>span10 offset5</cfif>">
										<cfif NOT local.strData.showContactFormFirst> 
											<button class="btn btn-default btnGoToStep1FromPrimaryContact" type="button" role="button">Previous </button>
										</cfif>
										<button class="btn btn-primary btnPrimaryContactSubmit" type="button" role="button">Submit</button>
									</div>
								</div>
							</div>
						</div>
					</fieldset>
				</div>
			</div>
		</div>

		<div id="isThisRep" class="hide step1Form">
			<div id="isThisRepSelect" class="row-fluid">
				<div class="questionLRISWrapper">
					<fieldset>
						<legend>Are you filling out this form on behalf of another party?<i id="editForm_isThisRepSelect" class="icon icon-pencil hide editForm_isThisRepSelect" data-id="isThisRepSelect" tabindex="0" role="button" aria-label="Edit are you filling out this form on behalf of another party"></i></legend>
						<div class="innerLRISContentWrapper">
							<div id="isThisRepSelectView" class="hide"></div>
							<div id="isThisRepSelectForm" class="form-group clearfix">
								<div class="control-group" id="repSelectLabel" data-label="">
									<label for="isRepY" class="radio inline">
										<input type="radio" name="isRep" id="isRepY" value="1" data-val="Yes" autofill="off" <cfif local.strData.isRep>checked</cfif>> Yes
									</label>
									<label for="isRepN" class="radio inline">
										<input type="radio" name="isRep" id="isRepN" value="0" data-val="No" autofill="off" <cfif NOT local.strData.isRep>checked</cfif>> No
									</label>
									<div class="" id="isRepContinue">
										<div class="span10 offset5">
											<button class="btn btn-primary btnIsRepSubmit btnSubmit" type="button" role="button">Submit</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</fieldset>
				</div>
			</div>

			<div class="row-fluid repForm hide" id="repForm">
				<div class="questionLRISWrapper">
					<fieldset>
					<legend>Representative Information <i id="editForm_repForm" class="icon icon-pencil hide editForm_repForm" data-id="repForm" tabindex="0" role="button" aria-label="Edit Representative Information"></i></legend>
					<div class="innerLRISContentWrapper">
						<div id="repContactErr" role="alert"><span></span><button type="button" class="close hide" data-dismiss="alert" aria-label="Close">&times;</button></div>
						<div id="repFormView" class="hide"></div>
						<div id="repFormForm">
							<div class="row-fluid control-group">
								<div class="controls">
									* indicates a required field
								</div>
							</div>
							<div class="row-fluid control-group" data-label="First Name">
								<label for="repFirstName" class="control-label ">First Name:*</label>
								<div class="controls">
									<input name="repFirstName"  id="repFirstName" type="text" maxlength="75" value="#local.strData.repFirstName#"/>
								</div>
							</div>
							<div class="row-fluid control-group" data-label="Last Name">
								<label for="repLastName" class="control-label ">Last Name:*</label>
								<div class="controls">
									<input name="repLastName"  id="repLastName" type="text" maxlength="75" value="#local.strData.repLastName#"/>
								</div>
							</div>
							<div class="row-fluid control-group" data-label="Relationship to Contact">
								<label for="relationToClient" class="control-label ">Relationship to Contact:*</label>
								<div class="controls">
									<input name="relationToClient"  id="relationToClient" type="text" maxlength="100" value="#local.strData.relationToClient#"/>
								</div>
							</div>

							<div class="row-fluid control-group" data-label="Address 1">
								<label for="repAddress1" class="control-label ">Address 1:</label>
								<div class="controls">
									<input name="repAddress1"  id="repAddress1" type="text" maxlength="100" value="#local.strData.repAddress1#"/>
								</div>
							</div>

							<div class="row-fluid control-group" data-label="Address 2">
								<label for="repAddress2" class="control-label ">Address 2:</label>
								<div class="controls">
									<input name="repAddress2"  id="repAddress2" type="text"  maxlength="100" value="#local.strData.repAddress2#"/>
								</div>
							</div>

							<div class="row-fluid control-group" data-label="City">
								<label for="repCity" class="control-label ">City:</label>
								<div class="controls">
									<input name="repCity"  id="repCity" type="text" maxlength="100" value="#local.strData.repCity#"/>
								</div>
							</div>

							<div class="row-fluid control-group" data-label="State">
								<label for="repState" class="control-label ">State:</label>
								<div class="controls">
									<select name="repState" id="repState">
										<option value=""></option>
										
										<cfoutput query="local.strData.qryStates" group="countryID">
											<optgroup label="#local.strData.qryStates.country#">
												<cfoutput>
													<option value="#local.strData.qryStates.stateid#" <cfif local.strData.qryStates.stateid eq local.strData.state>selected</cfif>>#local.strData.qryStates.stateName# &nbsp;</option>
												</cfoutput>
											</optgroup>
										</cfoutput>
										
									</select>
								</div>
							</div>

							<div class="row-fluid control-group" data-label="Zip Code">
								<label for="repPostalCode" class="control-label ">Zip Code:</label>
								<div class="controls">
									<input name="repPostalCode"  id="repPostalCode" type="text" maxlength="25" value="#local.strData.repPostalCode#"/>
								</div>
							</div>

							<div class="row-fluid control-group" data-label="E-mail">
								<label for="repEmail" class="control-label ">E-mail:</label>
								<div class="controls">
									<input name="repEmail"  id="repEmail" type="text" maxlength="255" value="#local.strData.repEmail#"/>
								</div>
							</div>

							<div class="row-fluid control-group" data-label="Verify E-mail">
								<label for="repVerifyEmail" class="control-label ">Verify E-mail:</label>
								<div class="controls">
									<input name="repVerifyEmail"  id="repVerifyEmail" type="text" maxlength="255" value=""/>
								</div>
							</div>

							<div class="row-fluid control-group" data-label="Home Phone ##">
								<label for="repHomePhone" class="control-label ">Home Phone ##:*</label>
								<div class="controls">
									<input name="repHomePhone"  id="repHomePhone" type="text" maxlength="40" value="#local.strData.repHomePhone#"/>
									<input type="hidden" name="repHomePhoneE164" id="repHomePhoneE164" value="#local.strData.repHomePhoneE164#">
									<input type="hidden" name="repHomePhoneNational" id="repHomePhoneNational" value="#local.strData.repHomePhone#">
								</div>
							</div>

							<div class="row-fluid control-group" data-label="Cell Phone ##">
								<label for="repCellPhone" class="control-label ">Cell Phone ##:</label>
								<div class="controls">
									<input name="repCellPhone"  id="repCellPhone" type="text" maxlength="40" value="#local.strData.repCellPhone#"/>
									<input type="hidden" name="repCellPhoneE164" id="repCellPhoneE164" value="#local.strData.repCellPhoneE164#">
									<input type="hidden" name="repCellPhoneNational" id="repCellPhoneNational" value="#local.strData.repCellPhone#">
								
								</div>
							</div>

							<div class="row-fluid control-group" data-label="Alternate Phone ##">
								<label for="repAlternatePhone" class="control-label ">Alternate Phone ##:</label>
								<div class="controls">
									<input name="repAlternatePhone"  id="repAlternatePhone" type="text" maxlength="40" value="#local.strData.repAlternatePhone#"/>
									<input type="hidden" name="repAlternatePhoneE164" id="repAlternatePhoneE164" value="#local.strData.repAlternatePhoneE164#">
									<input type="hidden" name="repAlternatePhoneNational" id="repAlternatePhoneNational" value="#local.strData.repAlternatePhone#">
								</div>
							</div>

							<cfif attributes.data.qryActiveSMSTemplate.recordcount and attributes.data.qryActiveSMSTemplate.status eq 'A'>
								<div class="row-fluid control-group textRefWrap smsReferralRep" data-label="Text Me Referral to">
									<label for="smsReferralRep" class="control-label ">Text Me My Referrals</label>
									<div class="controls">
										<i>In addition to email, would you also like to receive your referral information via text? 
										Selecting phone numbers below indicates that you agree to receive your referral text messages from [[organizationName]]. You may reply STOP at any time to cancel.
										</i><br/><br/>
										<select class="input-large" name="smsRepNumbers" id="smsRepNumbers" multiple="multiple" >
											
										</select>
									</div>
								</div>
							</cfif>

							<div class="control-group">
								<div class="span10 offset5">
									<button class="btn btn-primary btnRepSubmit btnSubmit" type="button" role="button">Submit</button>
								</div>
							</div>
						</div>
					</div>
					</fieldset>
				</div>

			</div>
		</div>

		<cfif local.strData.extraInformation.hasFields>
			<input type="hidden" name="hasClientFields" id="hasClientFields" value="1">
			<div id="extraInfo" class="hide step1Form">
				<div class="row-fluid">
					<div class="questionLRISWrapper">
						<legend><cfif len(trim(local.strData.referralSettingsQry.feAdditionalInfoTitle))>#trim(local.strData.referralSettingsQry.feAdditionalInfoTitle)#<cfelse>Additional Information</cfif>
						<i id="editForm_extraInfo" class="icon icon-pencil hide editForm_extraInfo" data-id="extraInfo"  tabindex="0" role="button" aria-label="Edit <cfif len(trim(local.strData.referralSettingsQry.feAdditionalInfoTitle))>#trim(local.strData.referralSettingsQry.feAdditionalInfoTitle)#<cfelse>Additional Information</cfif>"></i></legend>
						<div class="innerLRISContentWrapper">
							<div id="extraInfoErr" role="alert"><span></span><button type="button" class="close hide" data-dismiss="alert" aria-label="Close">&times;</button></div>
							<div id="extraInfoView" class="hide"></div>
							<div id="extraInfoForm" class="row-fluid">
								<div class="form-group clearfix">
									#local.strData.extraInformation.HTML#
									<div class="span10 offset5">
										<button class="btn btn-primary btnExtraInfoSubmit btnSubmit" type="button" role="button">Submit</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</cfif>

		<div id="paymentScreen" class="hide step1Form">
			<div class="row-fluid">
				<div class="questionLRISWrapper">
					<fieldset>
						<legend>Total referral fee to be paid: <b id="paymentAmt"></b></legend>
						<div class="form-group innerLRISContentWrapper">
							<cfif val(trim(local.strData.ccError))>
								<div id="CCInfoError" class="alert alert-danger" role="alert">
									There was a problem processing the payment for this referral fee. Please try again.
								</div>
							</cfif>
							<div id="ccForm">
								<div id="ccInputForm">#local.strData.profile_1.strPaymentForm.inputForm#</div>
								<br/>
								<div id="divBtnWrapper#local.strData.profile_1.profileID#">
									<button type="submit" name="clientSaleBtn" id="clientSaleBtn" class="clientSaleBtn btn btn-default" role="button">Accept and Pay Fee</button>
								</div>
							</div>
						</div>
					</fieldset>
				</div>
			</div>
			<cfif len(local.strData.profile_1.strPaymentForm.headCode)>
				<cfhtmlhead text="#application.objCommon.minText(local.strData.profile_1.strPaymentForm.headCode)#" />
			</cfif>
		</div>
	</cfform>

	<div id="formSubmitting" style="display:none;text-align:center;margin:50px;">
		<i class="icon-spin icon-spinner icon-3x"></i> <b>Please wait while we process your application.</b>
	</div>
</div>
</cfoutput>