ALTER PROC dbo.swod_copySeminar
@seminarID int,
@incBasics bit,
@incSpeakers bit,
@incSettings bit,
@incTitles bit,
@incCatalog bit,
@incSyndication bit,
@incCredit bit,
@recordedByMemberID int,
@newSeminarID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @participantID int, @orgCode varchar(10), @siteID int, @programCode varchar(15), 
		@swType varchar(4) = 'SWOD', @auditmsg varchar(max) = '', @minObjectiveID int, @newObjectiveID int,
		@SWAdminSiteResourceID int, @fieldUsageID int, @minFieldID int, @newFieldID int, @oldTitleID int, 
		@newTitleID int, @speakerAuthorTypeID int, @moderatorAuthorTypeID int, @nowDate datetime = GETDATE(),
		@resourceTypeID int, @siteResourceID int, @semWebCatalogSiteResourceID int;
	
	SELECT @resourceTypeID = memberCentral.dbo.fn_getResourceTypeID('SWSeminar');
	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID, @participantID = p.participantID, @orgCode=p.orgCode
	FROM dbo.tblSeminars AS s
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	WHERE s.seminarID = @seminarID;
	
	SELECT @semWebCatalogSiteResourceID = ai.siteResourceID
	FROM memberCentral.dbo.cms_applicationInstances AS ai 
	INNER JOIN memberCentral.dbo.cms_applicationTypes AS apt ON apt.applicationTypeID = ai.applicationTypeID 
		and apt.applicationTypeName = 'SemWebCatalog' 
	INNER JOIN memberCentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID
		AND sr.siteResourceID = ai.siteResourceID 
		AND sr.siteResourceStatusID = 1
	WHERE ai.siteID = @siteID;

	SELECT @SWAdminSiteResourceID = membercentral.dbo.fn_getSiteResourceIDForResourceType('SeminarWebAdmin',@siteID);
	SELECT @fieldUsageID = membercentral.dbo.fn_cf_getUsageID('SemWebCatalog',@swType+'Enrollment',NULL);
	EXEC memberCentral.dbo.getUniqueCode @uniqueCode=@programCode OUTPUT;

	SELECT @speakerAuthorTypeID = authorTypeID
	FROM dbo.tblAuthorTypes
	WHERE authorType = 'Speaker';

	SELECT @moderatorAuthorTypeID = authorTypeID
	FROM dbo.tblAuthorTypes
	WHERE authorType = 'Moderator';

	BEGIN TRAN;
		IF @incBasics = 1 BEGIN
			EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
				@isVisible=1, @parentSiteResourceID=@semWebCatalogSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;
			
			-- insert new seminar record
			INSERT INTO dbo.tblSeminars (seminarName, seminarSubTitle, programCode, seminarDesc, isPublished, participantID,
				--offerCertificate, 
				isDeleted, --isPriceBasedOnActual, revenueGLAccountID, 
				allowRegistrants,
				--dateCatalogStart, dateCatalogEnd, freeRateDisplay, 
				siteResourceID)
			SELECT 'Copy of ' + seminarName, seminarSubTitle, @programCode, seminarDesc, 0, participantID, --offerCertificate, 
				0,
				--isPriceBasedOnActual, revenueGLAccountID, 
				allowRegistrants, --dateCatalogStart, dateCatalogEnd, freeRateDisplay, 
				@siteResourceID
			FROM dbo.tblSeminars
			WHERE seminarID = @seminarID;
				SELECT @newSeminarID = SCOPE_IDENTITY();

			INSERT INTO dbo.tblSeminarsSWOD (seminarID, dateOrigPublished, --priceSyndication, allowSyndication,
				layoutID, seminarLength, --offerQA, introMessageText, endOfSeminarText, blankOnInactivity, 
				dateCreated)
			SELECT @newSeminarID, dateOrigPublished, --priceSyndication, allowSyndication, 
			layoutID, seminarLength,
				--offerQA, introMessageText, endOfSeminarText, blankOnInactivity, 
				@nowDate
			FROM dbo.tblSeminarsSWOD
			WHERE seminarID = @seminarID;

			-- learning objectives
			INSERT INTO dbo.tblLearningObjectives (seminarID, objective, objectiveOrder)
			SELECT @newSeminarID, objective, objectiveOrder
			FROM dbo.tblLearningObjectives
			WHERE seminarID = @seminarID
			ORDER BY objectiveOrder;
		END
		ELSE BEGIN
			EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
				@isVisible=1, @parentSiteResourceID=@semWebCatalogSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;
			
			INSERT INTO dbo.tblSeminars (seminarName, seminarSubTitle, programCode, seminarDesc, isPublished, participantID,
				--offerCertificate, isPriceBasedOnActual, dateCatalogStart, dateCatalogEnd, freeRateDisplay, 
				siteResourceID)
			SELECT 'Copy of ' + seminarName, seminarSubTitle, @programCode, '', 0, participantID, --0, 1, NULL, NULL, freeRateDisplay, 
			@siteResourceID
			FROM dbo.tblSeminars
			WHERE seminarID = @seminarID;
				SELECT @newSeminarID = SCOPE_IDENTITY();


			INSERT INTO dbo.tblSeminarsSWOD (seminarID, dateOrigPublished, --priceSyndication, allowSyndication, 
			layoutID, seminarLength, --offerQA, introMessageText, endofSeminartext, blankOnInactivity, 
				dateCreated)
			VALUES (@newSeminarID, @nowDate, --NULL, 0, 
			1, 60, --0, '', '', 0, 
			@nowDate);
			
		END

		SET @programCode = 'SWOD-' + CAST(@newSeminarID AS varchar(10));

		UPDATE dbo.tblSeminars
		SET programCode = @programCode
		WHERE seminarID = @newSeminarID;

		-- Update other columns depending on checkboxes field for the newly inserted record
		UPDATE newRecord
		SET offerCertificate = CASE WHEN @incSettings = 1 THEN original.offerCertificate ELSE 0 END,
		dateCatalogStart = CASE WHEN @incCatalog = 1 THEN original.dateCatalogStart ELSE NULL END,
		dateCatalogEnd = CASE WHEN @incCatalog = 1 THEN original.dateCatalogEnd ELSE NULL END,
		isPriceBasedOnActual = CASE WHEN @incCatalog = 1 THEN original.isPriceBasedOnActual ELSE 1 END,
		freeRateDisplay = original.freeRateDisplay,
		revenueGLAccountID = CASE WHEN @incCatalog = 1 THEN original.revenueGLAccountID ELSE NULL END
		FROM dbo.tblSeminars AS newRecord
		INNER JOIN dbo.tblSeminars AS original ON original.seminarID = @seminarID
		WHERE newRecord.seminarID = @newSeminarID;

		-- Update other columns depending on checkboxes field for the newly inserted record
		UPDATE newRecord
		SET offerQA = CASE WHEN @incSettings = 1 THEN original.offerQA ELSE 0 END,
		introMessageText = CASE WHEN @incSettings = 1 THEN original.introMessageText ELSE '' END,
		blankOnInactivity = CASE WHEN @incSettings = 1 THEN original.blankOnInactivity ELSE 0 END,
		endofSeminartext = CASE WHEN @incSettings = 1 THEN original.endofSeminartext ELSE '' END,
		allowSyndication = CASE WHEN @incSyndication = 1 THEN original.allowSyndication ELSE 0 END,
		priceSyndication = CASE WHEN @incSyndication = 1 THEN original.priceSyndication ELSE NULL END,
		includeConnectionInstructionCustomText  = CASE WHEN @incSettings = 1 THEN original.includeConnectionInstructionCustomText ELSE 0 END,
		customText  = CASE WHEN @incSettings = 1 THEN original.customText ELSE '' END
		FROM dbo.tblSeminarsSWOD AS newRecord
		INNER JOIN dbo.tblSeminarsSWOD AS original ON original.seminarID = @seminarID
		WHERE newRecord.seminarID = @newSeminarID;

		-- auto opt in the publisher
		INSERT INTO dbo.tblSeminarsOptIn (seminarID, participantID, isPriceBasedOnActual, freeRateDisplay, dateAdded, IsActive)
		SELECT seminarID, participantID, isPriceBasedOnActual, freeRateDisplay, GETDATE(), 1
		FROM dbo.tblSeminars
		WHERE seminarID = @newSeminarID;

		IF @incSpeakers = 1
			INSERT INTO dbo.tblSeminarsAndAuthors (seminarID, authorID, authorOrder)
			SELECT @newSeminarID, saa.authorID, saa.authorOrder
			FROM dbo.tblSeminarsAndAuthors as saa
			INNER JOIN dbo.tblAuthors as a on a.authorID = saa.authorID
			WHERE saa.seminarID = @seminarID
			AND a.authorTypeID = @speakerAuthorTypeID;

		IF @incCatalog = 1
		BEGIN
			--Subjects
			INSERT INTO dbo.tblSeminarsAndCategories (seminarID, categoryID)
			SELECT @newSeminarID, categoryID
			FROM dbo.tblSeminarsAndCategories
			WHERE seminarID = @seminarID;

			-- copy rates
			EXEC dbo.sw_copyRates @participantID=@participantID, @copyFromSeminarID=@seminarID, @copyToSeminarID=@newSeminarID, 
				@recordedByMemberID=@recordedByMemberID;

			--isFeatured
			INSERT INTO dbo.tblFeaturedPrograms (participantID, seminarID)
			SELECT TOP 1 participantID, @newSeminarID
			FROM dbo.tblFeaturedPrograms f
			WHERE f.seminarID = @seminarID;

		END

		-- record fee for seminar creation
		EXEC dbo.swod_addBillingLogForSeminarCreation @orgcode=@orgcode, @seminarID=@newSeminarID, @recordedByMemberID=@recordedByMemberID;

		-- seminar credit authorities
		IF @incCredit = 1
			EXEC dbo.sw_copyCreditAuthorities @copyFromSeminarID=@seminarID, @copyToSeminarID=@newSeminarID, @recordedByMemberID=@recordedByMemberID;
		
		IF @incSettings = 1
		BEGIN
			-- PreTest/Exam/Evaluation
			INSERT INTO dbo.tblSeminarsAndForms (seminarID, formID, loadPoint, orderBy, isRequired, maxMinutesAllowed, certifiedStatement, 
				maxTriesPerQuestion, allowSkipBackward, includeQuestions, enforceQReqStatusCorrectMaxTries , showImmediateAnswer, 
				overrideMessage, numResponsesPerEnrollment)
			SELECT @newSeminarID, formID, loadPoint, orderBy, isRequired, maxMinutesAllowed, certifiedStatement, 
				maxTriesPerQuestion, allowSkipBackward, includeQuestions, enforceQReqStatusCorrectMaxTries , showImmediateAnswer, 
				overrideMessage, numResponsesPerEnrollment 
			FROM dbo.tblSeminarsAndForms
			WHERE seminarID = @seminarID;

			-- author link
			-- This SHOULD NOT copy the provider fields.
			INSERT INTO dbo.tblSeminarsAndAuthors (seminarID, authorID, authorOrder)
			SELECT @newSeminarID, saa.authorID, saa.authorOrder
			FROM dbo.tblSeminarsAndAuthors as saa
			INNER JOIN dbo.tblAuthors as a on a.authorID = saa.authorID
			WHERE saa.seminarID = @seminarID
			AND a.authorTypeID = @moderatorAuthorTypeID;

			EXEC dbo.sw_copySeminarLinks @copyFromSeminarID=@seminarID, @copyToSeminarID=@newSeminarID, @recordedByMemberID=@recordedByMemberID;

			-- Custom Fields - seminar field groupings
			INSERT INTO membercentral.dbo.cf_fieldsGrouping (fieldGrouping, fieldGroupingDesc, fieldControllingSiteResourceID, fieldUsageID, fieldDetailID, fieldGroupingOrder)
			select fieldGrouping, fieldGroupingDesc, @SWAdminSiteResourceID, fieldUsageID, @newSeminarID, fieldGroupingOrder
			from membercentral.dbo.cf_fieldsGrouping
			where fieldControllingSiteResourceID = @SWAdminSiteResourceID
			and fieldUsageID = @fieldUsageID
			and fieldDetailID = @seminarID;

			-- seminar custom fields
			INSERT INTO membercentral.dbo.cf_fields (fieldGroupingID, controllingSiteResourceID, usageID, detailID, fieldTypeID, fieldText, fieldReference, isRequired, requiredMsg, 
				adminOnly, displayOnly, autoFillRegistrant, amount, GLAccountID, inventory, [uid], fieldOrder)
			select distinct fg.fieldGroupingID, @SWAdminSiteResourceID, fOld.usageID, @newSeminarID, fOld.fieldTypeID, fOld.fieldText, fOld.fieldReference, 
				fOld.isRequired, fOld.requiredMsg, fOld.adminOnly, fOld.displayOnly, fOld.autoFillRegistrant, fOld.amount, fOld.GLAccountID, fOld.inventory, 
				newid(), fOld.fieldOrder
			from membercentral.dbo.cf_fields as fOld
			left outer join membercentral.dbo.cf_fieldsGrouping AS fgOld on fgOld.fieldGroupingID = fOld.fieldGroupingID 
				and fgOld.fieldControllingSiteResourceID = @SWAdminSiteResourceID 
				and fgOld.fieldDetailID = @seminarID
				and fgOld.fieldUsageID = @fieldUsageID
			left outer join membercentral.dbo.cf_fieldsGrouping AS fg ON fg.fieldControllingSiteResourceID = @SWAdminSiteResourceID
				and fg.fieldUsageID = @fieldUsageID
				and fg.fieldDetailID = @newSeminarID
				and fg.fieldGrouping = fgOld.fieldGrouping
			where fOld.usageID = @fieldUsageID
			and fOld.controllingSiteResourceID = @SWAdminSiteResourceID
			and fOld.isActive = 1
			and fOld.detailID = @seminarID;
		
			-- seminar custom field options
			select @minFieldID = min(fOld.fieldID)
				from membercentral.dbo.cf_fields as fOld
				inner join membercentral.dbo.cf_fieldTypes as ft on ft.fieldTypeID = fOld.fieldTypeID
				where fOld.usageID = @fieldUsageID
				and fOld.controllingSiteResourceID = @SWAdminSiteResourceID
				and fOld.isActive = 1
				and fOld.detailID = @seminarID
				and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
				and exists (select 1 from membercentral.dbo.cf_fieldValues where fieldID = fOld.fieldID);
			WHILE @minFieldID IS NOT NULL BEGIN
				set @newFieldID = null;

				select @newFieldID = fNew.fieldID
				from membercentral.dbo.cf_fields as fNew
				inner join membercentral.dbo.cf_fieldUsages as fu on fu.usageID = fNew.usageID
					and fNew.usageID = @fieldUsageID
					and fNew.controllingSiteResourceID = @SWAdminSiteResourceID
					and fNew.isActive = 1
					and fNew.detailID = @newSeminarID
				inner join membercentral.dbo.cf_fields as fOld on fOld.fieldReference = fNew.fieldReference
				where fOld.fieldID = @minFieldID;

				IF @newFieldID IS NOT NULL 
					INSERT INTO membercentral.dbo.cf_fieldValues (fieldID, valueString, valueDecimal2, valueInteger, valueBit, valueDate, valueSiteResourceID, amount, inventory, valueOrder)
					select @newFieldID, valueString, valueDecimal2, valueInteger, valueBit, valueDate, valueSiteResourceID, amount, inventory, valueOrder
					from membercentral.dbo.cf_fieldValues
					where fieldID = @minFieldID;

				select @minFieldID = min(fOld.fieldID)
				from membercentral.dbo.cf_fields as fOld
				inner join membercentral.dbo.cf_fieldTypes as ft on ft.fieldTypeID = fOld.fieldTypeID
				where fOld.usageID = @fieldUsageID
				and fOld.controllingSiteResourceID = @SWAdminSiteResourceID
				and fOld.isActive = 1
				and fOld.detailID = @seminarID
				and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
				and exists (select 1 from membercentral.dbo.cf_fieldValues where fieldID = fOld.fieldID)
				and fOld.fieldID > @minFieldID;
			END
		END

		-- titles
		IF @incTitles = 1 BEGIN
			DECLARE @tblTitles TABLE (titleID int);

			INSERT INTO @tblTitles (titleID)
			SELECT titleID
			FROM dbo.tblSeminarsAndTitles 
			WHERE seminarID = @seminarID;

			SELECT @oldTitleID = min(titleID) from @tblTitles;
			WHILE @oldTitleID IS NOT NULL BEGIN
				EXEC dbo.sw_copyTitle @titleID=@oldTitleID, @newSeminarID=@newSeminarID, @oldSeminarID=@seminarID,
					@recordedByMemberID=@recordedByMemberID, @newTitleID=@newTitleID OUTPUT;

				SELECT @oldTitleID = min(titleID) from @tblTitles where titleID > @oldTitleID;
			END
		END
		
	COMMIT TRAN;

	SELECT @auditmsg = replace(memberCentral.dbo.fn_cleanInvalidXMLChars('SWOD-' + cast(seminarID AS varchar(20)) + ' [' + seminarName + '] has been created by copying SWOD-' + cast(@seminarID AS varchar(20)) + '.'),'"','\"')
	FROM dbo.tblSeminars
	WHERE seminarID = @newSeminarID;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + cast(@orgID AS varchar(10)) + ',
		"SITEID":' + cast(@siteID AS varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@recordedByMemberID AS varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MESSAGE":"' + @auditmsg + '" } }');

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
