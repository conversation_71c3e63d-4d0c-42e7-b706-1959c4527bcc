ALTER PROC dbo.tr_getAllocatedPaymentTransactionsForPartialPayment
@orgID int,
@paymentTransactionID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpAllocatedPaymentTrans') IS NOT NULL
		DROP TABLE #tmpAllocatedPaymentTrans;
	IF OBJECT_ID('tempdb..#tmpProcessingFeeTrans') IS NOT NULL
		DROP TABLE #tmpProcessingFeeTrans;

	CREATE TABLE #tmpAllocatedPaymentTrans (transactionID int, allocAmount decimal(18,2), allocDate datetime, PITTaxTID int);
	CREATE TABLE #tmpProcessingFeeTrans (transactionID int, allocAmount decimal(18,2), typeID int, stateIDForTax int, zipForTax varchar(25), 
		paymentFeeType<PERSON> tinyint, detail varchar(500), GLAccountID int);

	DECLARE @tr_SalesTaxTrans int, @processingFees decimal(18,2), @processingFeesTax decimal(18,2);

	SET @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

	-- allocated payment transactions
	INSERT INTO #tmpAllocatedPaymentTrans (transactionID, allocAmount, allocDate, PITTaxTID)
	SELECT transactionID, allocAmount, allocDate, PITTaxTID
	FROM dbo.fn_tr_getAllocatedTransactionsofPayment(@orgID,@paymentTransactionID);

	-- processing fee sales
	INSERT INTO #tmpProcessingFeeTrans (transactionID, allocAmount, typeID, stateIDForTax, zipForTax, paymentFeeTypeID, detail, GLAccountID)
	SELECT tmp.transactionID, tmp.allocAmount, t.typeID, ts.stateIDForTax, ts.zipForTax, ts.paymentFeeTypeID, t.detail, t.creditGLAccountID
	FROM #tmpAllocatedPaymentTrans AS tmp
	INNER JOIN dbo.tr_transactionSales AS ts ON ts.orgID = @orgID AND ts.transactionID = tmp.transactionID
	INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = @orgID AND t.transactionID = ts.transactionID
	WHERE ts.paymentFeeTypeID IN (1,2);

	-- processing fee sale tax
	INSERT INTO #tmpProcessingFeeTrans (transactionID, allocAmount, typeID, stateIDForTax, zipForTax, paymentFeeTypeID, detail, GLAccountID)
	SELECT tsTax.transactionID, atop.allocAmount, t.typeID, tmp.stateIDForTax, tmp.zipForTax, tmp.paymentFeeTypeID, t.detail, tmp.GLAccountID
	FROM #tmpProcessingFeeTrans AS tmp
	INNER JOIN dbo.tr_relationships AS tr ON tr.orgID = @orgID AND tr.typeID = @tr_SalesTaxTrans AND tr.appliedToTransactionID = tmp.transactionID
	INNER JOIN dbo.tr_transactionSales AS tsTax ON tsTax.orgID = @orgID AND tsTax.transactionID = tr.transactionID
	INNER JOIN #tmpAllocatedPaymentTrans AS atop ON atop.transactionID = tsTax.transactionID
	INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = @orgID AND t.transactionID = tsTax.transactionID;

	SELECT @processingFees = SUM(allocAmount)
	FROM #tmpProcessingFeeTrans
	WHERE typeID = 1;

	SELECT @processingFeesTax = SUM(allocAmount)
	FROM #tmpProcessingFeeTrans
	WHERE typeID = 7;

	-- allocated transactions excluding processing fees
	SELECT tmp.transactionID, tmp.allocAmount, tmp.allocDate, tmp.PITTaxTID, NULL AS newAllocTID
	FROM #tmpAllocatedPaymentTrans AS tmp
	LEFT OUTER JOIN #tmpProcessingFeeTrans AS pf ON pf.transactionID = tmp.transactionID
	WHERE pf.transactionID IS NULL
	ORDER BY tmp.transactionID;

	-- processing fees
	SELECT TOP 1 ISNULL(@processingFees,0) AS processingFees, ISNULL(@processingFeesTax,0) AS processingFeesTax, ISNULL(@processingFees,0) + ISNULL(@processingFeesTax,0) AS processingFeesIncTax, 
		stateIDForTax, zipForTax, paymentFeeTypeID, detail, GLAccountID
	FROM #tmpProcessingFeeTrans
	WHERE typeID = 1;

	IF OBJECT_ID('tempdb..#tmpAllocatedPaymentTrans') IS NOT NULL
		DROP TABLE #tmpAllocatedPaymentTrans;
	IF OBJECT_ID('tempdb..#tmpProcessingFeeTrans') IS NOT NULL
		DROP TABLE #tmpProcessingFeeTrans;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN -1;
END CATCH
GO
