CREATE TRIGGER [dbo].[trg_ref_clientsInsert] ON [dbo].[ref_clients]
AFTER INSERT
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN

    UPDATE c
    SET c.phoneForSearch = rtrim(ltrim(
		dbo.fn_RegExReplace(isnull(c.homePhone,''),'[^0-9]','') + ' ' + 
		dbo.fn_RegExReplace(isnull(c.cellPhone,''),'[^0-9]','') + ' ' +
		dbo.fn_RegExReplace(isnull(c.alternatePhone,''),'[^0-9]','')
		))
	FROM dbo.ref_clients as c 
	INNER JOIN Inserted as I ON c.clientID = I.clientID;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
ALTER TABLE [dbo].[ref_clients] ENABLE TRIGGER [trg_ref_clientsInsert]
GO
