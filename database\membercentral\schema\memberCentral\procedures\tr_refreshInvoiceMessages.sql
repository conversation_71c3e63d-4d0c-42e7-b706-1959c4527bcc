ALTER PROC dbo.tr_refreshInvoiceMessages
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpInvoicesEOI') IS NULL 
		RAISERROR('invoicesEOI is not found.',16,1);
	IF NOT EXISTS (select 1 from #tmpInvoicesEOI)
		GOTO on_done;

	declare @itid int, @contentVersionID int, @ARGLAID int;
	EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAID OUTPUT;

	IF OBJECT_ID('tempdb..#tblIT') IS NOT NULL 
		DROP TABLE #tblIT;
	CREATE TABLE #tblIT (itid int PRIMARY KEY, contentVersionID int);

	-- sales / tax / adj up on invoice that would have a different content version than they really do
	insert into #tblIT (itid, contentVersionID)
	select distinct it.itid, cv.contentVersionID
	from dbo.tr_invoices as i
	inner join #tmpInvoicesEOI as tmp on tmp.invoiceID = i.invoiceID
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID 
		and it.invoiceID = i.invoiceID
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID
		and t.transactionID = it.transactionID
	inner join dbo.tr_glAccounts as gl on gl.orgID = @orgID
		and gl.glAccountID = t.creditGLAccountID
	left outer JOIN dbo.cms_content as c 
		INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = c.siteResourceID 
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = c.contentID AND cl.languageID = 1
		INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID AND cv.isActive = 1 AND len(cv.rawContent) > 0
		on c.contentID = gl.invoiceContentID
	where i.orgID = @orgID
	and t.typeID in (1,3,7)
	and t.debitGLAccountID = @ARGLAID
	and isnull(it.messageContentVersionID,0) <> isnull(cv.contentVersionID,0);

	-- adj down on invoice that would have a different content version than they really do
	insert into #tblIT (itid, contentVersionID)
	select distinct it.itid, cv.contentVersionID
	from dbo.tr_invoices as i
	inner join #tmpInvoicesEOI as tmp on tmp.invoiceID = i.invoiceID
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID 
		and it.invoiceID = i.invoiceID
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID 
		and t.transactionID = it.transactionID
	inner join tr_glAccounts as gl on gl.orgID = @orgID
		and gl.glAccountID = t.debitGLAccountID
	left outer JOIN dbo.cms_content as c 
		INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = c.siteResourceID 
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = c.contentID AND cl.languageID = 1
		INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID AND cv.isActive = 1 AND len(cv.rawContent) > 0
		on c.contentID = gl.invoiceContentID
	where i.orgID = @orgID
	and t.typeID = 3
	and t.creditGLAccountID = @ARGLAID
	and isnull(it.messageContentVersionID,0) <> isnull(cv.contentVersionID,0);

	BEGIN TRAN;
		-- update the ones that have changed
		select @itid = min(itid) from #tblIT;
		while @itid is not null begin
			set @contentVersionID = null;
			select @contentVersionID = contentVersionID from #tblIT where itid = @itid;

			update dbo.tr_invoiceTransactions 
			set messageContentVersionID = @contentVersionID
			where itid = @itid;
		
			select @itid = min(itid) from #tblIT where itid > @itid;
		end
	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tblIT') IS NOT NULL 
		DROP TABLE #tblIT;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
