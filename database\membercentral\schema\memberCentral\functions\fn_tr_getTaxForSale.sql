ALTER FUNCTION dbo.fn_tr_getTaxForSale (
	@orgID int,
	@saleTransactionID int,
	@stateIDForTax int -- null to use stateid from member record
)
RETURNS TABLE 
AS
RETURN (

	-- this uses the transaction's amount after adjustment amount to determine the tax amount
	WITH rules AS (
		select ta.orgID, tr.taxAuthorityID, isnull(tr.saleGLAccountID,0) as saleGLAccountID, tr.stateID, tr.taxRuleID, 0 as theLevel
		from dbo.tr_taxRules as tr
		inner join dbo.tr_taxAuthorities as ta on ta.orgID = @orgID 
			and ta.taxAuthorityID = tr.taxAuthorityID 
			and ta.status = 'A'
		inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID 
			and t.transactionID = @saleTransactionID
			and t.statusID = 1
			and t.typeID = 1
			and t.transactionDate between tr.startDate and tr.endDate
		inner join dbo.ams_members as m on m.orgID = @orgID 
			and m.memberid = t.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.orgID = @orgID
			and m2.memberid = m.activeMemberID
		left outer join dbo.ams_memberAddressTags as matag 
			inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
				and matagt.addressTagTypeID = matag.addressTagTypeID 
				and matagt.addressTagType = 'Billing'
			inner join dbo.ams_memberAddresses as ma on ma.orgID = @orgID
				and ma.memberID = matag.memberID 
				and ma.addressTypeID = matag.addressTypeID
			on matag.orgID = @orgID AND matag.memberID = m2.memberID
		where tr.status = 'A'
		and coalesce(@stateIDForTax,ma.stateid,0) = tr.stateid
			union all
		select rules.orgID, rules.taxAuthorityID, gl.glAccountID, rules.stateID, rules.taxRuleID, rules.theLevel + 1 as theLevel
		from dbo.tr_glAccounts as gl
		inner join rules on rules.saleGLAccountID = isnull(gl.parentGLAccountID,0) 
			and rules.orgID = gl.orgID
		where gl.orgID = @orgID
	)
	select ROW_NUMBER() OVER (ORDER BY ta.authorityName, tr.taxRate) AS row,
		ta.taxAuthorityID, ta.GLAccountID as taxGLAccountID, glT.deferredGLAccountID, ta.authorityName, r.taxRuleID, tr.taxRate, 
		cast((cast(tsFull.cache_amountAfterAdjustment as decimal(18,5)) * tr.taxRate) as decimal(18,2)) as taxAmount
	from (
		select taxAuthorityID, saleGLAccountID, stateID, min(theLevel) as minLevel
		from rules
		group by taxAuthorityID, saleGLAccountID, stateID
	) as grp
	inner join rules as r on r.taxAuthorityID = grp.taxAuthorityID
		and r.saleGLAccountID = grp.saleGLAccountID
		and r.stateID = grp.stateID
		and r.theLevel = grp.minLevel
	inner join dbo.tr_taxAuthorities as ta on ta.orgID = @orgID 
		and ta.taxAuthorityID = r.taxAuthorityID
	inner join dbo.tr_glAccounts as glT on glT.orgID = @orgID 
		and glT.glaccountID = ta.GLAccountID
	inner join dbo.tr_taxRules as tr on tr.taxRuleID = r.taxRuleID
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID
		and t.transactionID = @saleTransactionID 
		and t.creditGLAccountID = r.saleGLAccountID
	cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull	
	inner join dbo.tr_glAccounts as gl on gl.orgID = @orgID 
		and gl.glaccountID = r.saleGLAccountID
	inner join dbo.tr_salesTaxProfiles as stp on stp.orgID = @orgID
		and stp.profileID = gl.salesTaxProfileID
	inner join dbo.tr_salesTaxProviders as stpr on stpr.providerID = stp.providerID
		and stpr.providerName = 'MemberCentral'

)
GO
