ALTER PROC dbo.queue_addMCPayECheckNSFBatches_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
	@xmldata xml, @itemID int, @ErrorMessage nvarchar(2048);

WHILE 1 = 1
BEGIN TRY

	/* NOTE!!!!!!!!
	This is not a good proc to use as a template for other Activated procs. 
	This proc purposely does not include any TRANSACTION code because there was an issue
	"Transaction failed because this DDL statement is not allowed inside a snapshot isolation transaction. 
    Since metadata is not versioned, a metadata change can lead to inconsistency if mixed within snapshot isolation."
	*/
	
	SELECT @DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, 
		@itemID = NULL, @ErrorMessage = NULL;

		WAITFOR (
			RECEIVE TOP (1) @DialogHandle = conversation_handle,
							@MessageType = message_type_name,
							@MessageBody = message_body
			FROM dbo.MCPayECheckNSFBatchQueue
		), TIMEOUT 1000;

		IF @DialogHandle IS NULL BEGIN
			BREAK;
		END

		IF @MessageType = N'PlatformQueue/GeneralXMLRequest' BEGIN
			BEGIN TRY
				SET @xmldata = CAST(@MessageBody AS XML);

				SELECT @itemID = @xmldata.value('(/mc/@i)[1]','int');
				IF @itemID IS NOT NULL
					EXEC membercentral.dbo.tr_addMCPayECheckNSFBatchFromQueue @itemID=@itemID;

				END CONVERSATION @DialogHandle;
			END TRY		
			BEGIN CATCH
				SET @ErrorMessage = N'queue_addMCPayECheckNSFBatches_Activated - ' + ERROR_MESSAGE();
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END CATCH
		END
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @DialogHandle;
			END 
			ELSE BEGIN
				IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
					SET @xmldata = cast(@MessageBody as xml);
					with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
					select @ErrorMessage = N'queue_addMCPayECheckNSFBatches_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
				ELSE BEGIN
					SET @ErrorMessage = N'queue_addMCPayECheckNSFBatches_Activated - Unexpected message type received: ' + @MessageType; 
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
			END
		END

END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH
GO
