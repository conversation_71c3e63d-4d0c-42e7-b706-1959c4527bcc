ALTER FUNCTION dbo.fn_ams_getOrgMemberColumns (
	@orgID int
)
RETURNS @orgMemberColumns TABLE (
	columnName sysname PRIMARY KEY,
	fieldCode varchar(40),
	columnDataType varchar(30),
	max_length int,
	allowMultiple bit,
	area varchar(25),
	areaID int,
	viewSuffix varchar(3),
	viewAlias varchar(5),
	defaultValueID int,
	MDdataTypeCode varchar(20),
	MDdisplayTypeCode varchar(20),
	allowNewValuesOnImport bit,
	allowNull bit,
	isReadOnly bit,
	orderBy int
)
AS
BEGIN

	DECLARE @orgCode varchar(10), @hasPrefix bit, @hasMiddleName bit, @hasSuffix bit, 
		@hasProfessionalSuffix bit, @showRecordType bit;
	
	DECLARE @tblColumnAreas TABLE (areaID tinyint PRIMARY KEY, area varchar(25), viewSuffix varchar(3), viewAlias varchar(5));
	DECLARE @tblCacheMDColumns TABLE (columnID int PRIMARY KEY, columnName varchar(128), dataTypeCode varchar(20), 
		displayTypeCode varchar(20), allowMultiple bit, defaultValueID int, allowNewValuesOnImport bit, allowNull bit, 
		isReadOnly bit);
	DECLARE @tblCacheColumns TABLE (columnName sysname PRIMARY KEY, fieldCode varchar(40), columnDataType varchar(30), max_length int, 
		areaID tinyint, allowMultiple bit, defaultValueID int, MDdataTypeCode varchar(20), MDdisplayTypeCode varchar(20), 
		realAreaID int, allowNewValuesOnImport bit, allowNull bit, isReadOnly bit);

	SELECT @orgcode=orgcode, @hasPrefix=hasPrefix, @hasMiddleName=hasMiddleName, @hasSuffix=hasSuffix, 
		@hasProfessionalSuffix=hasProfessionalSuffix
	FROM dbo.organizations 
	WHERE orgID = @orgID;

	IF EXISTS (select 1 from dbo.ams_recordTypes where orgID = @orgID)
		set @showRecordType = 1;
	ELSE
		set @showRecordType = 0;
	
	INSERT INTO @tblCacheMDColumns (columnID, columnName, dataTypeCode, displayTypeCode, allowMultiple, 
		allowNewValuesOnImport, allowNull, defaultValueID, isReadOnly)
	select mdc.columnID, mdc.columnName, dt.dataTypeCode, ddt.displayTypeCode, mdc.allowMultiple, 
		mdc.allowNewValuesOnImport, mdc.allowNull, 
		case when mdc.allowNull = 0 then mdc.defaultValueID else null end,
		mdc.isReadOnly
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
	where mdc.orgID = @orgID;

	INSERT INTO @tblColumnAreas (areaID, area, viewSuffix, viewAlias)
	VALUES 
		(1,'Demographics','m','m'), 
		(2,'Websites','mw','vwmw'), 
		(3,'Emails','me','vwme'), 
		(4,'Addresses','ma','vwma'), 
		(5,'Professional Licenses','mpl','vwmpl'), 
		(6,'Custom Fields','md','vwmd');

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly)
	VALUES 
		('FirstName', 'm_firstname', 'varchar', 75, 1, 0, 0), 
		('LastName', 'm_lastname', 'varchar', 75, 1, 0, 0), 
		('MemberNumber', 'm_membernumber', 'varchar', 50, 1, 0, 0),
		('NewMemberNumber', 'm_newmembernumber', 'varchar', 50, 1, 0, 0),
		('MCAccountType', 'm_membertypeid', 'varchar', 5, 1, 0, 0),
		('MCAccountStatus', 'm_status', 'varchar', 8, 1, 0, 0),
		('Company', 'm_company', 'varchar', 200, 1, 0, 0);
	IF @showRecordType = 1
		INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly) 
		VALUES ('MCRecordType', 'm_recordtypeid', 'varchar', 100, 1, 0, 0);
	IF @hasPrefix = 1
		INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly) 
		VALUES ('Prefix', 'm_prefix', 'varchar', 50, 1, 0, 0);
	IF @hasMiddleName = 1
		INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly) 
		VALUES ('MiddleName', 'm_middlename', 'varchar', 25, 1, 0, 0);
	IF @hasSuffix = 1
		INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly) 
		VALUES ('Suffix', 'm_suffix', 'varchar', 50, 1, 0, 0);
	IF @hasProfessionalSuffix = 1
		INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly) 
		VALUES ('ProfessionalSuffix', 'm_professionalsuffix', 'varchar', 100, 1, 0, 0);

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select websiteType, 'mw_' + cast(websiteTypeID as varchar(10)) + '_website', 'varchar', 400, 2, websiteTypeID, 0, 0
	from dbo.ams_memberWebsiteTypes 
	where orgID = @orgID;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select emailType, 'me_' + cast(emailTypeID as varchar(10)) + '_email', 'varchar', 255, 3, emailTypeID, 0, 0
	from dbo.ams_memberEmailTypes 
	where orgID = @orgID;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select emailTagType + 'EmailType', 'met_' + cast(emailTagTypeID as varchar(10)) + '_emailType', 'varchar', 60, 3, emailTagTypeID, 0, 0
	from dbo.ams_memberEmailTagTypes 
	where orgID = @orgID;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressType + '_attn', 'ma_' + cast(addressTypeID as varchar(10)) + '_attn', 'varchar', 100, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID 
	and hasAttn = 1;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressType + '_address1', 'ma_' + cast(addressTypeID as varchar(10)) + '_address1', 'varchar', 100, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly) 
	select addressType + '_address2', 'ma_' + cast(addressTypeID as varchar(10)) + '_address2', 'varchar', 100, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID 
	and hasAddress2 = 1;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressType + '_address3', 'ma_' + cast(addressTypeID as varchar(10)) + '_address3', 'varchar', 100, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID 
	and hasAddress3 = 1;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressType + '_city', 'ma_' + cast(addressTypeID as varchar(10)) + '_city', 'varchar', 75, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressType + '_stateprov', 'ma_' + cast(addressTypeID as varchar(10)) + '_stateprov', 'varchar', 4, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressType + '_postalCode', 'ma_' + cast(addressTypeID as varchar(10)) + '_postalCode', 'varchar', 25, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressType + '_county', 'ma_' + cast(addressTypeID as varchar(10)) + '_county', 'varchar', 50, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID 
	and hasCounty = 1;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressType + '_country', 'ma_' + cast(addressTypeID as varchar(10)) + '_country', 'varchar', 100, 4, addressTypeID, 0, 0 
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly)
	select mat.addressType + '_' + mpt.phonetype, 'mp_' + cast(mat.addressTypeID as varchar(10)) + '_' + cast(mpt.phoneTypeID as varchar(10)), 'varchar', 40, 4, 0, 0
	from dbo.ams_memberAddressTypes as mat
	cross join dbo.ams_memberPhoneTypes as mpt
	where mat.orgID = @orgID 
	and mpt.orgID = @orgID;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, isReadOnly)
	select addressTagType + 'AddressType', 'mat_' + cast(addressTagTypeID as varchar(10)) + '_addresstype', 'varchar', 60, 4, addressTagTypeID, 0, 0 
	from dbo.ams_memberAddressTagTypes 
	where orgID = @orgID;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly)
	select PLName + '_licenseNumber', 'mpl_' + cast(PLTypeID as varchar(10)) + '_licenseNumber', 'varchar', 200, 5, 0, 0 
	from dbo.ams_memberProfessionalLicenseTypes 
	where orgID = @orgID;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly)
	select PLName + '_activeDate', 'mpl_' + cast(PLTypeID as varchar(10)) + '_activeDate', 'datetime', 0, 5, 0, 0 
	from dbo.ams_memberProfessionalLicenseTypes 
	where orgID = @orgID;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, allowMultiple, isReadOnly)
	select PLName + '_status', 'mpl_' + cast(PLTypeID as varchar(10)) + '_status', 'varchar', 200, 5, 0, 0 
	from dbo.ams_memberProfessionalLicenseTypes 
	where orgID = @orgID;

	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, defaultValueID, MDdataTypeCode, 
		MDdisplayTypeCode, allowNewValuesOnImport, allowNull, isReadOnly)
	select columnName, 'md_' + cast(columnID as varchar(10)), 'varchar', 255, 6, columnID, allowMultiple, defaultValueID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, 
		allowNull, isReadOnly
	from @tblCacheMDColumns 
	where dataTypeCode = 'STRING' 
	and allowMultiple = 0;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, defaultValueID, MDdataTypeCode, 
		MDdisplayTypeCode, allowNewValuesOnImport, allowNull, isReadOnly)
	select columnName, 'md_' + cast(columnID as varchar(10)), 'varchar', 8000, 6, columnID, allowMultiple, defaultValueID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, 
		allowNull, isReadOnly
	from @tblCacheMDColumns 
	where dataTypeCode = 'STRING' 
	and allowMultiple = 1;
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, defaultValueID, MDdataTypeCode, 
		MDdisplayTypeCode, allowNewValuesOnImport, allowNull, isReadOnly)
	select columnName, 'md_' + cast(columnID as varchar(10)), 'decimal', 0, 6, columnID, allowMultiple, defaultValueID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, 
		allowNull, isReadOnly
	from @tblCacheMDColumns 
	where dataTypeCode = 'DECIMAL2';
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, defaultValueID, MDdataTypeCode, 
		MDdisplayTypeCode, allowNewValuesOnImport, allowNull, isReadOnly)
	select columnName, 'md_' + cast(columnID as varchar(10)), 'int', 0, 6, columnID, allowMultiple, defaultValueID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, allowNull,
		isReadOnly
	from @tblCacheMDColumns 
	where dataTypeCode = 'INTEGER';
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, defaultValueID, MDdataTypeCode, 
		MDdisplayTypeCode, allowNewValuesOnImport, allowNull, isReadOnly)
	select columnName, 'md_' + cast(columnID as varchar(10)), 'date', 0, 6, columnID, allowMultiple, defaultValueID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, allowNull, 
		isReadOnly
	from @tblCacheMDColumns 
	where dataTypeCode = 'DATE';
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, defaultValueID, MDdataTypeCode, 
		MDdisplayTypeCode, allowNewValuesOnImport, allowNull, isReadOnly)
	select columnName, 'md_' + cast(columnID as varchar(10)), 'bit', 0, 6, columnID, allowMultiple, defaultValueID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, allowNull, 
		isReadOnly
	from @tblCacheMDColumns 
	where dataTypeCode = 'BIT';
	
	INSERT INTO @tblCacheColumns (columnName, fieldCode, columnDataType, max_length, areaID, realAreaID, allowMultiple, defaultValueID, MDdataTypeCode, 
		MDdisplayTypeCode, allowNewValuesOnImport, allowNull, isReadOnly)
	select columnName, 'md_' + cast(columnID as varchar(10)), 'varchar', -1, 6, columnID, allowMultiple, defaultValueID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, 
		allowNull, isReadOnly
	from @tblCacheMDColumns 
	where dataTypeCode = 'CONTENTOBJ';

	INSERT INTO @orgMemberColumns (columnName, fieldCode, columnDataType, max_length, allowMultiple, area, areaID, 
		viewSuffix, viewAlias, defaultValueID, MDdataTypeCode, MDdisplayTypeCode, allowNewValuesOnImport, allowNull, 
		isReadOnly, orderBy)
	select c.columnName, c.fieldCode, c.columnDataType, c.max_length, c.allowMultiple, ca.area, c.realAreaID,
		ca.viewSuffix, ca.viewAlias, c.defaultValueID, c.MDdataTypeCode, c.MDdisplayTypeCode, c.allowNewValuesOnImport, 
		c.allowNull, c.isReadOnly, ROW_NUMBER() OVER (order by c.areaID, c.columnName) as orderBy
	from @tblCacheColumns as c
	inner join @tblColumnAreas as ca on ca.areaID = c.areaID;

	RETURN;
END
GO
