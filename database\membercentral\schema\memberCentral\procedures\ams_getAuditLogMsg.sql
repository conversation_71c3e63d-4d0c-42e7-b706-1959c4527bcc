ALTER PROC dbo.ams_getAuditLogMsg
@auditLogTable varchar(60),
@outputAsChangesArray bit = 0,
@isBulkUpdate bit = 0,
@msg varchar(max) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLogCol_oldVal') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_oldVal;
	IF OBJECT_ID('tempdb..#tmpAuditLogCol_newVal') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_newVal;
	IF OBJECT_ID('tempdb..#tmpAuditLogCol_dataTypeCode') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_dataTypeCode;
	CREATE TABLE #tmpAuditLogCol_oldVal (refID int, col varchar(max), oldValue varchar(max));
	CREATE TABLE #tmpAuditLogCol_newVal (refID int, col varchar(max), newValue varchar(max));
	CREATE TABLE #tmpAuditLogCol_dataTypeCode (col varchar(max), dataTypeCode varchar(30));

	IF @isBulkUpdate = 0 BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NOT NULL
			DROP TABLE #tmpAuditLogMessages;
		CREATE TABLE #tmpAuditLogMessages (rowID INT IDENTITY(1,1), refID int, msg varchar(MAX), col varchar(max));
	END
	ELSE BEGIN
		-- ensure tmpAuditLogMessages exists
		IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NULL 
			RAISERROR('Designated result table does not exist.',16,1);

		TRUNCATE TABLE #tmpAuditLogMessages;
	END

	DECLARE @colNameList varchar(max), @updateColList varchar(max), @count int, 
		@dynSQL nvarchar(max), @output nvarchar(50), @delimiter varchar(2);

	-- ensure auditLogTable exists
	IF OBJECT_ID('tempdb..'+@auditLogTable) IS NULL 
		RAISERROR('Designated source table does not exist.',16,1);
	
	SET @dynSQL = N'SELECT @count=COUNT(*) FROM '+@auditLogTable;
	SET @outPut = N'@count int OUTPUT';
	EXEC sp_executesql @dynSQL, @outPut, @count=@count OUTPUT;

	IF (@isBulkUpdate = 0 AND @count <> 3) OR (@isBulkUpdate = 1 AND @count < 3)
		GOTO on_auditLog_done;

	-- cols
	SELECT @colNameList = COALESCE(@colNameList + ',','') + '[' + [name] + ']'
	FROM tempdb.sys.columns 
	WHERE object_id = object_id('tempdb..'+@auditLogTable)
	AND [name] NOT IN ('autoID','rowCode','refID')
	ORDER BY column_id;

	IF @colNameList IS NULL
		GOTO on_auditLog_done;

	-- update all cols datatype to varchar(max) to not cause conflicts with the type of other columns specified in the UNPIVOT list.
	SET @updateColList = NULL;

	SELECT @updateColList = COALESCE(@updateColList + '; ', '') + 'ALTER TABLE ' + @auditLogTable + ' ALTER COLUMN ' + tbl.listitem + ' varchar(max) NULL'
	FROM dbo.fn_varcharListToTableInline(@colNameList,',') as tbl;
	
	EXEC(@updateColList);

	-- columns with null values
	SET @updateColList = NULL;

	SELECT @updateColList = COALESCE(@updateColList + ', ', '') + tbl.listitem + ' = ISNULL(' + tbl.listitem + ','''')'
	FROM dbo.fn_varcharListToTableInline(@colNameList,',') as tbl;

	EXEC('UPDATE ' + @auditLogTable + ' SET ' +  @updateColList + ';');

	IF @isBulkUpdate = 1 BEGIN
		INSERT INTO #tmpAuditLogCol_oldVal (refID, col, oldValue)
		EXEC('SELECT refID, col, oldValue FROM (SELECT refID, '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''OLDVAL'') p
				UNPIVOT (oldValue FOR col IN('+ @colNameList +')) AS upvt');

		INSERT INTO #tmpAuditLogCol_newVal (refID, col, newValue)
		EXEC('SELECT refID, col, newValue FROM (SELECT refID, '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''NEWVAL'') p
				UNPIVOT (newValue FOR col IN('+ @colNameList +')) AS upvt');
	END
	ELSE BEGIN
		-- adding dummy value for refID as there will be only one entry in the source table
		INSERT INTO #tmpAuditLogCol_oldVal (refID, col, oldValue)
		EXEC('SELECT 0, col, oldValue FROM (SELECT '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''OLDVAL'') p
				UNPIVOT (oldValue FOR col IN('+ @colNameList +')) AS upvt');

		INSERT INTO #tmpAuditLogCol_newVal (refID, col, newValue)
		EXEC('SELECT 0, col, newValue FROM (SELECT '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''NEWVAL'') p
				UNPIVOT (newValue FOR col IN('+ @colNameList +')) AS upvt');
	END

	INSERT INTO #tmpAuditLogCol_dataTypeCode (col, dataTypeCode)
	EXEC('SELECT col, dataTypeCode FROM (SELECT '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''DATATYPECODE'') p
			UNPIVOT (dataTypeCode FOR col IN('+ @colNameList +')) AS upvt');

	-- handle NULL
	UPDATE #tmpAuditLogCol_oldVal SET oldValue = ISNULL(oldValue,'') WHERE oldValue IS NULL;
	UPDATE #tmpAuditLogCol_newVal SET newValue = ISNULL(newValue,'') WHERE newValue IS NULL;

	-- BIT
	UPDATE tmpOld 
	SET tmpOld.oldValue = CASE WHEN tmpOld.oldValue = '1' THEN 'Yes' ELSE 'No' END
	FROM #tmpAuditLogCol_oldVal AS tmpOld
	INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpOld.col
	WHERE tmpDT.dataTypeCode = 'BIT'
	AND tmpOld.oldValue IN ('0','1');

	UPDATE tmpNew 
	SET tmpNew.newValue = CASE WHEN tmpNew.newValue = '1' THEN 'Yes' ELSE 'No' END
	FROM #tmpAuditLogCol_newVal AS tmpNew
	INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpNew.col
	WHERE tmpDT.dataTypeCode = 'BIT'
	AND tmpNew.newValue IN ('0','1');

	-- empty
	UPDATE #tmpAuditLogCol_oldVal SET oldValue = 'blank' WHERE oldValue = '';
	UPDATE #tmpAuditLogCol_newVal SET newValue = 'blank' WHERE newValue = '';

	-- escape special chars
	UPDATE tmp
	SET tmp.oldValue = STRING_ESCAPE(tmp.oldValue,'json')
	FROM #tmpAuditLogCol_oldVal AS tmp
	INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmp.col
	WHERE tmpDT.dataTypeCode <> 'CONTENTOBJ';

	UPDATE tmp
	SET tmp.newValue = STRING_ESCAPE(tmp.newValue,'json')
	FROM #tmpAuditLogCol_newVal AS tmp
	INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmp.col
	WHERE tmpDT.dataTypeCode <> 'CONTENTOBJ';

	-- auditLog msg
	IF @outputAsChangesArray = 1 BEGIN
		SET @delimiter = ',';

		INSERT INTO #tmpAuditLogMessages (refID, msg, col)
		SELECT tmpOld.refID, '{' +
				'"ITEM":"'+ tmpOld.col +'",' +
				'"NEWVALUE":"'+ tmpNew.newValue +'",' +
				'"OLDVALUE":"'+ tmpOld.oldValue +'"' +
			'}',
			tmpOld.col
		FROM #tmpAuditLogCol_oldVal AS tmpOld
		INNER JOIN #tmpAuditLogCol_newVal AS tmpNew ON tmpNew.refID = tmpOld.refID
			AND tmpNew.col = tmpOld.col
		INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpOld.col
		WHERE tmpOld.oldValue <> tmpNew.newValue
		AND tmpDT.dataTypeCode IN ('STRING','INTEGER','DECIMAL2','DATE','BIT');
	END
	ELSE BEGIN
		SET @delimiter = char(13) + char(10);

		INSERT INTO #tmpAuditLogMessages (refID, msg, col)
		SELECT tmpOld.refID, tmpOld.col + ' changed from [' + tmpOld.oldValue + '] to [' + tmpNew.newValue + '].', tmpOld.col
		FROM #tmpAuditLogCol_oldVal AS tmpOld
		INNER JOIN #tmpAuditLogCol_newVal AS tmpNew ON tmpNew.refID = tmpOld.refID
			AND tmpNew.col = tmpOld.col
		INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpOld.col
		WHERE tmpOld.oldValue <> tmpNew.newValue
		AND tmpDT.dataTypeCode IN ('STRING','INTEGER','DECIMAL2','DATE','BIT');

		INSERT INTO #tmpAuditLogMessages (refID, msg, col)
		SELECT tmpOld.refID, tmpOld.col + ' updated.', tmpOld.col
		FROM #tmpAuditLogCol_oldVal AS tmpOld
		INNER JOIN #tmpAuditLogCol_newVal AS tmpNew ON tmpNew.refID = tmpOld.refID
			AND tmpNew.col = tmpOld.col
		INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpOld.col
		WHERE tmpOld.oldValue <> tmpNew.newValue
		AND tmpDT.dataTypeCode = 'CONTENTOBJ';
	END

	IF NOT EXISTS (SELECT 1 FROM #tmpAuditLogMessages)
		GOTO on_auditLog_done;

	SET @msg = NULL;

	IF @isBulkUpdate = 0 BEGIN
		SELECT @msg = COALESCE(@msg + @delimiter,'') + tmp.msg
		FROM #tmpAuditLogMessages AS tmp
		INNER JOIN tempdb.sys.columns AS tmpSys ON object_id = object_id('tempdb..'+@auditLogTable)
			AND tmpSys.[name] = tmp.col
		WHERE tmp.msg IS NOT NULL
		ORDER BY tmpSys.column_id;
	END

	on_auditLog_done:

	SET @msg = ISNULL(@msg,'');

	IF OBJECT_ID('tempdb..#tmpAuditLogCol_oldVal') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_oldVal;
	IF OBJECT_ID('tempdb..#tmpAuditLogCol_newVal') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_newVal;
	IF OBJECT_ID('tempdb..#tmpAuditLogCol_dataTypeCode') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_dataTypeCode;
	IF @isBulkUpdate = 0 BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NOT NULL
			DROP TABLE #tmpAuditLogMessages;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
