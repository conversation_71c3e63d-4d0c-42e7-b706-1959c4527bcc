ALTER PROC dbo.queue_chatDocumentPrep_check 

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @queueStatusID int, @nextQueueStatusID int, 
		@errorSubject varchar(400), @errorTitle varchar(400);
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'chatDocumentPrep';

	-- chatDocumentPrep / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'grabbedForProcessing';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_chatDocumentPrep WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;

	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_chatDocumentPrep
		SET statusID = @nextQueueStatusID,
			dateUpdated = GETDATE()
		WHERE statusID = @queueStatusID
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='AI Chat Document Preparation Confirmation', @engine='BERLinux';

		SET @errorTitle = 'chatDocumentPrep Queue Issue';
		SET @errorSubject = 'chatDocumentPrep queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- chatDocumentPrep / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processingItem';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_chatDocumentPrep WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_chatDocumentPrep
		SET statusID = @nextQueueStatusID,
			dateUpdated = GETDATE()
		WHERE statusID = @queueStatusID
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='AI Chat Document Preparation Confirmation', @engine='BERLinux';

		SET @errorTitle = 'chatDocumentPrep Queue Issue';
		SET @errorSubject = 'chatDocumentPrep queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- chatDocumentPrep catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_chatDocumentPrep WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'chatDocumentPrep Queue Issue';
		SET @errorSubject = 'chatDocumentPrep queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
