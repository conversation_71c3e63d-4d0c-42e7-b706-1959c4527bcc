ALTER PROC dbo.queue_evRegistrantDelete_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
	@xmldata xml, @itemID int, @ErrorMessage nvarchar(2048);

WHILE 1 = 1
BEGIN TRY

	SELECT @DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL,
		@itemID = NULL, @ErrorMessage = NULL;

	-- initially set to snapshot. this allows us to go in and out throughout this request
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	BEGIN TRANSACTION;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		WAITFOR (
			RECEIVE TOP (1) @DialogHandle = conversation_handle,
							@MessageType = message_type_name,
							@MessageBody = message_body
			FROM dbo.EvRegistrantDeleteQueue
		), TIMEOUT 1000;

		IF @DialogHandle IS NULL BEGIN
			COMMIT TRANSACTION;
			BREAK;
		END

		IF @MessageType = N'PlatformQueue/GeneralXMLRequest' BEGIN
			BEGIN TRY
				SET @xmldata = cast(@MessageBody as xml);

				SELECT @itemID = @xmldata.value('(/mc/@i)[1]','int');
				IF @itemID is not null
					EXEC membercentral.dbo.ev_removeRegistrantFromQueue @itemID=@itemID;

				END CONVERSATION @DialogHandle;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET @ErrorMessage = N'queue_evRegistrantDelete_Activated - ' + ERROR_MESSAGE();
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END CATCH
		END
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @DialogHandle;
			END
			ELSE BEGIN
				IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
					SET @xmldata = cast(@MessageBody as xml);
					with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
						select @ErrorMessage = N'queue_evRegistrantDelete_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
				ELSE BEGIN
					SET @ErrorMessage = N'queue_evRegistrantDelete_Activated - Unexpected message type received: ' + @MessageType;
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
			END
		END

	IF @@trancount > 0
		COMMIT TRANSACTION;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH

IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
GO
