ALTER PROC dbo.tr_getNonPostedBatches
@orgID int,
@limitRowsCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @SevenDaysAgo datetime = DATEADD(d,-7,GETDATE()), @postedBatchStatus int, @totalCount int;

	SELECT @postedBatchStatus = statusID FROM dbo.tr_batchStatuses WHERE [status] = 'Posted';

	IF OBJECT_ID('tempdb..#tblOpenBatches') IS NOT NULL
		DROP TABLE #tblOpenBatches;
	IF OBJECT_ID('tempdb..#tmpAllBatches') IS NOT NULL
		DROP TABLE #tmpAllBatches;
	CREATE TABLE #tblOpenBatches (batchID int PRIMARY KEY);
	CREATE TABLE #tmpAllBatches (batchID int PRIMARY KEY, [status] varchar(25), batchName varchar(400),
		depositDate datetime, profileName varchar(100), actualCount int, actualAmount decimal(18,2), rowID int);

	INSERT INTO #tblOpenBatches (batchID)
	SELECT batchID
	FROM dbo.tr_batches
	WHERE orgID = @orgID
	AND statusID <> @postedBatchStatus
	AND ISNULL(batchCode,'') <> 'PENDINGPAYMENTS'
	AND depositDate < @SevenDaysAgo;

	INSERT INTO #tmpAllBatches (batchID, [status], batchName, depositDate, profileName, actualCount, actualAmount, rowID)
	SELECT b.batchID, bs.[status], b.batchName, b.depositDate, mp.profileName, 0, 0, ROW_NUMBER() OVER (ORDER BY b.batchName) AS rowID
	FROM #tblOpenBatches AS tmp
	INNER JOIN dbo.tr_batches AS b ON b.orgID = @orgID AND b.batchID = tmp.batchID
	INNER JOIN dbo.tr_batchStatuses AS bs ON bs.statusID = b.statusID
	LEFT OUTER JOIN dbo.mp_profiles AS mp ON mp.profileID = b.payProfileID;

	SET @totalCount = @@ROWCOUNT;

	EXEC dbo.tr_getBatchActualBulk @orgID=@orgID;
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	SELECT batchID, [status], batchName, actualAmount, depositDate, profileName, @totalCount AS totalCount
	FROM #tmpAllBatches AS tmp
	WHERE rowID <= @limitRowsCount
	ORDER BY rowID;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
