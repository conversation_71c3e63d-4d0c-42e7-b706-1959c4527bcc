ALTER FUNCTION dbo.fn_getMCAssnDataForEnrollment (@depoMemberDataID int, @siteCode varchar(10))
RETURNS @tblMemberData TABLE
(
	memberID int,
	firstname varchar(100),
	lastname varchar(100),
	company varchar(200), 
	membernumber varchar(50),
	email varchar(255), 
	[address] varchar(450), 
	city varchar(100),
	stateID int,
	[state] varchar(100),
	postalCode varchar(25), 
	phone varchar(40)
)
AS
BEGIN
	
	DECLARE @siteID int, @orgID int;
	SELECT @siteID = siteID, @orgID = orgID from membercentral.dbo.sites where sitecode = @siteCode;
	
	insert into @tblMemberData
	select TOP 1 m.memberID, m.firstname, m.lastname, nullif(m.company,'') as company, m.membernumber, nullif(me.email,'') as email, 
		nullif(ltrim(ma.address1 + isnull(' ' + nullif(ma.address2,''),'') + isnull(' ' + nullif(ma.address3,''),'')),'') as [address], 
		nullif(ma.city,'') as city, ma.stateID, ma.stateCode as [state], nullif(ma.postalCode,'') as postalCode, 
		nullif(mp.phone,'') as phone
	from membercentral.dbo.ams_networkProfiles as np
	inner join membercentral.dbo.ams_membernetworkProfiles as mnp on mnp.profileID = np.profileID and mnp.siteID = @siteID
		and mnp.status = 'A'
	inner join membercentral.dbo.ams_members as m on m.orgID = @orgID and m.memberID = mnp.memberID 
		and m.memberID = m.activeMemberID
		and m.status <> 'D'
	inner join membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = m.memberID
	inner join membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
	inner join membercentral.dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = metag.memberID and me.emailTypeID = metag.emailTypeID
	left outer join membercentral.dbo.ams_memberAddressTags as matag 
		inner join membercentral.dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID and matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
		inner join membercentral.dbo.ams_memberAddresses as ma on ma.orgID = @orgID and ma.memberID = matag.memberID and ma.addressTypeID = matag.addressTypeID
		on matag.orgID = @orgID AND matag.memberID = m.memberID
	left outer join membercentral.dbo.ams_memberPhones as mp
		inner join membercentral.dbo.ams_memberPhoneTypes as mpt on mpt.orgID = @orgID 
			and mpt.phoneTypeID = mp.phoneTypeID 
			and mpt.phoneType = 'Phone'
		on mp.orgID = @orgID 
		and mp.memberID = ma.memberID 
		and mp.addressID = ma.addressID
	where np.depomemberdataid = @depoMemberDataID
	and np.status = 'A';

	IF @@ROWCOUNT = 0
		insert into @tblMemberData
		select TOP 1 m.memberID, m.firstname, m.lastname, nullif(m.company,'') as company, m.membernumber, nullif(me.email,'') as email, 
			nullif(ltrim(ma.address1 + isnull(' ' + nullif(ma.address2,''),'') + isnull(' ' + nullif(ma.address3,''),'')),'') as [address], 
			nullif(ma.city,'') as city, ma.stateID, ma.stateCode as [state], nullif(ma.postalCode,'') as postalCode, 
			nullif(mp.phone,'') as phone
		from trialsmith.dbo.depomemberdata as d
		inner join membercentral.dbo.ams_members as m on m.orgID = @orgID and m.memberID = d.MCmemberIDtemp
		inner join membercentral.dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID and m2.status <> 'D'
		inner join membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = m2.memberID
		inner join membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
		inner join membercentral.dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = metag.memberID and me.emailTypeID = metag.emailTypeID
		left outer join membercentral.dbo.ams_memberAddressTags as matag 
			inner join membercentral.dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID and matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
			inner join membercentral.dbo.ams_memberAddresses as ma on ma.orgID = @orgID and ma.memberID = matag.memberID and ma.addressTypeID = matag.addressTypeID
			on matag.orgID = @orgID AND matag.memberID = m2.memberID
		left outer join membercentral.dbo.ams_memberPhones as mp
			inner join membercentral.dbo.ams_memberPhoneTypes as mpt on mpt.orgID = @orgID 
				and mpt.phoneTypeID = mp.phoneTypeID 
				and mpt.phoneType = 'Phone'
			on mp.orgID = @orgID 
			and mp.memberID = ma.memberID
			and mp.addressID = ma.addressID
		where d.depomemberdataid = @depoMemberDataID;
	
	RETURN 
END
GO
