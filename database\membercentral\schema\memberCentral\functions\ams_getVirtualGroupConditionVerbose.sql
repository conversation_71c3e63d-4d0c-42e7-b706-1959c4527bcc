ALTER FUNCTION dbo.ams_getVirtualGroupConditionVerbose (@conditionID int)
RETURNS varchar(max)
AS
BEGIN

	DECLARE @fieldCode varchar(40), @processArea varchar(25), @expression varchar(20), 
		@expressionVerbose varchar(50), @displayTypeCode varchar(20), @dataTypeCode varchar(20), 
		@fieldLabel varchar(max), @verbose varchar(max);
	
	select @fieldCode=c.fieldCode, @processArea=c.processArea, @expression=e.expression,
		@expressionVerbose=e.expressionVerbose, @displayTypeCode=fields.displayTypeCode, 
		@dataTypeCode=fields.dataTypeCode, @fieldLabel=fields.fieldLabel
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
	cross apply dbo.fn_ams_getVirtualGroupConditionVerboseFields(c.orgID,c.fieldCode) as fields
	where c.conditionID = @conditionID;

	IF @fieldCode = 'm_hasmemberphoto' BEGIN
		set @verbose = 'Has Member Photo';
		goto on_done;
	END
	IF @fieldCode = 'ev_entry' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_ev_entry(@conditionID, @expression);
		goto on_done;
	END
	IF @processArea = 'Events' BEGIN
		set @verbose = @expressionVerbose + ' ' + @fieldLabel;
		goto on_done;
	END
	IF @fieldcode in ('acct_allocsum','acct_allocsumrecog') BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_acct_alloc(@conditionID, @expression, @expressionVerbose);
		goto on_done;
	END
	IF @fieldcode = 'acct_balance' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_acct_balance(@conditionID, @expression, @expressionVerbose);
		goto on_done;
	END
	IF @fieldcode = 'acct_cc' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_acct_cc(@conditionID);
		goto on_done;
	END
	IF @fieldcode = 'acct_inv' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_acct_inv(@conditionID);
		goto on_done;
	END
	IF @processArea = 'Record Types' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_rt_role(@conditionID);
		goto on_done;
	END
	IF @processArea = 'Groups' BEGIN
		set @verbose = 'Member of ' + @fieldLabel;
		goto on_done;
	END
	IF @processArea = 'Subscriptions' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_sub_entry(@conditionID);
		goto on_done;
	END
	IF @processArea = 'Member History' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_mh_entry(@conditionID);
		goto on_done;
	END
	IF @processArea = 'Relationships' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_rel_entry(@conditionID);
		goto on_done;
	END
	IF @processArea = 'Notes' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_mn_entry(@conditionID);
		goto on_done;
	END
	IF @processArea = 'Tasks' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_task_entry(@conditionID);
		goto on_done;
	END
	IF @processArea = 'Districting' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_districting(@conditionID, @fieldLabel, @expressionVerbose);
		goto on_done;
	END
	IF @expression in ('exists','not_exists') BEGIN
		set @verbose = @fieldLabel + ' ' + @expressionVerbose;
		goto on_done;
	END
	IF @fieldCode = 'm_membertypeid' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_m_membertypeid(@conditionID, @fieldLabel, @expressionVerbose);
		goto on_done;
	END
	IF @fieldCode = 'm_status' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_m_status(@conditionID, @fieldLabel, @expressionVerbose);
		goto on_done;
	END
	IF @processArea = 'Addresses' and right(@fieldCode,10) = '_stateprov' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_stateprov(@conditionID, @fieldLabel, @expressionVerbose);
		goto on_done;
	END
	IF @processArea = 'Addresses' and right(@fieldCode,8) = '_country' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_country(@conditionID, @fieldLabel, @expressionVerbose);
		goto on_done;
	END
	IF @processArea = 'Addresses' and @expression = 'istaggedwith' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_ma_tagged(@conditionID, @fieldLabel);
		goto on_done;
	END
	IF @expression = 'isanniversary' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_isanniversary(@conditionID, @fieldLabel, @expressionVerbose);
		goto on_done;
	END
	IF @processArea = 'Professional Licenses' and right(@fieldCode,7) = '_status' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_mpl_status(@conditionID, @fieldLabel, @expressionVerbose);
		goto on_done;
	END
	IF @expression = 'datediff' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_datediff(@conditionID, @fieldLabel);
		goto on_done;
	END
	IF @expression = 'datepart' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_datepart(@conditionID, @fieldLabel);
		goto on_done;
	END
	IF @processArea = 'Custom Fields' and @displayTypeCode in ('SELECT','RADIO') and @dataTypeCode = 'BIT' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_md_bit(@conditionID, @fieldLabel, @expressionVerbose);
		goto on_done;
	END
	IF @processArea = 'Custom Fields' and @displayTypeCode in ('SELECT','RADIO','CHECKBOX') BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_md_select(@conditionID, @fieldLabel, @expressionVerbose, @dataTypeCode);
		goto on_done;
	END
	IF @processArea = 'Listserver Memberships' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_lists(@conditionID);
		goto on_done;
	END	
	IF @fieldCode = 'cl_entry' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_cl_entry(@conditionID);
		goto on_done;
	END
	IF @fieldcode = 'cp_entry' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_cp_entry(@conditionID);
		goto on_done;
	END
	IF @fieldcode = 'cp_valuesum' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_cp_valuesum(@conditionID, @expression, @expressionVerbose);
		goto on_done;
	END
	IF @processArea = 'Emails' and @expression = 'istaggedwith' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_me_tagged(@conditionID, @fieldLabel);
		goto on_done;
	END
	IF @fieldcode = 'ref_entry' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_ref_entry(@conditionID);
		goto on_done;
	END
	IF @fieldCode = 'sup_entry' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_sup_entry(@conditionID);
		goto on_done;
	END
	IF @fieldCode = 'sw_entry' BEGIN
		set @verbose = dbo.ams_getVirtualGroupConditionVerbose_sw_entry(@conditionID);
		goto on_done;
	END

	-- All others
	SELECT TOP 1 @verbose = @fieldLabel + ' ' + @expressionVerbose + ' ' + STRING_AGG(cv.conditionValue + case when af.AFID is not null then ' (' + af.afName + ')' else '' end, ' OR ') WITHIN GROUP (ORDER BY cv.conditionValue ASC)
	FROM dbo.ams_virtualGroupConditions as c
	INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
	INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
	LEFT OUTER JOIN dbo.af_advanceFormulas as af on af.AFID = cv.AFID
	WHERE c.conditionID = @conditionID
	AND cvk.conditionKey = 'value';

	on_done:
	RETURN @verbose;
END
GO
