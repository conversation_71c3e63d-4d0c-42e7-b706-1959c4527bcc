ALTER PROC dbo.queue_chatDocumentPrep_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @environmentName varchar(50), @environmentID int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'chatDocumentPrep';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpQueueItems
	FROM dbo.queue_chatDocumentPrep AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_chatDocumentPrep
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT DISTINCT qid.itemID, qid.caseID, qid.caseExpertID, c.caseReference, e.first_name + ' ' + e.last_name AS expertName,
		d.depomemberdataID, d.firstName + ' ' + d.lastName AS memberName, d.Email AS memberEmail
	FROM #tmpQueueItems as tmp
	INNER JOIN dbo.queue_chatDocumentPrep as qid ON qid.itemID = tmp.itemID
	INNER JOIN trialsmith.dbo.depomemberdataCases AS c ON c.caseID = qid.caseID
    INNER JOIN trialsmith.dbo.depomemberdataCaseExperts AS e ON e.caseExpertID = qid.caseExpertID
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = c.depomemberdataID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
