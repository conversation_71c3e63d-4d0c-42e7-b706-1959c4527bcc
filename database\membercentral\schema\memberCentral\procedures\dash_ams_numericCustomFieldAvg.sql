ALTER PROC dbo.dash_ams_numericCustomFieldAvg
@siteID int,
@groupSetID int,
@columnID int,
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpGroupCustomFieldAvg') IS NOT NULL
		DROP TABLE #tmpGroupCustomFieldAvg;
	CREATE TABLE #tmpGroupCustomFieldAvg (groupName varchar(115), avgIntCount decimal(14,1), avgDecCount decimal(14,2));

	DECLARE @orgID int, @dataTypeCode varchar(20), @tabledatacountXML xml, @tabledataXML xml;
	SET @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SELECT @dataTypeCode = mdcdt.dataTypeCode
	FROM dbo.ams_memberDataColumns as mdc
	INNER JOIN dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
	WHERE mdc.orgID = @orgID
	AND mdc.columnID = @columnID;
	
	IF @dataTypeCode = 'INTEGER' BEGIN
		INSERT INTO #tmpGroupCustomFieldAvg (groupName, avgIntCount)
		SELECT ISNULL(mgsg.labelOverride,g.groupName), AVG(cast(mdcv.columnValueInteger as decimal(14,1)))
		FROM dbo.ams_memberGroupSetGroups AS mgsg
		INNER JOIN dbo.ams_memberGroupSets AS mgs ON mgs.orgID = @orgID
			AND mgsg.groupSetID = mgs.groupSetID
			AND mgs.groupSetID = @groupSetID
		INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID 
			AND g.groupID = mgsg.groupID
		LEFT OUTER JOIN dbo.cache_members_groups AS mg
			INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = mg.memberID
			INNER JOIN dbo.ams_memberData AS md ON md.memberID = m.memberID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = @columnID
				AND mdcv.valueID = md.valueID
				AND mdcv.columnValueInteger IS NOT NULL
			ON mg.orgID = @orgID and mg.groupID = g.groupID
		GROUP BY mgsg.labelOverride, g.groupName;

		SELECT @tabledataXML = (
			SELECT datarow.groupname, datarow.avgIntCount as [avgcount]
			FROM #tmpGroupCustomFieldAvg as datarow
			ORDER BY datarow.groupname
			FOR XML AUTO, ELEMENTS
		);
	END ELSE BEGIN
		INSERT INTO #tmpGroupCustomFieldAvg (groupName, avgDecCount)
		SELECT ISNULL(mgsg.labelOverride,g.groupName), AVG(mdcv.columnValueDecimal2)
		FROM dbo.ams_memberGroupSetGroups AS mgsg
		INNER JOIN dbo.ams_memberGroupSets AS mgs ON mgs.orgID = @orgID
			AND mgsg.groupSetID = mgs.groupSetID
			AND mgs.groupSetID = @groupSetID
		INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID 
			AND g.groupID = mgsg.groupID
		LEFT OUTER JOIN dbo.cache_members_groups AS mg
			INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = mg.memberID
			INNER JOIN dbo.ams_memberData AS md ON md.memberID = m.memberID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = @columnID
				AND mdcv.valueID = md.valueID
				AND mdcv.columnValueDecimal2 IS NOT NULL
			ON mg.orgID = @orgID and mg.groupID = g.groupID
		GROUP BY mgsg.labelOverride, g.groupName;

		SELECT @tabledataXML = (
			SELECT datarow.groupname, datarow.avgDecCount as [avgcount]
			FROM #tmpGroupCustomFieldAvg as datarow
			ORDER BY datarow.groupname
			FOR XML AUTO, ELEMENTS
		);
	END

	-- we do this because if there are no rows to display, we need to not just return an empty data node so we can tell the difference between no rows and no cache.
	SELECT @tabledatacountXML = ISNULL((
		SELECT count(*) AS datarowcount
		FROM #tmpGroupCustomFieldAvg
		FOR XML RAW(''), ELEMENTS
	),'<reportdata/>');

	SELECT @xmlDataset = ISNULL((
		select @tabledatacountXML, @tabledataXML for xml path('data')
	),'<data/>');

	SET @xmlDataset.modify('insert (attribute dt {sql:variable("@dataTypeCode")}) into (/data)[1]');

	IF OBJECT_ID('tempdb..#tmpGroupCustomFieldAvg') IS NOT NULL
		DROP TABLE #tmpGroupCustomFieldAvg;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
