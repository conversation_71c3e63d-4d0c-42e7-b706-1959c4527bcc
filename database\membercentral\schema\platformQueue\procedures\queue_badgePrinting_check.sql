ALTER PROC dbo.queue_badgePrinting_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @grabProcessingStatusID int, @readyStatusID int, 
		@processingStatusID int, @errorSubject varchar(400), @errorTitle varchar(400);
	EXEC dbo.queue_getQueueTypeID @queueType='badgePrinting', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- badgePrinting / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_badgePrinting WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_badgePrinting
		SET statusID = @readyStatusID,
			dateUpdated = GETDATE()
		WHERE statusID = @grabProcessingStatusID
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Badge Printing Queue', @engine='MCLuceeLinux';

		SET @errorTitle = 'badgePrinting Queue Issue';
		SET @errorSubject = 'badgePrinting queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- badgePrinting / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_badgePrinting WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_badgePrinting
		SET statusID = @readyStatusID,
			dateUpdated = GETDATE()
		WHERE statusID = @processingStatusID
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Badge Printing Queue', @engine='MCLuceeLinux';

		SET @errorTitle = 'badgePrinting Queue Issue';
		SET @errorSubject = 'badgePrinting queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- badgePrinting catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_badgePrinting WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'badgePrinting Queue Issue';
		SET @errorSubject = 'badgePrinting queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
