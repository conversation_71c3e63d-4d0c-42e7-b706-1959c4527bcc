ALTER PROC dbo.swod_copySponsorWithGroupings
@siteID int,
@copyFromSeminarID int,
@copyToSeminarID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @sponsorsUsageID int;

	-- Create temp table to store the mapping of old to new sponsor grouping IDs
	IF OBJECT_ID('tempdb..#tmpSponsorGroupingMapping') IS NOT NULL
		DROP TABLE #tmpSponsorGroupingMapping;
	CREATE TABLE #tmpSponsorGroupingMapping (
		oldSponsorGroupingID int,
		newSponsorGroupingID int,
		sponsorGrouping varchar(200),
		sponsorGroupingOrder int
	);

	BEGIN TRAN;
		
		-- Copy sponsor groupings from source to destination seminar
		INSERT INTO membercentral.dbo.sponsorsGrouping (sponsorGrouping, sponsorGroupingOrder, siteID, referenceType, referenceID)
		OUTPUT inserted.sponsorGroupingID, 0, inserted.sponsorGrouping, inserted.sponsorGroupingOrder
			INTO #tmpSponsorGroupingMapping (newSponsorGroupingID, oldSponsorGroupingID, sponsorGrouping, sponsorGroupingOrder)
		SELECT sponsorGrouping, sponsorGroupingOrder, siteID, referenceType, @copyToSeminarID
		FROM membercentral.dbo.sponsorsGrouping
		WHERE siteID = @siteID
		AND referenceType = 'swodProgram'
		AND referenceID = @copyFromSeminarID;

		-- Update the mapping with the original sponsor grouping IDs
		UPDATE tmp
		SET tmp.oldSponsorGroupingID = orig.sponsorGroupingID
		FROM #tmpSponsorGroupingMapping tmp
		INNER JOIN membercentral.dbo.sponsorsGrouping orig ON orig.siteID = @siteID
			AND orig.referenceID = @copyFromSeminarID
			AND orig.referenceType = 'swodProgram'
			AND orig.sponsorGrouping = tmp.sponsorGrouping
			AND orig.sponsorGroupingOrder = tmp.sponsorGroupingOrder;

		-- Copy sponsors with their grouping associations
		DECLARE @sponsorID int, @oldSponsorGroupingID int, @newSponsorGroupingID int;
		DECLARE sponsor_cursor CURSOR FOR
			SELECT DISTINCT su.sponsorID, sug.sponsorGroupingID
			FROM membercentral.dbo.sponsorsUsage su
			LEFT OUTER JOIN membercentral.dbo.sponsorsUsageGrouping sug ON sug.sponsorUsageID = su.sponsorUsageID
			INNER JOIN membercentral.dbo.sponsors s ON s.sponsorID = su.sponsorID AND s.siteID = @siteID
			WHERE su.referenceType = 'swodProgram'
			AND su.referenceID = @copyFromSeminarID;

		OPEN sponsor_cursor;
		FETCH NEXT FROM sponsor_cursor INTO @sponsorID, @oldSponsorGroupingID;

		WHILE @@FETCH_STATUS = 0
		BEGIN
			-- Map old sponsor grouping ID to new one (NULL if no grouping)
			SET @newSponsorGroupingID = NULL;
			IF @oldSponsorGroupingID IS NOT NULL
			BEGIN
				SELECT @newSponsorGroupingID = newSponsorGroupingID
				FROM #tmpSponsorGroupingMapping
				WHERE oldSponsorGroupingID = @oldSponsorGroupingID;
			END

			EXEC membercentral.dbo.sponsors_associateSponsor
				@siteID = @siteID,
				@sponsorID = @sponsorID,
				@referenceType = 'swodProgram',
				@referenceID = @copyToSeminarID,
				@recordedByMemberID = @recordedByMemberID,
				@sponsorGroupingID = @newSponsorGroupingID,
				@sponsorsUsageID=@sponsorsUsageID OUTPUT;

			FETCH NEXT FROM sponsor_cursor INTO @sponsorID, @oldSponsorGroupingID;
		END;

		CLOSE sponsor_cursor;
		DEALLOCATE sponsor_cursor;

	COMMIT TRAN;

	-- Clean up temp table
	IF OBJECT_ID('tempdb..#tmpSponsorGroupingMapping') IS NOT NULL
		DROP TABLE #tmpSponsorGroupingMapping;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
