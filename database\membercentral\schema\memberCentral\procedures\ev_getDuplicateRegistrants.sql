ALTER PROC dbo.ev_getDuplicateRegistrants
@orgID int,
@siteID int,
@limitRowsCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @totalCount int;

	IF OBJECT_ID('tempdb..#tmpEVDupes') IS NOT NULL
		DROP TABLE #tmpEVDupes;
	CREATE TABLE #tmpEVDupes (activeMemberID int, eventID int, siteID int, eventcontentID int);

	INSERT INTO #tmpEVDupes (activeMemberID, eventID, siteID, eventcontentID)
	SELECT m.activeMemberID, e.eventID, e.siteID, e.eventcontentID
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_registration AS reg ON e.siteID = @siteID
		AND reg.siteID = @siteID
		AND e.[status] = 'A'
		AND e.eventID = reg.eventID
		AND reg.[status] = 'A'
	INNER JOIN dbo.ev_registrants AS r ON r.recordedOnSiteID = @siteID
		AND r.[status] = 'A'
		AND r.registrationID = reg.registrationID
	INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
		AND m.memberid = r.memberID
	GROUP BY m.activeMemberID, e.eventID, e.siteID, e.eventcontentID
	HAVING COUNT(DISTINCT r.registrantID) > 1;

	SET @totalCount = @@ROWCOUNT;

	WITH orderedRows AS (
		SELECT m.memberID, m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company,
			CONVERT(varchar(10),et.startTime,101) + ' - ' + cl.contentTitle AS eventName,
			ROW_NUMBER() OVER(ORDER BY m.lastname, m.firstname, m.membernumber, et.startTime, cl.contentTitle) AS rowID
		FROM #tmpEVDupes AS tmp
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
			AND m.memberid = tmp.activeMemberID
		INNER JOIN dbo.sites AS s ON s.siteID = tmp.siteID
		INNER JOIN dbo.ev_times AS et ON et.eventID = tmp.eventID
			AND et.timeZoneID = s.defaultTimeZoneID
		INNER JOIN dbo.cms_contentLanguages AS cl ON cl.siteID = @siteID
			AND cl.contentID = tmp.eventcontentID
			AND cl.languageID = 1
	)
	SELECT memberID, memberName, company, eventName, @totalCount AS totalCount
	FROM orderedRows
	WHERE rowID <= @limitRowsCount
	ORDER BY rowID;

	IF OBJECT_ID('tempdb..#tmpEVDupes') IS NOT NULL
		DROP TABLE #tmpEVDupes;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
