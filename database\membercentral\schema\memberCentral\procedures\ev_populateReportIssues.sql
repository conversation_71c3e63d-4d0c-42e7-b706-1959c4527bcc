ALTER PROC dbo.ev_populateReportIssues
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @xmlData xml;
	
	IF OBJECT_ID('tempdb..#tblSites') IS NOT NULL 
		DROP TABLE #tblSites;
	CREATE TABLE #tblSites (siteID int PRIMARY KEY);

	INSERT INTO #tblSites (siteID)
	SELECT DISTINCT s.siteID
	FROM dbo.sites as s
	INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID 
		AND sr.siteResourceID = s.siteResourceID
		AND sr.siteResourceStatusID = 1
	INNER JOIN dbo.ev_calendars as c on c.siteID = s.siteID;

	SELECT @itemCount = @@ROWCOUNT;

	SELECT @siteID = MIN(siteID) FROM #tblSites;
	WHILE @siteID IS NOT NULL BEGIN
		SET @xmlData = NULL;

		SELECT @xmlData = ISNULL((
			SELECT @siteID as s
			FOR XML RAW('mc'), TYPE
		),'<mc/>');

		EXEC platformQueue.dbo.queue_EventIssues_sendMessage @xmlMessage=@xmlData;
		
		SELECT @siteID = MIN(siteID) FROM #tblSites WHERE siteID > @siteID;
	END

	IF OBJECT_ID('tempdb..#tblSites') IS NOT NULL 
		DROP TABLE #tblSites;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
