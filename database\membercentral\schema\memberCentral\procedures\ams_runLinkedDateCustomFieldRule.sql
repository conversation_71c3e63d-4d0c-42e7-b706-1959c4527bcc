ALTER PROC dbo.ams_runLinkedDateCustomFieldRule
@orgID int,
@memberID int,
@columnID int,
@recordedByMemberID int,
@byPassQueue bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @activeMemberID int, @linkedDateColumnID int, @linkedDateCompareDate date, @existingColValDate date, 
		@existingNumberofYears int, @numberofYears int, @columnName varchar(128);
	SET @activeMemberID = dbo.fn_getActiveMemberID(@memberID);

	SELECT @columnName = columnName, @linkedDateColumnID = linkedDateColumnID, @linkedDateCompareDate = linkedDateCompareDate
	FROM dbo.ams_memberDataColumns
	WHERE orgID = @orgID
	AND columnID = @columnID;

	IF @linkedDateColumnID IS NULL
		RAISERROR('Date column not specified.',16,1);

	-- update member
	-- when run for a single MemberID, this MUST be called from the context of PMI (via the ams_importMemberFromQueue)
	IF @memberID IS NOT NULL BEGIN
		SELECT @existingColValDate = mdcv.columnValueDate
		FROM dbo.ams_memberData as md 
		INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
		INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID 
			AND mdc.columnID = @linkedDateColumnID
		WHERE md.memberID = @activeMemberID;

		SELECT @existingNumberofYears = mdcv.columnValueInteger
		FROM dbo.ams_memberData as md 
		INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
		INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID 
			AND mdc.columnID = @columnID
		WHERE md.memberID = @activeMemberID;

		/* DateDiff calculation - Taken from: https://dba.stackexchange.com/a/108079 */
		SELECT @numberofYears = (CAST(CONVERT(char(8), @linkedDateCompareDate, 112) AS int) - CAST(CONVERT(char(8), @existingColValDate, 112) AS int)) / 10000 

		IF ISNULL(@numberofYears,0) <> ISNULL(@existingNumberofYears,0)
			EXEC dbo.ams_setMemberData @memberID=@activeMemberID, @orgID=@orgID, @columnName=@columnName, 
				@columnValue=@numberofYears, @recordedByMemberID=@recordedByMemberID, @byPassQueue=1;
		
		-- reprocess any applicable conditions without rolling up error
		IF @byPassQueue = 0 BEGIN			
			BEGIN TRY
				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
				CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
				SELECT @orgID, @memberID, c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'md_' + cast(@columnID as varchar(10));

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
			END TRY
			BEGIN CATCH
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		END ELSE BEGIN
			IF OBJECT_ID('tempdb..#tmpMCGConditions') IS NOT NULL BEGIN
				INSERT INTO #tmpMCGConditions (conditionID)
				SELECT distinct c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'md_' + cast(@columnID as varchar(10));
			END
		END
	END
	-- use PMI to mass update members
	ELSE BEGIN
		IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
			DROP TABLE #mc_PartialMemImport;
		CREATE TABLE #mc_PartialMemImport (membernumber varchar(50));

		DECLARE @sql varchar(max), @importResult xml, @errCount int, @environmentName varchar(50);
		SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

		EXEC ('ALTER TABLE #mc_PartialMemImport ADD [' + @columnName + '] int, rowID int;');

		set @sql = 'select memberNumber, numberofYears, ROW_NUMBER() over (order by memberNumber)
			from (
				select m.memberNumber, ((CAST(CONVERT(char(8), c.linkedDateCompareDate, 112) AS int) - CAST(CONVERT(char(8), mdcv_ldt.columnValueDate, 112) AS int)) / 10000) as numberofYears, mdcv.columnValueInteger as existingNumberofyears
				from dbo.ams_members as m
				left outer join dbo.ams_memberData as md 
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					inner join dbo.ams_memberDataColumns as mdc on mdc.orgID = ' + cast(@orgID as varchar(4)) + ' 
						and mdc.columnID = mdcv.columnID 
						and mdc.columnID = ' + cast(@columnID as varchar(10)) + '
					on md.memberid = m.memberID
				left outer join dbo.ams_memberData as md_ldt
					inner join dbo.ams_memberDataColumnValues as mdcv_ldt on mdcv_ldt.valueID = md_ldt.valueID
					inner join dbo.ams_memberDataColumns as mdc_ldt on mdc_ldt.orgID = ' + cast(@orgID as varchar(4)) + ' 
						and mdc_ldt.columnID = mdcv_ldt.columnID 
						and mdc_ldt.columnID = ' + cast(@linkedDateColumnID as varchar(10)) + '
					on md_ldt.memberid = m.memberID
				inner join dbo.ams_memberDataColumns as c on c.orgID = ' + cast(@orgID as varchar(4)) + ' 
					and c.columnID = ' + cast(@columnID as varchar(10)) + '
				where m.orgID = ' + cast(@orgID as varchar(4)) + ' 
				and m.memberid = m.activememberID 
				and m.status in (''A'',''I'')
			) as tmp
			where isnull(numberofYears,0) <> isnull(existingNumberofyears,0)';

		INSERT INTO #mc_PartialMemImport
		EXEC(@sql);

		IF EXISTS (select 1 from #mc_PartialMemImport) BEGIN
			EXEC membercentral.dbo.ams_importPartialMemberData_autoconfirm @orgID=@orgID, @importTitle='Manual Partial Update', 
				@runByMemberID=@recordedByMemberID, @activateIncMembers=0, @inactivateNonIncMembers=0, @thresholdLimit=0, 
				@bypassRO=1, @finalMSGHeader='MemberCentral automatically', @emailSubject='Linked Date Column Rule Run Report',
				@environmentName=@environmentName, @importResult=@importResult OUTPUT, @errCount=@errCount OUTPUT;

			IF @errCount > 0
				RAISERROR('Error running partial member update.',16,1);
		END

		IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
			DROP TABLE #mc_PartialMemImport;
	END
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
