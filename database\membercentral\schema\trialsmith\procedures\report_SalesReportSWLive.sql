ALTER PROC dbo.report_SalesReportSWLive
@BeginDate smalldatetime,
@EndDate smalldatetime

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @tblNATLEsem TABLE (seminarID int PRIMARY KEY);
	insert into @tblNATLEsem (seminarID)
	select seminarID
	from seminarweb.dbo.tblSeminarsSWLive
	where isNATLE = 1;

	-- report_GrossRevenues, report_NetRevenueReceipts, report_Refunds
	select 
		sum(case when dt.AmountBilled > 0 AND natsem.seminarID is not null then isnull(dt.AmountBilled,0) else 0 end) as AmountBilledNATLE,
		sum(case when dt.AmountBilled > 0 AND natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebBundle' then isnull(dt.AmountBilled,0) else 0 end) as AmountBilledBundles,
		sum(case when dt.AmountBilled > 0 AND natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebLive' then isnull(dt.AmountBilled,0) else 0 end) as AmountBilledNonNATLE,
		sum(case when dt.AmountBilled > 0 AND natsem.seminarID is not null then isnull(dt.SalesTaxAmount,0) else 0 end) as SalesTaxAmountNATLE,
		sum(case when dt.AmountBilled > 0 AND natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebBundle' then isnull(dt.SalesTaxAmount,0) else 0 end) as SalesTaxAmountBundles,
		sum(case when dt.AmountBilled > 0 AND natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebLive' then isnull(dt.SalesTaxAmount,0) else 0 end) as SalesTaxAmountNonNATLE,
		sum(case when dt.AmountBilled < 0 AND natsem.seminarID is not null then isnull(dt.AmountBilled,0) else 0 end) as REFAmountBilledNATLE,
		sum(case when dt.AmountBilled < 0 AND natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebBundle' then isnull(dt.AmountBilled,0) else 0 end) as REFAmountBilledBundles,
		sum(case when dt.AmountBilled < 0 AND natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebLive' then isnull(dt.AmountBilled,0) else 0 end) as REFAmountBilledNonNATLE,
		sum(case when dt.AmountBilled < 0 AND natsem.seminarID is not null then isnull(dt.SalesTaxAmount,0) else 0 end) as REFSalesTaxAmountNATLE,
		sum(case when dt.AmountBilled < 0 AND natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebBundle' then isnull(dt.SalesTaxAmount,0) else 0 end) as REFSalesTaxAmountBundles,
		sum(case when dt.AmountBilled < 0 AND natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebLive' then isnull(dt.SalesTaxAmount,0) else 0 end) as REFSalesTaxAmountNonNATLE,
		sum(case when natsem.seminarID is not null then isnull(dt.AmountBilled,0) else 0 end) as NETAmountBilledNATLE,
		sum(case when natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebBundle' then isnull(dt.AmountBilled,0) else 0 end) as NETAmountBilledBundles,
		sum(case when natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebLive' then isnull(dt.AmountBilled,0) else 0 end) as NETAmountBilledNonNATLE,
		sum(case when natsem.seminarID is not null then isnull(dt.SalesTaxAmount,0) else 0 end) as NETSalesTaxAmountNATLE,
		sum(case when natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebBundle' then isnull(dt.SalesTaxAmount,0) else 0 end) as NETSalesTaxAmountBundles,
		sum(case when natsem.seminarID is null and dt.purchasedItemTableName = 'SeminarWebLive' then isnull(dt.SalesTaxAmount,0) else 0 end) as NETSalesTaxAmountNonNATLE
	from dbo.depoTransactions as dt
	left outer join @tblNATLEsem as natsem on natsem.seminarID = dt.purchasedItemID and dt.purchasedItemTableName = 'SeminarWebLive'
	where dt.DatePurchased between @BeginDate and @EndDate
	and dt.accountCode = '7001';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
