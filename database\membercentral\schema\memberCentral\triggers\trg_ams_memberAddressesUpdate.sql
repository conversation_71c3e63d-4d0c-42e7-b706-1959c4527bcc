-- trigger to update postal<PERSON>ode<PERSON><PERSON><PERSON><PERSON><PERSON>
CREATE TRIGGER [dbo].[trg_ams_memberAddressesUpdate] ON [dbo].[ams_memberAddresses]
AFTER UPDATE
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN

	IF UPDATE(postalCode) BEGIN
	    UPDATE ma
	    SET ma.postalCodeForSearch = CASE 
			WHEN ma.countryID = 1 then left(replace(ma.postalCode,' ',''),5)
			ELSE replace(ma.postalCode,' ','')
			END
		FROM dbo.ams_memberAddresses as ma 
		INNER JOIN Inserted as I ON ma.addressID = I.addressID
		INNER JOIN Deleted as D ON ma.addressID = D.addressID
		WHERE D.postalCode <> I.postalCode
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
ALTER TABLE [dbo].[ams_memberAddresses] ENABLE TRIGGER [trg_ams_memberAddressesUpdate]
GO
