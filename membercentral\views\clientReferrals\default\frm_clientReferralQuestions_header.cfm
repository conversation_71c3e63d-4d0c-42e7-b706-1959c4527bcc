<cfset local.strData = attributes.data>
<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.pageHead">
	<cfoutput>
	<style>
		.telField{
			padding-top: 14px !important;
			padding-bottom: 14px !important;
		}
	</style>
	<script type="text/javascript" src="/assets/common/javascript/resourceFields.js#local.assetCachingKey#"></script>
	<script type="text/javascript">
		var nameRegex = /^([a-zA-Z]{1,})+([a-zA-Z0-9'\.\-\s]{0,1})+([a-zA-Z]{1,})$/;
		var initialRegex = /^([a-zA-Z]{1,})+([a-zA-Z0-9'\.\-\s]{0,1})+([a-zA-Z]{0,1})$/;
		var emailRegex = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z]{2,4})+$/;
		var phoneRegex = /^(1\s*[-\/\.]?)?(\((\d{3})\)|(\d{3}))\s*([\s-./\\])?([0-9]*)([\s-./\\])?([0-9]*)$/;
		var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
		var ref_hideRepFieldsFE = #local.strData.referralSettingsQry.hideRepFieldsFE#;
		
		function handleEnter(e) {
			var keycode = (e.keyCode ? e.keyCode : e.which);;
			if (keycode == '13') {
				if(document.body === document.activeElement) {
					if($('button.btn:visible').length > 1){
						$('button.btn.btn-primary:visible').trigger('click');
					} else if($('button.btn:visible').length == 1){
						$('button.btn:visible').trigger('click');
					}
				} else {
					e.preventDefault();
					document.activeElement.click();
					document.activeElement.setAttribute("aria-checked","true");
				}
			}
		}
		<cfif val(local.strData.referralSettingsQry.feLegalDescLimitWords)>
			function updateLegalDescWordCount() {
				const legalDesc = $('##issueDesc').val().trim();
				const legalDescWordLimit = #int(val(local.strData.referralSettingsQry.feLegalDescLimitWordCount))#;
				const legalDescWords = legalDesc.split(/\s+/).filter(Boolean); /* Filter out empty strings*/
				const legalDescWordCount = legalDescWords.length;
				$('##legalDescWordcount').text(legalDescWordCount);

				/* Check if word count exceeds limit */
				if (legalDescWordCount > legalDescWordLimit) {
					$('##legalDescWordLimitExceedAlert').show();
					return false;
				} else {
					$('##legalDescWordLimitExceedAlert').hide();
				}
			}
		</cfif>

		function referralQuestionsMessageHandler(event) {
			if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
				
				switch (event.data.messagetype.toLowerCase()) {
					case "mcpaymentformloadevent":
						var iframeWin = document.querySelector('iframe[name=iframeManageForm'+event.data.profileid+']').contentWindow;
						var message = { success:true, 
										messagetype:"MCFrontEndPaymentEvent", 
										paymentbuttonname:$('##clientSaleBtn').text(), 
										profileid:event.data.profileid };
						iframeWin.postMessage(message, event.origin);
						break;

					case "mcgatewayevent":
						if (['cardadded','cardupdated'].indexOf(event.data.eventname.toLowerCase()) != -1 && event.data.payprofileid) {
							$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.profileid+'_mppid' }).val(event.data.payprofileid).appendTo('form##frmClient');
							$('##divInputFormWrapper'+event.data.profileid).attr("pofcount",1);
							$('##ccInputForm').html('');
							setClientRefPaymentProcessingFeesField(event.data);
							$('##clientSaleBtn').trigger('click');
							showFormSubmitting();
						} else if (['applepaytokenready','gpaytokenready'].indexOf(event.data.eventname.toLowerCase()) != -1) {
							$('##ccInputForm').html('');
							setClientRefPaymentTokenData(event.data.tokendata,event.data.profileid);
							setClientRefPaymentProcessingFeesField(event.data);
							$('##clientSaleBtn').trigger('click');
							showFormSubmitting();
						}
						break;
				};

			} else {
				return false;
			}
		}
		function setClientRefPaymentProcessingFeesField(obj) {
			if (typeof obj.enableprocessingfee != "undefined") {
				let processFeeDonationElement = $('##processFeeDonation'+obj.profileid);
				if (processFeeDonationElement.length) {
					processFeeDonationElement.val(obj.enableprocessingfee);
				} else {
					$('<input>').attr({ type: 'hidden', name: 'processFeeDonation'+obj.profileid, id: 'processFeeDonation'+obj.profileid }).val(obj.enableprocessingfee).appendTo('form##frmClient');
				}
			}
		}
		function setClientRefPaymentTokenData(tokendata,profileid) {
			let tokenDataElement = $('##p_'+profileid+'_tokenData');
			if (tokenDataElement.length) {
				tokenDataElement.val(JSON.stringify(tokendata));
			} else {
				$('<input>').attr({ type: 'hidden', name: 'p_'+profileid+'_tokenData', id: 'p_'+profileid+'_tokenData' }).val(JSON.stringify(tokendata)).appendTo('form##frmClient');
			}
		}
		function showFormSubmitting() {
			$('##formSubmitting').show();
			$('html,body').animate({scrollTop: $('##formSubmitting').offset().top},100);
		}
		
		$(function() {
			$("body").attr('onkeypress','handleEnter(event)');

			<cfif local.strData.showContactFormFirst>
				_html = $('##contactInfo-primary')[0].outerHTML + $('##isThisRep')[0].outerHTML ;
				$('##contactInfo-primary,##isThisRep').remove();
				$('##contactInfoLocation').html(_html);
			</cfif>

			<cfif val(local.strData.ccError)>
				showPaymentScreenWhenError();
			</cfif>
			
			$('.btnContinuePanelSubpanel').click(function(){
				if($('##panelID1').val() == 0){
					showAlert('panelSubpanelIssueErr','Primary Panel is required to continue.');
					scrollDown();
				} else {
					goToViewMode('panelSubpanel');
					checkNshowIssueDesc();
					$('.btnContinuePanelSubpanel').hide();
				}
			});

			<cfif val(local.strData.referralSettingsQry.feLegalDescLimitWords)>
				$('##issueDesc').on("input", updateLegalDescWordCount);
				updateLegalDescWordCount();
			</cfif>

			$('.btnContinueLegalIssue').click(function(){
				var legalDesc = $('##issueDesc').val().trim();
				if(legalDesc.length > 0){
					<cfif val(local.strData.referralSettingsQry.feLegalDescLimitWords)>
						updateLegalDescWordCount();
						const legalDescWordLimit = #int(val(local.strData.referralSettingsQry.feLegalDescLimitWordCount))#;
						/* Check if word count exceeds limit */
						if (parseInt($('##legalDescWordcount').text()) > legalDescWordLimit) return false;
					</cfif>

					goToViewMode('legalIssue');

					if($('##additionalFilters').length) {
						$('.additionalFilters').slideDown();
						goToEditMode('additionalFilters');
						additionalFiltersMultiSelectInit();
						scrollDown('additionalFilters');
					} else {
						showNextStepButton();
						scrollDown('step2btnWrapper');
					}
				} else {
					if(#local.strData.dspLegalDescription#){
						showAlert('legalIssueErr','The \'description of the legal issue\' is required to continue.');
						scrollDown('legalIssue');
					} else {
						$('.issueDescTextlabel').html("No legal description entered.");
						goToViewMode('legalIssue');
						if($('##additionalFilters').length) {
							$('.additionalFilters').slideDown();
							goToEditMode('additionalFilters');
						} else {
							showNextStepButton();
							scrollDown('step2btnWrapper');
						}
						scrollDown();
					}
				}
			});
			
			$('.btnLegalIssueFilterSkip').click(function(){
				goToViewMode('legalIssue');
				$('.additionalFilters').slideDown();
				goToEditMode('additionalFilters');
				scrollDown('additionalFilters');
				
			});
			
			$('.btnContinueAddFilter').click(function(){
				var _contentHtml = "";
				var errMsg = validateAdditionalFilters();

				if(errMsg.length == 0){
					initAlert('additionalFiltersErr');
					goToViewMode('additionalFilters');
					showNextStepButton();
					scrollDown('step2btnWrapper');
				} else{
					showAlert('additionalFiltersErr',errMsg);
					scrollUp();
				}
			});
			$('.btnAdditionalFilterSkip').click(function(){
				initAlert('additionalFiltersErr');
				goToViewMode('additionalFilters');
				showNextStepButton();
				scrollDown('step2btnWrapper');
			});
			$('.btnStep2').click(function(){
				/*validation for additional fields here*/
				$('##step1form').hide();
				$('##formSubmitting').show();
				$('html,body').animate({scrollTop: $('##formSubmitting').offset().top},100);

				$.ajax({
					type: "POST",
					url: '/?event=cms.showResource&resID=#local.strData.siteResourceID#&ra=search&saveClientBtn=1&mode=stream',
					success: showStep2,
					dataType: 'json',
					data : $("##frmClient").serialize()
				});
			});
			$('.btnReviewSubmit').click(function(){
				$('##noReferralInfo').hide();
				populateSecondaryForm();
				$('##contactInfo-secondary').slideDown();
				scrollDown('contactInfo-secondary');
			});
			$('.btnGoToStep1').click(function(){
				$('##noReferralInfo').hide();
				$('##step1form').slideDown();
				scrollDown('step1form');
			});
			$('.btnGoToStep1FromPrimaryContact').click(function(){
				$('##contactInfo-primary').hide();
				$('##step1form').slideDown();
				scrollDown('step1form');
			});
			$('.btnGoToStep1FromSecondaryContact').click(function(){
				$('##contactInfo-secondary').hide();
				$('##step1form').slideDown();
				scrollDown('step1form');
			});
			
			$('.btnPrimaryContactSubmit').click(function(){
				var errMsg = validatePrimaryContact();
				if(errMsg.length == 0){
					initAlert('primaryContactErr');
					$.ajax({
						type: "POST",
						url: '#local.strData.ajaxURL#&ra=checkDuplicate',
						success: function(result){
							if(result.status == 'fail'){
								$('##divMCClientReferralsContainer').html(result.referralhtml);
								return false;
							} else {
								if (ref_hideRepFieldsFE) {
									$('##isRepN').prop('checked',true);
									showNextStepAfterRepContact();
								} else {
									$('##isThisRep').slideDown();
									scrollDown('isThisRep');
								}
							}
						},
						dataType: 'json',
						data : $("##frmClient").serialize()
					});
				} else {
					showAlert('primaryContactErr',errMsg);
					scrollDown('contactInfo-primary');
				}
			   
			});
			$('.btnSecondaryContactSubmit').click(function(){
				var errMsg = validateSecondaryContact();
				if(errMsg.length == 0){
					initAlert('secondaryContactErr');
					goToViewMode('contactInfo-secondary');
					$('##frmClient').attr('action', '#local.strData.mainurl#&ra=submitcontact');
					$('##frmClient').submit();
				} else {
					showAlert('secondaryContactErr',errMsg);
					scrollUp();
				}
				
			});
			
			$('.btnRepSubmit').click(function(){
				var errMsg = validateRepContact();
				if(errMsg.length == 0){
					initAlert('repContactErr');
					goToViewMode('repForm');
					showNextStepAfterRepContact();
				} else {
					showAlert('repContactErr',errMsg);
					scrollDown('repForm');
				}
			});

			$('.btnIsRepSubmit').click(function(){
				var _isRep = parseInt($('input[name=isRep]:checked').val());
				goToViewMode('isThisRepSelect');
				if(_isRep){
					$('##paymentScreen').hide();
					if($('##extraInfo').length > 0){
						$('##extraInfo').hide();
					}
					$('.repForm').slideDown();
					scrollDown('repForm');
				} else {
					$('.repForm').hide();
					<cfif NOT local.strData.showContactFormFirst>
						if($('##extraInfo').length > 0){
							$('##extraInfo').slideDown();
						} else{
							if(parseInt($('##paymentRequired').val()) > 0 && !#local.strData.collectClientFeeFE#){
								showPaymentScreen();
							} else {
								submitAndProcessReferral();
							}
						}
					<cfelse>
						if($('##questionsSection').length){
							$('##questionsSection').slideDown();
							scrollDown('questionsSection');
						} else {
							$('##panelSubpanel').slideDown();
							scrollDown('panelSubpanel');
						}
					</cfif>
				}
			});

			$('.btnExtraInfoSubmit').click(function(){
				var errMsg = validateExtraInfoForm();
				if(errMsg.length == 0){
					initAlert('extraInfoErr',errMsg);
					goToViewMode('extraInfo');
					if(parseInt($('##paymentRequired').val()) > 0  && !#local.strData.collectClientFeeFE#){
						showPaymentScreen();
					} else {
						submitAndProcessReferral();
					}
					scrollDown();
				} else{
					showAlert('extraInfoErr',errMsg);
					scrollDown('extraInfo');
				}
			});

			$('##clientSaleBtn').click(function(){
				var arrReq = new Array();
				$('##clientSaleBtn').attr('disabled','disabled');
				#local.strData.profile_1.strPaymentForm.jsvalidation#
				if (arrReq.length > 0) {
					$('##clientSaleBtn').removeAttr('disabled');
					var msg = 'Please address the following issues with your application:\n\n';
					for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
					alert(msg);
				} else {	
					submitAndProcessReferral();
				}
			});
			
			$('i[class^=editForm_],i[class*=editForm_]').click(function(){
				var _this = $(this);
				var _id = _this.attr('data-id');
				$('##'+_id+'View').hide();
				$('##'+_id+'Form').slideDown();

				if(_id=='contactInfo-primary'){
					$('##isThisRep').hide();
					$('##paymentScreen').hide();
				}
				if(_id=='isThisRepSelect'){
					$('##repForm').hide();
					$('##paymentScreen').hide();
				}
				if(_id=='additionalFilters'){
					$('.step2btnWrapper').hide();
				}
				if(_id=='legalIssue'){
					$('##toHide').show();
					$('##toShow').hide();
					$('##additionalFilters').hide();
					$('.step2btnWrapper').hide();
				}
				
				if(_id=='panelSubpanel'){
					$('.btnContinuePanelSubpanel').show();
				}
				if(_id=='extraInfo'){
					hideSection('paymentScreen');
				}
				scrollDown(_id);
				$(this).hide();
			});

			if (window.addEventListener) {
				window.addEventListener("message", referralQuestionsMessageHandler, false);
			} else if (window.attachEvent) {
				window.attachEvent("message", referralQuestionsMessageHandler);
			}
		});
		$(document).on('click','.btnAccept',function(){
			$('##referralInfo').hide();
			<cfif NOT local.strData.showContactFormFirst>
				$('##contactInfo-primary').slideDown();
				scrollDown('contactInfo-primary');
			<cfelse>
				if($('##extraInfo').length > 0){
					$('##extraInfo').slideDown();
				} else{
					if(parseInt($('##paymentRequired').val()) > 0  && !#local.strData.collectClientFeeFE#){
						showPaymentScreen();
					} else {
						submitAndProcessReferral();
					}
				}
			</cfif>
		});
		$(document).on('click','.btnGoToStep1',function(){
			$('##referralInfo').hide();
			$('##step1form').slideDown();
			scrollDown('step1form');
		});
		function hideSection(_id){
			$('##'+_id).hide();
		}
		function populateSecondaryForm(){
			if($('##firstName').val().length > 0){
				$('##firstNameSecondary').val($('##firstName').val());
				$('##lastNameSecondary').val($('##lastName').val());
				$('##emailSecondary').val($('##email').val());
				
				if ($('##homePhoneSecondary').length) {
					if($('##homePhone').length && $('##homePhone').val().length > 0){
						$('##homePhoneSecondary').val($('##homePhone').val());
						$('##homePhoneSecondaryE164').val($('##homePhoneE164').val());
						$('##homePhoneSecondaryNational').val($('##homePhoneNational').val());
					}else if($('##cellPhone').length && $('##cellPhone').val().length > 0){
						$('##homePhoneSecondary').val($('##cellPhone').val());
						$('##homePhoneSecondaryE164').val($('##cellPhoneE164').val());
						$('##homePhoneSecondaryNational').val($('##cellPhoneNational').val());
					}else if($('##alternatePhone').length && $('##alternatePhone').val().length > 0){
						$('##homePhoneSecondary').val($('##alternatePhone').val());
						$('##homePhoneSecondaryE164').val($('##alternatePhoneE164').val());
						$('##homePhoneSecondaryNational').val($('##alternatePhoneNational').val());
					}
				}
			}
		}
		function showStep2(r){
			if(r.status == "noattorneysfound"){
				$('##formSubmitting').hide();
				$('##noReferralInfo').slideDown();
				$('##paymentRequired').val(0);
				<cfif len(trim(local.strData.feFormInstructionsContent))>
					scrollDown('mainInstructions');
				<cfelse>
					scrollDown('noReferralInfo');
				</cfif>
			} else if(r.status == "attorneyfound"){
				$('##formSubmitting').hide();
				$('##referralInfo').html(r.referralhtml);
				if(typeof r.chargeinfo != "undefined"){
					if (typeof window['chargeInfo'+r.fepayprofileid] != "undefined") window['chargeInfo'+r.fepayprofileid] = r.chargeinfo;
					$('##paymentScreen ##paymentAmt').html(formatCurrency(r.chargeinfo.amt));
					$('##paymentScreen ##paymentAmt').removeClass('hide');
					$('##paymentRequired').val(1);
					if (typeof r.processfeelabel != "undefined") {
						window['processFeeLabel'+r.fepayprofileid] = r.processfeelabel;
						$('##processFeeLabel'+r.fepayprofileid).html(r.processfeelabel);
					}
				}

				$('##referralInfo').slideDown();
				<cfif len(trim(local.strData.feFormInstructionsContent))>
					scrollDown('mainInstructions');
				<cfelse>
					scrollDown('referralInfo');
				</cfif>
			}
		}
		function showNextStepButton(){
			$('.step2btnWrapper').show();
		}
		function checkNshowIssueDesc(){
			if($('##panelID1').val().length > 0 && $('##panelID1').val() != 0){
				$('.legalIssueDesc').slideDown();
				goToEditMode('legalIssue');
				scrollDown('legalIssue');
			}
		}
		function showPaymentScreen(){
			$('##paymentScreen').slideDown();
			scrollDown('paymentScreen');
		}
		function submitAndProcessReferral(){
			$('##frmClient').attr('action', '#local.strData.mainurl#&ra=checkout');
			$('##frmClient').submit();
		}
		function validatePrimaryContact(){
			var errorMsg = "";

			<cfif local.strData.isFrontEndDisplay['First Name']>
				if( <cfif local.strData.isRequired['First Name']>($.trim($('##firstName').val()).length == 0) ||</cfif> (($.trim($('##firstName').val()).length > 0) && (!nameRegex.test($.trim($('##firstName').val())))) ) {
					errorMsg += 'Enter a valid client first name. <br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Middle Name']>
				if( <cfif local.strData.isRequired['Middle Name']>($.trim($('##middleName').val()).length == 0) || </cfif>(($.trim($('##middleName').val()).length > 0) && (!initialRegex.test($.trim($('##middleName').val()))))) {
					errorMsg += 'Enter a valid client middle name. <br>';
				 }
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Last Name']>
				if( <cfif local.strData.isRequired['Last Name']>($.trim($('##lastName').val()).length == 0) || </cfif>(($.trim($('##lastName').val()).length > 0) && (!nameRegex.test($.trim($('##lastName').val()))))) {
					errorMsg += 'Enter a valid client last name. <br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Business']>
				if( <cfif local.strData.isRequired['Business']>($.trim($('##businessName').val()).length == 0) || </cfif>(($.trim($('##businessName').val()).length > 0) && (!nameRegex.test($.trim($('##businessName').val()))))) {
					errorMsg += 'Enter a valid business. <br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Address 1'] AND  local.strData.isRequired['Address 1']>
				if($.trim($('##address1').val()).length == 0) {
					errorMsg += 'Enter a valid address 1.<br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Address 2'] AND  local.strData.isRequired['Address 2']>
				if($.trim($('##address2').val()).length == 0) {
					errorMsg += 'Enter a valid address 2.<br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['City'] AND local.strData.isRequired['City']>
				if( $.trim($('##city').val()).length == 0) {
					errorMsg += 'Enter a valid city.<br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['State'] AND local.strData.isRequired['State']>
				if($.trim($('##state').val()).length == 0) {
					errorMsg += 'Select a state.<br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Zip Code'] AND local.strData.isRequired['Zip Code']>
				if( $.trim($('##postalCode').val()).length == 0) {
					errorMsg += 'Enter a valid zip Code.<br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Email']>
				if( <cfif local.strData.isRequired['Email']>( $.trim($('##email').val()).length == 0) ||</cfif> ($.trim($('##email').val()).length > 0 && (!emailRegex.test($.trim($('##email').val())))) ) {
					errorMsg += 'Enter a valid client e-mail. <br>';
				}	
				if( <cfif local.strData.isRequired['Email']>( $.trim($('##verifyEmail').val()).length == 0) ||</cfif> ( $.trim($('##email').val()).length > 0 && $.trim($('##email').val()) != $.trim($('##verifyEmail').val()))) {
					errorMsg += 'Please verify your e-mail. <br>';
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Home Phone ##']>
				indexHome = arrPhoneNumbersIds.indexOf("homePhone");
				if (<cfif local.strData.isRequired['Home Phone ##']>( $.trim($('##homePhone').val()).length == 0) ||</cfif>($.trim($('##homePhone').val()).length > 0  && indexHome >= 0)) {
					if(!MFAPhNoInput[indexHome].isValidNumber()) {
						errorMsg += 'Enter the client home telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
					}else{
						setNumberFormats(indexHome,'validation');
					}					
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Cell Phone ##']>
				indexCellPhone = arrPhoneNumbersIds.indexOf("cellPhone");
				if( <cfif local.strData.isRequired['Cell Phone ##']>( $.trim($('##cellPhone').val()).length == 0) ||</cfif>($.trim($('##cellPhone').val()).length > 0 && indexCellPhone >= 0)) {
					if(!MFAPhNoInput[indexCellPhone].isValidNumber()) {
						errorMsg += 'Enter the client cell number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
					}else{
						setNumberFormats(indexCellPhone,'validation');
					}	
				}
			</cfif>
			<cfif local.strData.isFrontEndDisplay['Alternate Phone ##']>
				indexAlternatePhone = arrPhoneNumbersIds.indexOf("alternatePhone");
				if( <cfif local.strData.isRequired['Alternate Phone ##']>( $.trim($('##alternatePhone').val()).length == 0) ||</cfif>($.trim($('##alternatePhone').val()).length > 0 && indexAlternatePhone >= 0)) {
					if(!MFAPhNoInput[indexAlternatePhone].isValidNumber()) {
						errorMsg += 'Enter the client alternate telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
					}else{
						setNumberFormats(indexAlternatePhone,'validation');
					}	
				}
			</cfif>

			return errorMsg;
		}
		function checkDuplicateReferral(){
			$.ajax({
				type: "POST",
				url: '#local.strData.ajaxURL#&ra=checkDuplicate',
				success: function(result){
					console.log(result);
					if(result.status == 'fail')
						$('##divMCClientReferralsContainer').html(result.referralhtml);
					else
						return;
				},
				dataType: 'json',
				data : $("##frmClient").serialize()
			});
			return false;
		}
		function validateSecondaryContact(){
			var errorMsg = "";

			 if( ($.trim($('##firstNameSecondary').val()).length == 0) || (($.trim($('##firstNameSecondary').val()).length > 0) && (!nameRegex.test($.trim($('##firstNameSecondary').val())))) ) {
				errorMsg += 'Enter a valid first name. <br>';
			}
			if( ($.trim($('##lastNameSecondary').val()).length == 0) || (($.trim($('##lastNameSecondary').val()).length > 0) && (!nameRegex.test($.trim($('##lastNameSecondary').val()))))) {
				errorMsg += 'Enter a valid last name. <br>';
			}
			if( $.trim($('##emailSecondary').val()).length == 0){
				if (($.trim($('##homePhoneSecondary').val()).length == 0)) {
					errorMsg += 'Enter the telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************) or enter a valid e-mail. <br>';
				} 
			}		
			if( $.trim($('##emailSecondary').val()).length > 0 && ($.trim($('##emailSecondary').val()) != $.trim($('##verifyEmailSecondary').val())) ){
				errorMsg += 'Please verify your e-mail. <br>';
			}
			if ($.trim($('##homePhoneSecondary').val()).length > 0) {
				indexSecondaryPhone = arrPhoneNumbersIds.indexOf("homePhoneSecondary");
				if( indexSecondaryPhone >= 0){
					if(!MFAPhNoInput[indexSecondaryPhone].isValidNumber()) {
						errorMsg += 'Enter the telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
					}
				}
			}
				
			return errorMsg;
		}
		function validateRepContact(){
			var errorMsg = "";

			if( ($.trim($('##repFirstName').val()).length == 0) || (($.trim($('##repFirstName').val()).length > 0) && (!nameRegex.test($.trim($('##repFirstName').val())))) ) {
				errorMsg += 'Enter a valid representative first name. <br>';
			}
			if( ($.trim($('##repLastName').val()).length == 0) || (($.trim($('##repLastName').val()).length > 0) && (!nameRegex.test($.trim($('##repLastName').val()))))) {
				errorMsg += 'Enter a valid representative last name. <br>';
			}	
			
			indexRepHomePhone = arrPhoneNumbersIds.indexOf("repHomePhone");
			if( ($.trim($('##repHomePhone').val()).length > 0 && indexRepHomePhone >= 0)) {
				if(!MFAPhNoInput[indexRepHomePhone].isValidNumber()) {
					errorMsg += 'Enter the representative home telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
				}else{
					setNumberFormats(indexRepHomePhone,'validation');
				}				
			}

			indexRepCellPhone = arrPhoneNumbersIds.indexOf("repCellPhone");
			if( ($.trim($('##repCellPhone').val()).length > 0 && indexRepCellPhone >= 0)) {
				if(!MFAPhNoInput[indexRepCellPhone].isValidNumber()) {
					errorMsg += 'Enter the representative cell telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
				}else{
					setNumberFormats(indexRepCellPhone,'validation');
				}				
			}

			indexRepAlternatePhone = arrPhoneNumbersIds.indexOf("repAlternatePhone");
			if( ($.trim($('##repAlternatePhone').val()).length > 0 && indexRepCellPhone >= 0)) {
				if(!MFAPhNoInput[indexRepCellPhone].isValidNumber()) {
					errorMsg += 'Enter the representative alternate telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
				}else{
					setNumberFormats(indexRepAlternatePhone,'validation');
				}				
			}
	
			if( (($.trim($('##repEmail').val()).length > 0) && (!emailRegex.test($.trim($('##repEmail').val())))) ) {
				errorMsg += 'Enter a valid representative e-mail. <br>';
			}	
			if( ($.trim($('##repEmail').val()).length > 0) && ($.trim($('##repEmail').val()) != $.trim($('##repVerifyEmail').val()))) {
				errorMsg += 'Please verify representative e-mail. <br>';
			}
			return errorMsg;
		}

		function showNextStepAfterRepContact() {
			<cfif NOT local.strData.showContactFormFirst>
				if($('##extraInfo').length > 0){
					$('##extraInfo').slideDown();
				} else {
					if(parseInt($('##paymentRequired').val()) > 0 && !#local.strData.collectClientFeeFE#){
						showPaymentScreen();
					} else {
						submitAndProcessReferral();
					}
				}
			<cfelse>
				if($('##questionsSection').length){
					$('##questionsSection').slideDown();
					scrollDown('questionsSection');
				} else {
					$('##panelSubpanel').slideDown();
					scrollDown('panelSubpanel');
				}
			</cfif>
		}
		function validateAdditionalFilters(){
			var errMsg = '';
			return errMsg;
		}
		function validateExtraInfoForm(){
			var errorMsg= "";
			var errorMsgArray = [];
			<cfif local.strData.extraInformation.hasFields>
				#local.strData.extraInformation.JSVALIDATION#
			</cfif>		
			/*drop empty elements*/
			var finalErrors = $.map(errorMsgArray, function(thisError){
				if (thisError.length) return "- "+thisError;
				else return null;
			});
			errorMsg += finalErrors.join('\n');	
			return errorMsg;
		}
		function goToViewMode(_id){
			var _containerid = _id;
			var viewHtml = "";
		   
			$('##'+_containerid+' .control-group').each(function(){
				_this = $(this);
				var viewHtml1 = "";
				var viewHtml2 = "";
				var viewHtml3 = "";
				if(_this.attr('data-proximity') == 'true'){
					_elem = _this.find('input');
					_elem2 = _this.find('select');
					if(_elem.val().trim().length > 0) {
						viewHtml1 =  '<br>Within ' + _elem2.find(':selected').text() + ' miles of <span class="pin">' + _elem.val().toString() + '</span>';
						viewHtml1 = '<p><b>'+_this.attr('data-label')+'</b> ' + viewHtml1 + '</p>';
					}
				} else {
					_elem = _this.find('input');
					if(_elem.length > 0) {
						_elem.each(function(){
							if($(this).attr('type') == "text"){
								if($(this).val().length > 0)
									viewHtml1 = viewHtml1 + '<br> ' + $(this).val().toString() + '<br>';
							}else if($(this).attr('type') == "checkbox" || $(this).attr('type') == "radio" ){
								if($(this).is(':checked')){
									if(_id == 'isThisRepSelect'){
										viewHtml1 = viewHtml1 + $(this).attr('data-val').toString() + '<br>';
									}else if(_id == 'extraInfo' || _id == 'additionalFilters'){
										viewHtml1 = viewHtml1 + $(this).parent().text().toString() + '<br>';
									}else if(_id == 'contactInfo-primary'){
										viewHtml1 = viewHtml1 + '<br>' + $(this).parent().text().toString() + '<br>';
									} else {
										viewHtml1 = viewHtml1 + '<br>' + $(this).val().toString() + '<br>';
									}
									
								}
							}
						});
						if(viewHtml1.replace('<br>','').trim().length > 0)
							viewHtml1 = '<p><b>'+_this.attr('data-label')+'</b> ' + viewHtml1 + '</p>';
					}
					
					_elem2 = _this.find('select');
					
					if(_elem2.length > 0) {
						_elem2.each(function(){
							
							if(typeof $(this).attr('multiple') == "undefined"){
								if($(this).find(':selected').length > 0 && $(this).find(':selected').text().trim().length > 0)
									viewHtml2 = viewHtml2 + '<br>' + $(this).find(':selected').text().trim() + '<br>';
							} else {
								_text = "";
								$(this).find(':selected').each(function(){
									if($(this).text().trim().length > 0) {
										if(_text.length > 0)
											_text = _text + ', '+ $(this).text().trim();
										else
											_text =  $(this).text().trim();
									}
								});
								if(_text.length > 0 && _text.length > 0)
									viewHtml2 = viewHtml2 + '<br>' + _text  + '<br>';					  
							}
						
						});
						if(viewHtml2.replace('<br>','').trim().length > 0)
							viewHtml2 = '<p><b>'+_this.attr('data-label')+'</b>' + viewHtml2 + '</p>';
					}

					_elem3 = _this.find('textarea');
					if(_elem3.length > 0) {
						if(_elem3.val().trim().length > 0){
							_text = _elem3.val().trim();
							if(_text.length > 100)
								_text = _text.substr(0, _text.lastIndexOf(' ', 97)) + '...';
							if(_containerid == "legalIssue")
								viewHtml3 = '<p>'+ _text + '</p>';
							else
								viewHtml3 = '<p><b>'+_this.attr('data-label')+'</b> <br>'+ _text + '</p>';
						}
					}
				}
				if(viewHtml1.trim().length>0)
					viewHtml = viewHtml + viewHtml1;
				if(viewHtml2.trim().length>0)
					viewHtml = viewHtml + viewHtml2;
				if(viewHtml3.trim().length>0)
					viewHtml = viewHtml + viewHtml3;				
			});
			if(viewHtml.length == 0)
				viewHtml = "<i>No data entered</i>";

			$('##'+_containerid+'View').html(viewHtml);
			$('##'+_containerid+'View').show();
			$('##editForm_'+_containerid).show();
			$('##'+_containerid+'Form').hide();

			if(_containerid == "legalIssue"){
				$('##toShow').show();
				$('##toHide').hide();
			}
		}
		function goToEditMode(_id){
			$('##'+_id+'View').hide();
			$('##'+_id+'Form').slideDown();

			if(_id=='legalIssue'){
				$('##toHide').show();
				$('##toShow').hide();
			}
		}
		function scrollTopThis(_this){
			$([document.documentElement, document.body]).animate({
					scrollTop: _this.offset().top
			}, 1000);
		}
		function scrollDown(_id){
			if(_id){
				$([document.documentElement, document.body]).animate({
					scrollTop: $("##"+_id).offset().top -100
				}, 1000);
			} else{
				$('html,body').animate({
					scrollTop: $(window).scrollTop() + 400
				});
			}
		}
		function scrollUp(){
			$('html,body').animate({
				scrollTop: $(window).scrollTop() - 400
			});
		}
		function showPaymentScreenWhenError(){
			$('##step1form').hide();

			goToViewMode('contactInfo-primary');
			$('##contactInfo-primary').slideDown();
			if($('##extraInfo').length > 0){
				goToViewMode('extraInfo');
				$('##extraInfo').slideDown();
			}
			if($('input[name=isRep]:checked').val()){
				$('##isThisRep').slideDown();
				goToViewMode('repForm');
				$('.repForm').slideDown();
			}
			showPaymentScreen();
		}
		function formatCurrency(num) {
			num = num.toString().replace(/\$|\,/g,'');
			if(isNaN(num)) num = "0";
			num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
			sign = (num == (num = Math.abs(num)));
			num = Math.floor(num*100+0.50000000001);
			cents = num%100;
			num = Math.floor(num/100).toString();
			if(cents<10) cents = "0" + cents;
			for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
			num.substring(num.length-(4*i+3));
			return (((sign)?'':'-') + '$' + num + '.' + cents);
		}
		function showAlert(e, msg){
			$('##'+e).addClass('alert alert-error');
			$('##'+e+' span').html(msg);
			$('##'+e+' button').show();
		}
		function initAlert(e){
			$('##'+e+' span').html('')
			$('##'+e).removeClass('alert alert-error');
			$('##'+e+' button').hide();
		}
	</script>
	<cfif local.strData.extraInformation.hasFields>
		#local.strData.extraInformation.head#
	</cfif>
	<style type="text/css">
		<!--- common styles --->
		##divMCClientReferralsContainer .refqa-pt-0 {padding-top:0!important;}
		##divMCClientReferralsContainer .refqa-pr-0 {padding-right:0!important;}
		##divMCClientReferralsContainer .refqa-pr-4 {padding-right:1.5em!important;}
		##divMCClientReferralsContainer .refqa-pl-3 {padding-left: 1em!important;}
		##divMCClientReferralsContainer .refqa-pl-5 {padding-left:2em!important;}
		##divMCClientReferralsContainer .refqa-mt-0 {margin-top:0!important;}
		##divMCClientReferralsContainer .refqa-mt-2 {margin-top:.5em!important;}
		##divMCClientReferralsContainer .refqa-mt-3 {margin-top:1em!important;}
		##divMCClientReferralsContainer .refqa-mt-4 {margin-top:1.5em!important;}
		##divMCClientReferralsContainer .refqa-mt-5 {margin-top:2em!important;}
		##divMCClientReferralsContainer .refqa-mb-0 {margin-bottom:0!important;}
		##divMCClientReferralsContainer .refqa-mb-1 {margin-bottom:.25em!important;}
		##divMCClientReferralsContainer .refqa-mb-2 {margin-bottom:.5em!important;}
		##divMCClientReferralsContainer .refqa-mb-3 {margin-bottom:1em!important;}
		##divMCClientReferralsContainer .refqa-mb-4 {margin-bottom:1.5em!important;}
		##divMCClientReferralsContainer .refqa-mb-5 {margin-bottom:2em!important;}
		##divMCClientReferralsContainer .refqa-ml-2 {margin-left:.5em!important;}
		##divMCClientReferralsContainer .refqa-mr-1 {margin-right:.25em!important;}
		##divMCClientReferralsContainer .refqa-mr-2 {margin-right:.5em!important;}
		##divMCClientReferralsContainer .refqa-mr-3 {margin-right:1em!important;}
		##divMCClientReferralsContainer .refqa-mr-4 {margin-right:1.5em!important;}
		##divMCClientReferralsContainer .refqa-mr-5 {margin-right:2em!important;}
		##divMCClientReferralsContainer .text-right {text-align:right;}

		##divMCClientReferralsContainer .questionLRISWrapper{
			position: relative;
			margin: 0 auto;
			padding: 19px 19px 14px;
			background-color: ##fff;
			border: 1px solid ##ddd;
			-webkit-border-radius: 4px;
			-moz-border-radius: 4px;
			border-radius: 4px;
			margin-top:20px;
			max-width: 65%;
		}
		##divMCClientReferralsContainer .questionLRISWrapper select{
			width: 220px;
		}
		##divMCClientReferralsContainer .questionLRISWrapper input[type=text], 
		##divMCClientReferralsContainer .questionLRISWrapper textarea, 
		##divMCClientReferralsContainer .questionLRISWrapper .uneditable-input {
			width: 206px;
		}
		##divMCClientReferralsContainer legend [class^="icon-"],legend [class*=" icon-"]{
			background:##FFF!important;
		}
		##divMCClientReferralsContainer .icon-pencil{
			cursor:pointer;
			float:right;
		}
		##divMCClientReferralsContainer .alert{
			position:relative;
		}
		##divMCClientReferralsContainer .alert button{top:5px;right:5px;}
		##divMCClientReferralsContainer ##step1form, ##divMCClientReferralsContainer ##mainInstructions, ##divMCClientReferralsContainer ##contactInfo-primary {
			padding-top: 20px;
		}
		##divMCClientReferralsContainer .hide {display:none;}
		

		/* custom-radio */
		##divMCClientReferralsContainer .mcref-qa-radio-container { -webkit-user-select: none; user-select: none; }
		##divMCClientReferralsContainer .mcref-qa-radio-container svg.mcref-qa-radio-svg { fill:none !important; vertical-align:middle !important; margin-left:-20px; }
		##divMCClientReferralsContainer .mcref-qa-radio-container svg.mcref-qa-radio-svg circle { stroke-width:2; stroke: ##C8CCD4; }
		##divMCClientReferralsContainer .mcref-qa-radio-container svg.mcref-qa-radio-svg path { stroke: ##008FFF !important; }
		##divMCClientReferralsContainer .mcref-qa-radio-container svg.mcref-qa-radio-svg path.mcref-qa-radio-inner { stroke-width: 6; stroke-dasharray: 19;	stroke-dashoffset: 19; }
		##divMCClientReferralsContainer .mcref-qa-radio-container svg.mcref-qa-radio-svg path.mcref-qa-radio-outer { stroke-width: 2; stroke-dasharray: 57; stroke-dashoffset: 57; }
		##divMCClientReferralsContainer .mcref-qa-radio-container input.mcref-qa-radio { display: none !important; }
		##divMCClientReferralsContainer .mcref-qa-radio-container input.mcref-qa-radio:checked + svg.mcref-qa-radio-svg path { transition: all 0.4s ease; }
		##divMCClientReferralsContainer .mcref-qa-radio-container input.mcref-qa-radio:checked + svg.mcref-qa-radio-svg path.mcref-qa-radio-inner { stroke-dashoffset: 38;	transition-delay: 0.3s; }
		##divMCClientReferralsContainer .mcref-qa-radio-container input.mcref-qa-radio:checked + svg.mcref-qa-radio-svg path.mcref-qa-radio-outer { stroke-dashoffset: 0; }
		##divMCClientReferralsContainer .mcref-qa-radio-container span { vertical-align:middle; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pageHead#">
<cfinclude template="/views/clientReferrals/commonTelJS.cfm">