<cfcomponent output="no">
	
	<cffunction name="doEventReg" access="package" output="false" returntype="string">
		<cfargument name="event" type="any">
		<cfargument name="strEvent" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset arguments.event.paramValue('regAction','')>
		<cfset local.objLocator = CreateObject("component","model.system.user.accountLocater")>
		<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event")>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.memberAdmin")>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.objMemberFieldsets = createObject("component","model.admin.memberFieldSets.memberFieldSets")>

		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfquery name="local.qryRegistrantRoles" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @controllingSiteResourceID int, @catTreeID int;
			select @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.eventAdminSiteResourceID#">;
			select @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@controllingSiteResourceID, 'Event Roles');
			
			select categoryID, categoryName, categoryDesc 
			from cms_categories
			where categoryTreeID = @catTreeID
			and isActive = 1 and parentCategoryID is NULL;
		</cfquery>
		
		<!--- switch depending on regAction --->
		<cfswitch expression="#arguments.event.getValue('regAction')#">

			<!--- locator --->
			<cfcase value="locator">
				<cfset local.qryEvRegResultsFS = local.objMemberFieldsets.getSettingsFieldsetID(siteresourceID=local.eventAdminSiteResourceID, area='adminEvRegResults', module="Events")>
				<cfset local.qryEvRegNewAcctFS = local.objMemberFieldsets.getSettingsFieldsetID(siteresourceID=local.eventAdminSiteResourceID, area='adminEvRegNewAcct', module="Events")>
				<cfset arguments.event.setValue('ov_resultsFSID',local.qryEvRegResultsFS.fieldsetID)>
				<cfset arguments.event.setValue('ov_createAcctFSID',local.qryEvRegNewAcctFS.fieldsetID)>
				<cfset local.resultsData = local.objLocator.locateMemberAdmin(event=arguments.event)>
				<cfset local.xmlResultFields = local.resultsData.qryMemberLocator.mc_outputFieldsXML[1]>
				<cfsavecontent variable="local.data">
					<cfinclude template="eventReg_step1_results.cfm">
				</cfsavecontent>
				<cfreturn local.data>
			</cfcase>

			<!--- usemid (selected a member from step 1) --->
			<cfcase value="usemid">
				<cfset arguments.event.paramValue('mid',int(val(arguments.event.getValue('mid',0))))>
				<cfif arguments.event.getValue('mid') gt 0>
					<cflocation url="#arguments.event.getValue('mainregurl')#&mid=#arguments.event.getValue('mid')#&mode=direct" addtoken="no">
				</cfif>
			</cfcase>
		
			<!--- newacct (new account from step 1) --->
			<cfcase value="newacct">
				<cfset local.qryGetFS = local.objMemberFieldsets.getSettingsFieldsetID(siteresourceID=local.eventAdminSiteResourceID, area='adminEvRegNewAcct', module="Events")>
				<cfset arguments.event.setValue('ov_createAcctFSID',local.qryGetFS.fieldsetID)>
				<cfset local.newMemID = local.objLocator.createAccount(event=arguments.event)>
				<cfif local.newMemID gt 0>
					<cfsavecontent variable="local.data">
						<cfoutput>
						Please wait... we will refresh this page automatically.
						<script language="javascript">useMember(#local.newMemID#);</script>
						</cfoutput>
					</cfsavecontent>
					<cfreturn local.data>
				</cfif>
			</cfcase>
						
			<!--- insert/update registrant --->
			<cfcase value="insertRegistrant">
				<cfset local.activeMemberID = application.objMember.getActiveMemberID(memberID=val(arguments.event.getValue('mid')))>

				<cfif arguments.event.getValue('registrantID',0) gt 0>
					<cfset local.addRegistrantSQL = saveRegistrant_update(event=arguments.event, strEvent=arguments.strEvent, qryRegistrantRoles=local.qryRegistrantRoles)>
				<cfelse>
					<cfset local.addRegistrantSQL = saveRegistrant_insert(event=arguments.event, strEvent=arguments.strEvent, qryRegistrantRoles=local.qryRegistrantRoles)>
					<cfset local.qrySubEvents = getForceSelectSubEvents(eventID=arguments.event.getValue('eid'), rateID=int(val(arguments.event.getValue('ev_rateID'))),
						defaultTimeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'), memberID=int(val(local.activeMemberID)))>
				</cfif>

				<!--- save registrant info --->
				<cftry>
					<cfquery name="local.qryAddRegistrant" datasource="#application.dsn.membercentral.dsn#" result="local.qryAddRegistrantResults">
						#preserveSingleQuotes(local.addRegistrantSQL)#
					</cfquery>
				<cfcatch type="any">
					<cfset local.tmpErrStr = { sql=replace(local.addRegistrantSQL,chr(10),"<br/>","ALL") }>
					<cfreturn application.objError.showAndSendError(cfcatch=cfcatch,objectToDump=local.tmpErrStr)>
				</cfcatch>
				</cftry>

				<cfif structKeyExists(local.qryAddRegistrant,"regCapReached") and local.qryAddRegistrant.regCapReached is 1>
					<cfset local.strEmailRegCap = generateRegCapReachedEmail(eventID=arguments.event.getValue('eid'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'))>
					
					<cfif arrayLen(local.strEmailRegCap.arrEmailTo)>
						<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
							emailto=local.strEmailRegCap.arrEmailTo,
							emailreplyto=local.strEmailRegCap.mailCollection.replyto,
							emailsubject=local.strEmailRegCap.mailCollection.subject,
							emailtitle=local.strEmailRegCap.emailTitle,
							emailhtmlcontent=local.strEmailRegCap.emailcontent,
							emailAttachments=[],
							siteID=arguments.event.getValue('mc_siteinfo.siteid'),
							memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTNOTIFY"),
							sendingSiteResourceID=local.eventAdminSiteResourceID
						)>
					</cfif>
				<!--- if a success email if needed --->
				<cfelseif local.qryAddRegistrant.success is 1>
					<cfquery name="local.getRegInfo" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
							@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">,
							@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.activeMemberID#">,
							@registrationID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEvent.qryEventRegMeta.registrationID#">;
						
						SELECT top 1 r.registrantID, ISNULL(eo.email,'') as overrideEmail
						FROM dbo.ev_registrants as r					
						INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = r.memberID
						INNER JOIN dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
						LEFT OUTER JOIN dbo.ams_emailAppOverrides as eo on eo.itemID = r.registrantID and eo.itemType = 'eventreg'
						WHERE r.registrationID = @registrationID
						AND mActive.memberID = @memberID
						AND r.recordedOnSiteID = @siteID
						AND r.status = 'A';

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<!--- send registrant their version --->
					<cfif len(arguments.event.getValue('chkEmailRegistrant','')) and listFind(arguments.event.getValue('chkEmailRegistrant'),1)>
						<cfset local.emailTo = arguments.event.getTrimValue('txtEmailRegistrant1','')>
						<cfset local.strEmailConfirmation = generateConfirmationEmail(registrantID=local.getRegInfo.registrantID, emailMode="registrant", emailTo=local.emailTo, siteid=arguments.event.getValue('mc_siteinfo.siteid'))>
						<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
							<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
								emailto=local.strEmailConfirmation.arrEmailTo,
								emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
								emailsubject=local.strEmailConfirmation.mailCollection.subject,
								emailtitle=local.strEmailConfirmation.emailTitle,
								emailhtmlcontent=local.strEmailConfirmation.emailcontent,
								emailAttachments=[],
								siteID=arguments.event.getValue('mc_siteinfo.siteid'),
								memberID=local.strEmailConfirmation.memberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
								sendingSiteResourceID=local.eventAdminSiteResourceID
							)>
						</cfif>
					</cfif>
					
					<!--- send staff their version --->
					<cfif len(arguments.event.getValue('chkEmailRegistrant','')) and listFind(arguments.event.getValue('chkEmailRegistrant'),2) and len(arguments.event.getTrimValue('txtEmailRegistrant2',''))>
						<cftry>
							<cfset local.strEmailConfirmation = generateConfirmationEmail(registrantID=local.getRegInfo.registrantID, emailMode="staff", emailTo=arguments.event.getTrimValue('txtEmailRegistrant2'), siteid=arguments.event.getValue('mc_siteinfo.siteid'))>
							<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
								<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
									emailto=local.strEmailConfirmation.arrEmailTo,
									emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
									emailsubject=local.strEmailConfirmation.mailCollection.subject,
									emailtitle=local.strEmailConfirmation.emailTitle,
									emailhtmlcontent=local.strEmailConfirmation.emailcontent,
									emailAttachments=[],
									siteID=arguments.event.getValue('mc_siteinfo.siteid'),
									memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
									messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
									sendingSiteResourceID=local.eventAdminSiteResourceID
								)>
							</cfif>
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch)>
						</cfcatch>
						</cftry>
					</cfif>
					<!--- sub event confirmation emails --->
					<cfif isDefined("local.qrySubEvents")>
						<cfloop query="local.qrySubEvents">
							<cfquery name="local.getRegInfo" datasource="#application.dsn.membercentral.dsn#">
								SELECT top 1 r.registrantID
								FROM dbo.ev_registrants as r
								INNER JOIN dbo.ams_members as m on m.memberID = r.memberID
								WHERE r.registrationID = <cfqueryparam value="#local.qrySubEvents.subEventRegistrationID#" cfsqltype="CF_SQL_INTEGER">
								AND r.memberID = <cfqueryparam value="#local.activeMemberID#" cfsqltype="CF_SQL_INTEGER">
								AND r.recordedOnSiteID = #arguments.event.getValue('mc_siteinfo.siteid')#
								AND r.status = 'A'
							</cfquery>

							<cfif local.qrySubEvents.sendRegConfirmation is 1>
								<cftry>
									<cfset local.strEmailConfirmation = generateConfirmationEmail(registrantID=local.getRegInfo.registrantID, emailMode="registrant", siteid=arguments.event.getValue('mc_siteinfo.siteid'))>
									<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
										<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
											emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
											emailto=local.strEmailConfirmation.arrEmailTo,
											emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
											emailsubject=local.strEmailConfirmation.mailCollection.subject,
											emailtitle=local.strEmailConfirmation.emailTitle,
											emailhtmlcontent=local.strEmailConfirmation.emailcontent,
											emailAttachments=[],
											siteID=arguments.event.getValue('mc_siteinfo.siteid'),
											memberID=local.strEmailConfirmation.memberID,
											messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
											sendingSiteResourceID=local.eventAdminSiteResourceID
										)>
									</cfif>
								<cfcatch type="Any">
									<cfset application.objError.sendError(cfcatch=cfcatch)>
								</cfcatch>				
								</cftry>
							</cfif>						

							<cfif local.qrySubEvents.sendStaffConfirmation is 1>
								<cftry>
									<cfset local.strEmailConfirmation = generateConfirmationEmail(registrantID=local.getRegInfo.registrantID, emailMode="staff", siteid=arguments.event.getValue('mc_siteinfo.siteid'))>
									<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
										<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
											emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
											emailto=local.strEmailConfirmation.arrEmailTo,
											emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
											emailsubject=local.strEmailConfirmation.mailCollection.subject,
											emailtitle=local.strEmailConfirmation.emailTitle,
											emailhtmlcontent=local.strEmailConfirmation.emailcontent,
											emailAttachments=[],
											siteID=arguments.event.getValue('mc_siteinfo.siteid'),
											memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
											messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
											sendingSiteResourceID=local.eventAdminSiteResourceID
										)>
									</cfif>
								<cfcatch type="Any">
									<cfset application.objError.sendError(cfcatch=cfcatch)>
								</cfcatch>				
								</cftry>
							</cfif>						
						</cfloop>
					</cfif>

				</cfif>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						top.closeUpdateRegistration();
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn local.data>
			</cfcase>
			
			<cfdefaultcase></cfdefaultcase>
		</cfswitch>
		
		<!--- no event found --->
		<cfif arguments.strEvent.qryEventMeta.recordcount is 0> 
			<cfsavecontent variable="local.data">
				<cfoutput>That event does not exist.</cfoutput>
			</cfsavecontent>
		
		<!--- inactive event --->
		<cfelseif arguments.strEvent.qryEventMeta.status neq "A"> 
			<cfsavecontent variable="local.data">
				<cfoutput>This event is currently unavailable.</cfoutput>
			</cfsavecontent>

		<!--- good event --->
		<cfelse>
				
			<!--- event dates/times --->
			<cfsavecontent variable="local.eventtime">
				<cfif arguments.strEvent.qryEventMeta.isAllDayEvent>
					<cfif DateDiff("m",arguments.strEvent.qryEventTimes_selected.endTime,arguments.strEvent.qryEventTimes_selected.startTime)>
						<cfoutput>#DateFormat(arguments.strEvent.qryEventTimes_selected.startTime, "mmmm d, yyyy")#</cfoutput>
						<cfoutput> - #DateFormat(arguments.strEvent.qryEventTimes_selected.endTime, "mmmm d, yyyy")#</cfoutput>
					<cfelse>
						<cfoutput>#DateFormat(arguments.strEvent.qryEventTimes_selected.startTime, "mmmm d")#</cfoutput>
						<cfif DateCompare(arguments.strEvent.qryEventTimes_selected.endTime,arguments.strEvent.qryEventTimes_selected.startTime,"d")>
							<cfoutput>-#DateFormat(arguments.strEvent.qryEventTimes_selected.endTime, "d")#</cfoutput>
						</cfif>
						<cfoutput> #DateFormat(arguments.strEvent.qryEventTimes_selected.startTime, ", yyyy")#</cfoutput>
					</cfif>
				<cfelse>
					<cfoutput>#DateFormat(arguments.strEvent.qryEventTimes_selected.startTime, "mmmm d, yyyy")# </cfoutput>
					<cfif DateCompare(arguments.strEvent.qryEventTimes_selected.endTime,arguments.strEvent.qryEventTimes_selected.startTime,"d") is 0 and
						DateDiff("n",arguments.strEvent.qryEventTimes_selected.endTime,arguments.strEvent.qryEventTimes_selected.startTime) is 0>
						<cfoutput> #TimeFormat(arguments.strEvent.qryEventTimes_selected.startTime, "h:mm TT")#</cfoutput>
						<cfoutput> #arguments.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfoutput>
					<cfelseif DateCompare(arguments.strEvent.qryEventTimes_selected.endTime,arguments.strEvent.qryEventTimes_selected.startTime,"d") is 0>
						<cfoutput> #TimeFormat(arguments.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# </cfoutput>
						<cfoutput>- #timeformat(arguments.strEvent.qryEventTimes_selected.endTime,"h:mm TT")#</cfoutput>
						<cfoutput> #arguments.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfoutput>
					<cfelse>
						<cfoutput> #TimeFormat(arguments.strEvent.qryEventTimes_selected.startTime, "h:mm TT")#</cfoutput>
						<cfoutput> - #DateFormat(arguments.strEvent.qryEventTimes_selected.endTime, "mmmm d, yyyy")#</cfoutput>
						<cfoutput> #TimeFormat(arguments.strEvent.qryEventTimes_selected.endTime, "h:mm TT")#</cfoutput>
					</cfif>
				</cfif>
			</cfsavecontent>	
			
			<cfset local.qryCurrentRegMember = getRegistrantInfo(mid=arguments.event.getValue('mid',0))>
			<cfset local.qryMemberGroups = CreateObject("component","model.admin.members.members").getMember_groups(memberID=arguments.event.getValue('mid',0), orgID=val(arguments.event.getValue('mc_siteinfo.orgid')), featuredOnly=0)>
			<cfset local.qryMemberActiveSubs = CreateObject("component","model.admin.subscriptions.subscriptions").getMemberSubscriptions(orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberID=arguments.event.getValue('mid',0), statusCodeList='A')>
			
			<cfif arguments.event.getValue('registrantID',0)>
				<cfquery name="local.qryRegPayment" datasource="#application.dsn.membercentral.dsn#">
					select totalRegFee, totalRegFee-regFeePaid as totalDue
					from dbo.fn_ev_totalRegFeeAndPaid(<cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">, <cfqueryparam value="#arguments.event.getValue('registrantID',0)#" cfsqltype="CF_SQL_INTEGER">)
				</cfquery>

				<cfquery name="local.qryRegNotes" datasource="#application.dsn.membercentral.dsn#">
					select isFlagged, internalNotes
					from dbo.ev_registrants
					where registrantID = <cfqueryparam value="#arguments.event.getValue('registrantID',0)#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
				<cfset local.internalNotes = local.qryRegNotes.internalNotes>
				<cfset local.isRegFlagged = local.qryRegNotes.isFlagged>
				<cfset local.registrantOverrideEmail = local.objAdminEvent.getRegistrantOverrideEmail(registrantID=arguments.event.getValue('registrantID'))>
				<cfif NOT len(local.registrantOverrideEmail)>
					<cfset local.registrantOverrideEmail = local.qryCurrentRegMember.email>
				</cfif>
			<cfelse>
				<cfset local.internalNotes = "">
				<cfset local.isRegFlagged = 0>
				<cfset local.registrantOverrideEmail = local.qryCurrentRegMember.email>
			</cfif>

			<cfquery name="local.selectedRegistrantRoles" datasource="#application.dsn.membercentral.dsn#">
				select categoryID
				from dbo.ev_registrantCategories
				WHERE registrantID = <cfqueryparam value="#arguments.event.getValue('registrantID',0)#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>		

			<cfset local.stateIDForTax = ''>
			<cfset local.zipForTax = ''>
			<cfif arguments.event.getValue('registrantID',0) gt 0>
				<cfquery name="local.qryRegistrantTaxInfo" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					select top 1 ts.stateIDForTax, ts.zipForTax
					from dbo.tr_transactionSales as ts 
					inner join dbo.fn_ev_registrantTransactions(#val(arguments.event.getValue('registrantID'))#) as rt on rt.transactionId = ts.transactionId
					where ts.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset local.stateIDForTax = val(local.qryRegistrantTaxInfo.stateIDForTax)>
				<cfset local.zipForTax = local.qryRegistrantTaxInfo.zipForTax>
			<cfelseif arguments.event.getValue('regAction') eq '' and arguments.event.getValue('mid',0) gt 0>
				<cfset local.qryStateZipForTax = getStateZipForTax(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=arguments.event.getValue('mid'))>
				<cfset local.stateIDForTax = val(local.qryStateZipForTax.stateID)>
				<cfset local.zipForTax = local.qryStateZipForTax.postalCode>
			</cfif>

			<cfif len(local.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax).isvalidzip>
				<cfset local.zipForTax = "">
			</cfif>

			<cfset local.strRoleFields = structNew()>
			<cfloop query="local.qryRegistrantRoles">
				<cfset local.strTemp =  local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
					viewMode='bs4', resourceType='EventAdmin', areaName='Role', csrid=local.eventAdminSiteResourceID, 
					detailID=local.qryRegistrantRoles.categoryID, hideAdminOnly=0, itemType='EventRole', itemID=arguments.event.getValue('registrantID',0),
					trItemType='', trApplicationType='')>
				<cfset structInsert(local.strRoleFields, local.qryRegistrantRoles.categoryID, local.strTemp)>
			</cfloop>

			<cfset local.strRegFields = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
											viewMode='bs4', resourceType='Event', areaName='Registrant', csrid=arguments.strEvent.qryEventMeta.siteResourceID, 
											detailID=0, hideAdminOnly=0, itemType='EventRegCustom', itemID=arguments.event.getValue('registrantID',0),
											trItemType='custom', trApplicationType='Events')>

			<cfif local.qryCurrentRegMember.recordCount>
				<cfset local.strEventRegRates = getRegRates(regid=arguments.strEvent.qryEventRegMeta.registrationid, mid=arguments.event.getValue('mid'))>
				<cfset local.qryRateIncludedTicketPackages = getTicketPackagesForEventRate(registrationID=arguments.strEvent.qryEventRegMeta.registrationid)>
				<cfset local.strRegistrantRate = local.objAdminEvent.getRegistrantRateIDAndFee(registrantID=arguments.event.getValue('registrantID',0))>
				<cfset local.qryTicketDetails = getTicketForReg(registrationid=arguments.strEvent.qryEventRegMeta.registrationid, rateID=0)>
			</cfif>

			<cfset local.arrEventSetupIssues = local.objAdminEvent.getEventSetupIssues(strEvent=arguments.strEvent)>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<cfinclude template="eventReg.cfm">
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getRegistrantInfo" access="package" output="no" returntype="query">
		<cfargument name="mid" type="numeric" required="yes">
		
		<cfset var qryMember = "">

		<cfquery name="qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @memberID int, @orgID int;
			SET @memberID = <cfqueryparam value="#arguments.mid#" cfsqltype="CF_SQL_INTEGER">;
			SELECT @orgID = orgID FROM dbo.ams_members WHERE memberID = @memberID;

			select top 1 o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix,
				mActive.memberID, mActive.firstname, mActive.lastname, me.email, mActive.memberNumber, o.orgcode,
				case when o.hasPrefix = 1 then mActive.prefix else '' end as prefix,
				case when o.hasMiddleName = 1 then mActive.middlename else '' end as middleName,
				case when o.hasSuffix = 1 then mActive.suffix else '' end as suffix,
				case when o.hasProfessionalSuffix = 1 then mActive.professionalsuffix else '' end as professionalsuffix,
				mActive.company
			from dbo.ams_members as m
			inner join dbo.organizations as o on o.orgID = @orgID
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = mActive.memberID
			inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID 
				and metagt.emailTagTypeID = metag.emailTagTypeID
				and metagt.emailTagType = 'Primary'
			inner join dbo.ams_memberEmails as me on me.orgID = @orgID
				and me.memberID = metag.memberID
				and me.emailTypeID = metag.emailTypeID
			where m.memberID = <cfqueryparam value="#arguments.mid#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMember>
	</cffunction>

	<cffunction name="getRegRates" access="package" output="no" returntype="struct">
		<cfargument name="regid" type="numeric" required="yes">
		<cfargument name="mid" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="EventRate", functionName="Qualify")>

		<cfstoredproc procedure="ev_getRatesByRegistrationIDForAdmin" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.regid#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.mid#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.QualifyRFID#">
			<cfprocresult name="local.qryRatesEligible" resultset="1">
			<cfprocresult name="local.qryRatesIneligible" resultset="2">
		</cfstoredproc>

		<cfreturn local>
	</cffunction>

	<cffunction name="getForceSelectSubEvents" access="package" output="no" returntype="query">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="defaultTimeZoneID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var qrySubEvents = "">

		<cfquery name="qrySubEvents" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select cl.contentTitle as subEventTitle, r.rateName as subEventRateName, r.rateID as subEventRateID, subE.eventID as subEventID, 
				subR.registrationID as subEventRegistrationID, coalesce(r.glaccountID,subE.glAccountID) as useGLAccountID, 
				r.rate as subEventRate, se.sendStaffConfirmation, se.sendRegConfirmation
			from dbo.ev_events as e
			inner join dbo.ev_subEvents as se on se.parenteventID = e.eventID 
				and se.forceSelect = 1
			inner join dbo.ev_events as subE on subE.eventID = se.eventID 
				and subE.status = 'A'
			inner join dbo.cms_contentLanguages as cl on cl.contentID = subE.eventContentID
				and cl.languageID = 1
			inner join dbo.ev_registration as subR on subR.eventID = subE.eventID 
				and subR.siteID = subE.siteID
				and subR.status = 'A'
				and getdate() between subR.startDate and subR.endDate
			inner join dbo.ev_rateMappings as rm on rm.subEventID = se.subEventID
				and rm.parentRateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.ev_rates as r on r.rateID = rm.subRateID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.ev_times as subET on subET.eventID = subE.eventID
				and subET.timezoneID = #arguments.defaultTimeZoneID#
			where e.eventID = <cfqueryparam value="#arguments.eventID#" cfsqltype="CF_SQL_INTEGER">
			and dbo.fn_ev_isUserRegisteredForEvent(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,subE.eventID) = 0
			order by subET.startTime, cl.contentTitle, r.rateName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySubEvents>
	</cffunction>

	<cffunction name="getStateZipForTax" access="private" output="no" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberid" type="numeric" required="yes">

		<cfset var qryStateID = "">

		<cfquery name="qryStateID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">,
				@memberID int = <cfqueryparam value="#arguments.memberid#" cfsqltype="cf_sql_integer">;

			select ma.stateID, ma.postalCode
			from dbo.ams_memberAddresses as ma
			inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
				AND matag.memberID = ma.memberID 
				AND matag.addressTypeID = ma.addressTypeID
			inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
				AND matagt.addressTagTypeID = matag.addressTagTypeID 
				AND matagt.addressTagType = 'Billing'
			where ma.orgID = @orgID
			and ma.memberid = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryStateID>
	</cffunction>
	
	<cffunction name="getRegRateByRateID" access="public" output="no" returntype="query">
		<cfargument name="rateid" type="numeric" required="yes">
		<cfset var local = structNew()>
		<cfstoredproc procedure="ev_getRateByRateIDForAdmin" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.rateID#">
			<cfprocresult name="local.qryRate" resultset="1">
		</cfstoredproc>				
		<cfreturn local.qryRate>
	</cffunction>
	
	<cffunction name="getEventName" access="private" output="no" returntype="query">
		<cfargument name="eid" type="numeric" required="yes">
		<cfset var local = structNew()>
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_getMetaByEventID">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.eid#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#session.mcstruct.languageID#">
			<cfprocresult name="local.qryEvent" resultset="1">
		</cfstoredproc>		
		<cfreturn local.qryEvent>
	</cffunction>

	<cffunction name="isMemberRegisteredForEvent" access="private" output="no" returntype="numeric">
		<cfargument name="mid" type="numeric" required="yes">
		<cfargument name="eid" type="numeric" required="yes">

		<cfset var qryRegistered = "">
		
		<cfquery name="qryRegistered" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_ev_isUserRegisteredForEvent(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mid#">,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eid#">) as isRegistered
		</cfquery>

		<cfreturn val(qryRegistered.isRegistered)>
	</cffunction>

	<cffunction name="generateConfirmationEmail" access="public" output="no" returntype="struct">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="emailMode" type="string" required="yes">
		<cfargument name="emailTo" type="string" required="no" default="">
		<cfargument name="emailCC" type="string" required="no" default="">
		<cfargument name="customText" type="string" required="no" default=""> 
		<cfargument name="emailsubject" type="string" required="no" default="">
		<cfargument name="siteID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.objEvent = CreateObject("component","event")>
		<cfset local.objEvents = CreateObject("component","model.events.events")>
		<cfset local.objAdminEvent = CreateObject("component","eventAdmin")>
		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>

		<cfquery name="local.qryRegistrantDetails" datasource="#application.dsn.membercentral.dsn#">
			select TOP 1 m.activeMemberID as memberID, reg.eventID, r.rateID, r.registrationID, s.siteCode, 
				r.isFlagged, r.internalNotes, ISNULL(eo.email,'') as overrideEmail
			from dbo.ev_registrants as r
			inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID and reg.siteID = r.recordedOnSiteID
			inner join dbo.ams_members as m on m.memberID = r.memberID
			inner join dbo.sites as s on s.siteID = r.recordedOnSiteID
			left outer join dbo.ams_emailAppOverrides as eo on eo.itemID = r.registrantID and eo.itemType = 'eventreg'
			where r.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
		</cfquery>
		
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryRegistrantDetails.sitecode)>
		<cfset local.qryRegistrant = getRegistrantInfo(mid=local.qryRegistrantDetails.memberID)>
		<cfset local.qryMainAddress = application.objMember.getMemberAddressByBillingAddressType(orgID=local.mc_siteInfo.orgID, memberid=local.qryRegistrantDetails.memberID)>
		<cfset local.qryMainEmail = application.objMember.getMainEmail(local.qryRegistrantDetails.memberID)>
		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=local.mc_siteInfo.siteid)>
		<cfset local.strEvent = local.objEvents.getEvent(eventID=local.qryRegistrantDetails.eventID, siteID=local.mc_siteInfo.siteid, languageID=1)>
		
		<cfset local.showTimeZone = true>
		<cfif application.objSiteInfo.getSiteInfo(session.mcStruct.siteCode).defaultTimeZoneID eq local.strEvent.qryEventTimes_selected.timezoneID and local.strEvent.qryEventMeta.alwaysShowEventTimezone eq 0>
			<cfset local.showTimeZone = false>
		</cfif>
		<cfset local.defaultCurrencyType = "">
		<cfif local.mc_siteInfo.showCurrencyType is 1>
			<cfset local.defaultCurrencyType = " #local.mc_siteInfo.defaultCurrencyType#">
		</cfif>
				
		<!--- event dates/times --->
		<cfsavecontent variable="local.eventtime">
			<cfif local.strEvent.qryEventMeta.isAllDayEvent>
				<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.startTime, "ddd, mmmm d, yyyy")#</cfoutput>
				<cfif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d")>
					<cfoutput> to </cfoutput>
					<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
				</cfif>
			<cfelse>
				<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.startTime, "ddd, mmmm d, yyyy")# </cfoutput>
				<cfif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d") is 0 and
					DateDiff("n",local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime) is 0>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				<cfelseif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d") is 0>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# </cfoutput>
					<cfoutput>- #timeformat(local.strEvent.qryEventTimes_selected.endTime,"h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				<cfelse>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# to </cfoutput>
					<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
					<cfoutput> #TimeFormat(local.strEvent.qryEventTimes_selected.endTime, "h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				</cfif>
			</cfif>
		</cfsavecontent>

		<!--- links to add to calendar --->
		<cfquery name="local.qryGetSRID" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,1,0)#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.mc_siteInfo.siteID#">;

			select ai.siteResourceID, ai.applicationInstanceID
			from dbo.ev_events as e
			inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID and ce.calendarID = ce.sourceCalendarID
			inner join dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = ce.calendarID
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = c.applicationInstanceID
			where e.siteID = @siteID
			and e.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryRegistrantDetails.eventID#">;
		</cfquery>
		<cfset local.baseProgramLink = CreateObject("component","model.appLoader").getAppBaseLink(applicationInstanceID=local.qryGetSRID.applicationInstanceID)>
		<cfset local.icalURL = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?event=cms.ShowResource&resID=#local.qryGetSRID.siteResourceID#&evAction=downloadICal&eid=#local.qryRegistrantDetails.eventID#&memRegId=#arguments.registrantID#&includeInfoContent=1&mode=stream">
		<cfset local.gcalURL = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?event=cms.ShowResource&resID=#local.qryGetSRID.siteResourceID#&evAction=downloadGCal&eid=#local.qryRegistrantDetails.eventID#&memRegId=#arguments.registrantID#&includeInfoContent=1&mode=stream">

		<!--- custom fields --->
		<cfquery name="local.qryQuestions" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int, @registrationID int;
			select @usageID = dbo.fn_cf_getUsageID('Event','Registrant',null);
			set @registrationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryRegistrantDetails.registrationID#">;

			select f.fieldID, f.detailID, f.fieldText, f.fieldReference, f.isRequired, f.requiredMsg, f.adminOnly, f.fieldOrder, 
				ft.dataTypeCode, ft.displayTypeCode, ft.supportAmt, ft.supportQty
			from dbo.cf_fields as f 
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID 
			inner join dbo.ev_events as e on e.siteResourceID = f.controllingSiteResourceID
			inner join dbo.ev_registration as r on r.eventID = e.eventID and e.siteID = r.siteID
			where r.registrationID = @registrationID
			and fu.parentUsageID = @usageID
			and f.isActive = 1
			and len(f.fieldReference) > 0
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case 
					when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
					else 1 end
			order by f.fieldOrder;
		</cfquery>
		
		<cfset local.registrantCustomDetails = local.objCustomFields.getResponses(itemType='EventRegCustom', itemID=arguments.registrantID).map(function(thisRow){
			if (arguments.thisRow.dataTypeCode == 'STRING' and arguments.thisRow.displayTypeCode == 'TEXTAREA')
				arguments.thisRow.customValue = Replace(arguments.thisRow.customValue,newLine(),"<br/>","ALL");
			return arguments.thisRow;
		})>
		
		<cfquery name="local.registrantRoleFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @registrantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">;

			SELECT fd.dataID, f.fieldID, f.fieldReference, f.fieldText, f.fieldOrder, f.displayOnly, ft.displayTypeCode,
				f.detailID, fv.valueID, ft.supportAmt, ft.supportQty, f.adminOnly, fd.customValue, c.categoryID, c.categoryName
			FROM dbo.fn_cf_getResponses (@registrantID, 'EventRole', NULL) as fd
			INNER JOIN dbo.ev_registrants as r on r.registrantID = @registrantID
				AND r.[status] = 'A'
			INNER JOIN dbo.ev_registrantCategories as rc on rc.registrantID = r.registrantID
			INNER JOIN dbo.cms_categories as c on c.categoryID = rc.categoryID
			INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID 
				AND f.detailID = c.categoryID
				AND f.isActive = 1
				AND f.adminOnly = 0
			INNER JOIN dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			INNER JOIN dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID AND fv.valueID = fd.valueID
			ORDER BY c.sortOrder, f.fieldOrder, fv.valueOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfquery name="local.registrantPackageInstances" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select rp.instanceID, tp.ticketPackageID, t.ticketName + ' - ' + tp.ticketPackageName as packageName, tp.ticketPackageConfirmationEmailInfo, t.ticketID, rp.includedFromRate
			from dbo.ev_registrantPackageInstances as rp
			inner join dbo.ev_ticketPackages as tp on tp.ticketPackageID = rp.ticketPackageID and rp.status = 'A'
			inner join dbo.ev_tickets as t on t.ticketID = tp.ticketID
			inner join dbo.ev_ticketPackageAvailable as tpa on tpa.priceID = rp.availablePriceID
			where rp.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
			order by t.sortOrder, t.ticketID, tp.sortOrder, tp.ticketPackageID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.registrantPackageInstances.recordCount>
			<cfquery name="local.registrantPackageCustomDetails" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select rp.instanceID, fd.dataID, f.fieldReference, f.fieldText, f.fieldOrder, f.displayOnly, ft.displayTypeCode,
					f.fieldID, fv.valueID, ft.supportAmt, ft.supportQty, f.adminOnly, fd.customValue
				from dbo.ev_registrantPackageInstances as rp
				cross apply dbo.fn_cf_getResponses(rp.instanceID,'ticketPackInstCustom',NULL) as fd
				inner join dbo.cf_fields as f on f.fieldID = fd.fieldID and f.isActive = 1
				inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and fv.valueID = fd.valueID 
				where rp.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
				and rp.status = 'A'
				order by rp.instanceID, f.fieldOrder, fv.valueOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfquery name="local.qryRegistrantTicketInstanceDetails" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select rp.instanceID, fd.dataID, rp.seatID, f.fieldReference, f.fieldText, f.fieldOrder, f.displayOnly, ft.displayTypeCode,
					f.fieldID, fv.valueID, ft.supportAmt, ft.supportQty, f.adminOnly, fd.customValue
				from dbo.ev_registrantPackageInstanceSeats as rp
				cross apply dbo.fn_cf_getResponses(rp.seatID,'ticketPackSeatCustom',NULL) as fd				
				inner join dbo.cf_fields as f on f.fieldID = fd.fieldID and f.isActive = 1
				inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and fv.valueID = fd.valueID 
				where rp.instanceID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.registrantPackageInstances.instanceID)#">)
				and rp.status = 'A'
				order by rp.instanceID, f.fieldOrder, fv.valueOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfset local.thisEventDetailURL = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?#local.baseProgramLink#&evAction=showDetail&eid=#local.qryRegistrantDetails.eventID#">

		<cfif local.strEvent.qryEventRegMeta.registrantEditAllowed is 1>
			<cfset local.regEditDeadline = local.strEvent.qryEventRegMeta.registrantEditDeadline>
			<cfset local.onlineTimeZone = local.objTSTZ.getTZCodeFromTZID(timeZoneID=local.mc_siteinfo.defaultTimeZoneID)>
			<cfif local.onlineTimeZone neq "US/Central">
				<cfset local.regEditDeadline = local.objTSTZ.convertTimeZone(dateToConvert=local.regEditDeadline,fromTimeZone='US/Central',toTimeZone=local.onlineTimeZone)>
			</cfif>
			
			<!--- edit event registration url --->
			<cfset local.editEventRegURL = local.thisEventDetailURL>
		</cfif>

		<!--- for online meetings --->
		<cfif local.strEvent.qryEventRegMeta.isOnlineMeeting is 1>
			<!--- online start time (convert times from central (how stored in db) to default timezone of site) --->
			<cfset local.onlinestarttime = local.strEvent.qryEventRegMeta.onlineEnterStartTime>
			<cfset local.onlineTimeZone = local.objTSTZ.getTZCodeFromTZID(timeZoneID=local.mc_siteinfo.defaultTimeZoneID)>
			<cfif local.onlineTimeZone neq "US/Central">
				<cfset local.onlinestarttime = local.objTSTZ.convertTimeZone(dateToConvert=local.onlinestarttime,fromTimeZone='US/Central',toTimeZone=local.onlineTimeZone)>
			</cfif>	
			
			<!--- online enter program url --->
			<cfset local.enterProgramURL = local.thisEventDetailURL>
		</cfif>			

		<!--- has event documents --->
		<cfset local.eventDocURL = "">
		<cfset local.hasEventDocs = local.objEvent.hasEventDocumentsForAnEvent(eventID=local.qryRegistrantDetails.eventID)>
		<cfif local.hasEventDocs>
			<cfset local.eventDocURL = local.thisEventDetailURL>
		</cfif>

		<!--- online meetings, event documents, and edit reg use memberkey --->
		<cfif local.strEvent.qryEventRegMeta.isOnlineMeeting is 1 OR len(local.eventDocURL) OR local.strEvent.qryEventRegMeta.registrantEditAllowed is 1>
			<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.qryRegistrant.orgcode, membernumber=local.qryRegistrant.membernumber)>

			<cfif local.strEvent.qryEventRegMeta.isOnlineMeeting is 1>
				<cfset local.enterProgramURL = local.enterProgramURL & "&mk=#local.memberKey#">
			</cfif>
			<cfif len(local.eventDocURL)>
				<cfset local.eventDocURL = local.eventDocURL & "&mk=#local.memberKey#">
			</cfif>
			<cfif local.strEvent.qryEventRegMeta.registrantEditAllowed is 1>
				<cfset local.editEventRegURL = local.editEventRegURL & "&mk=#local.memberKey#">
			</cfif>
		</cfif>

		<!--- credits --->
		<cfif local.strEvent.qryEventRegMeta.showCredit is 1>
			<cfset local.showCredit = 1>
			<cfset local.strEventRegCredits = local.objEvents.getCreditInfoForEvent(local.qryRegistrantDetails.eventID)>
			<cfif local.strEventRegCredits.creditCount is 0>
				<cfset local.showCredit = 0>
			</cfif>
		<cfelse>					
			<cfset local.showCredit = 0>
		</cfif>

		<!--- transactions for confirmation --->
		<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
			<cfprocresult name="local.qryTotals" resultset="1">
			<cfprocresult name="local.qryRegTransactions" resultset="2">
			<cfprocresult name="local.qryPaymentAllocations" resultset="3">
		</cfstoredproc>

		<!--- rate price and name if the registrant has a rate. --->
		<cfif local.qryRegistrantDetails.rateID gt 0>
			<cfquery name="local.qryGetRateAmount" dbtype="query">
				select sum(amount) as amount
				from [local].qryRegTransactions
				where itemType = 'Rate'
				and itemID = #arguments.registrantID#
			</cfquery>
			<cfset local.ratename = local.objEvent.getRateName(rateID=local.qryRegistrantDetails.rateID)>
			<cfset local.qryRateDetails = local.objEvent.getRateDetails(rateID=local.qryRegistrantDetails.rateID)>
			<cfset local.rateMessage = local.qryRateDetails.rateMessage>
			<cfset local.freeRateDisplay = local.qryRateDetails.freeRateDisplay>
			<cfset local.rateprice = val(local.qryGetRateAmount.amount)>
		<cfelse>
			<cfset local.ratename = "">
			<cfset local.rateMessage = "">
			<cfset local.freeRateDisplay = "FREE">
			<cfset local.rateprice = 0>
		</cfif>
		
		<cfset local.tdStyle = "font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">

		<cfset local.strRegFields = {}>
		<cfquery name="local.qryCustom" dbtype="query">
			SELECT *
			FROM [local].qryQuestions
			<cfif listFindNoCase("registrant,registrantprint",arguments.emailMode)>
				WHERE adminOnly = 0
			</cfif>
			ORDER BY fieldOrder
		</cfquery>
		<cfif local.qryCustom.recordcount>
			<cfset local.strRegFields = getRegistrantFieldDetails(registrantID=arguments.registrantID, 
							defaultCurrencyType=local.defaultCurrencyType, tdStyle=local.tdStyle, freeRateDisplay=local.freeRateDisplay, 
							qryRegTransactions=local.qryRegTransactions, qryRegistrantCustomDetails=local.registrantCustomDetails, 
							qryCustom=local.qryCustom, mode="custom")>
		</cfif>

		<cfset local.arrRoleCustom = arrayNew(1)>
		<cfoutput query="local.registrantRoleFields" group="categoryID">
			<cfquery name="local.qryCustom" dbtype="query">
				select *
				from [local].registrantRoleFields
				where detailID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.registrantRoleFields.categoryID#">
				order by fieldOrder
			</cfquery>
			<cfset local.tmpStr = getRegistrantFieldDetails(registrantID=arguments.registrantID, defaultCurrencyType=local.defaultCurrencyType, 
									tdStyle=local.tdStyle, freeRateDisplay=local.freeRateDisplay, qryRegTransactions=local.qryRegTransactions, 
									qryRegistrantCustomDetails=local.registrantRoleFields, qryCustom=local.qryCustom, mode="rolecustom")>
			<cfif len(local.tmpStr.customDetails)>
				<cfset structInsert(local.tmpStr, "categoryName", local.registrantRoleFields.categoryName)>
				<cfset arrayAppend(local.arrRoleCustom, local.tmpStr)>
			</cfif>
		</cfoutput>

		<cfset local.arrTickets = []>
		<cfoutput query="local.registrantPackageInstances" group="ticketPackageID">
			<cfquery name="local.thisRegistrantPackage" dbtype="query">
				select instanceID, includedFromRate
				from [local].registrantPackageInstances
				where ticketPackageId = #local.registrantPackageInstances.ticketPackageID#
			</cfquery>
			
			<cfset local.totalInstances = local.thisRegistrantPackage.recordCount>
			<cfset local.totalPackageAmount = 0>
			<cfif local.totalInstances gt 0>
				<cfquery name="local.qryPackageAmount" dbtype="query">
					select sum(amount) as totalAmt
					from [local].qryRegTransactions
					where itemID IN (#valueList(local.thisRegistrantPackage.instanceID)#)
					and itemType = 'TicketPackInst'
				</cfquery>
				<cfset local.totalPackageAmount = val(local.qryPackageAmount.totalAmt)>
			</cfif>
			
			<cfset local.totalInstancesIncludedFromRate = 0>
			<cfloop query="local.thisRegistrantPackage">
				<cfif local.thisRegistrantPackage.includedFromRate eq 1>
					<cfset local.totalInstancesIncludedFromRate = local.totalInstancesIncludedFromRate + 1>
				</cfif>
			</cfloop>
			<cfset local.totalPackageRegistrantSelected = local.totalInstances-local.totalInstancesIncludedFromRate>

			<!--- dont show item price here since they could be diff amounts --->
			<cfset local.strTemp = {}>
			<cfset local.strTemp.confTitle = local.registrantPackageInstances.packageName>
			<cfset local.strTemp.confEmailInfo = local.registrantPackageInstances.ticketPackageConfirmationEmailInfo>
			<cfif local.totalPackageRegistrantSelected gt 0>
				<cfif local.totalInstancesIncludedFromRate gt 0>
					<cfset local.strTemp.confQty = "QTY: #local.totalInstances# (#local.totalInstancesIncludedFromRate#&nbsp;included)">
				<cfelse>
					<cfset local.strTemp.confQty = "QTY: #local.totalInstances#">
				</cfif>
				<cfset local.strTemp.confAmt = "#dollarformat(local.totalPackageAmount)##iif(len(local.defaultCurrencyType),de(' #local.defaultCurrencyType#'),de(''))#">
			<cfelseif local.totalInstancesIncludedFromRate gt 0>
				<cfset local.strTemp.confQty = "QTY: #local.totalInstances# (#local.totalInstancesIncludedFromRate#&nbsp;included)">
				<cfset local.strTemp.confAmt = "#dollarformat(0)##iif(len(local.defaultCurrencyType),de(' #local.defaultCurrencyType#'),de(''))#">
			</cfif>

			<cfset local.strTemp.arrInstances = []>
			<cfset local.instanceNumber = 0>
			<cfoutput>
				<cfset local.instanceNumber = local.instanceNumber + 1>
				<cfset local.strTemp.arrInstances[local.instanceNumber] = {}>
				<cfset local.strTemp.arrInstances[local.instanceNumber].confTitle = local.registrantPackageInstances.packageName & " (#local.instanceNumber# of #local.totalInstances#)">

				<cfquery name="local.qryThisPackageInstanceCustom" dbtype="query">
					select *
					from [local].registrantPackageCustomDetails
					where instanceID = #local.registrantPackageInstances.instanceID#
					<cfif listFindNoCase("registrant,registrantprint",arguments.emailMode)>
						and adminOnly = 0
					</cfif>
					order by fieldOrder
				</cfquery>
				<cfif local.qryThisPackageInstanceCustom.recordcount>
					<cfset local.strTemp.arrInstances[local.instanceNumber].custom = getRegistrantFieldDetails(registrantID=arguments.registrantID, 
								defaultCurrencyType=local.defaultCurrencyType, tdStyle=local.tdStyle, freeRateDisplay=local.freeRateDisplay, 
								qryRegTransactions=local.qryRegTransactions, qryRegistrantCustomDetails=local.registrantPackageCustomDetails, 
								qryCustom=local.qryThisPackageInstanceCustom, mode="tpcustom")>
				</cfif>

				<cfset local.qryThisInstanceTicketSeatDetails = getRegistrantTicketSeatDetails(instanceID=local.registrantPackageInstances.instanceID)>

				<cfquery name="local.qryThisInstanceTicketCustomDetails" dbtype="query">
					select instanceID, dataID, seatID, fieldReference, fieldText, fieldOrder, displayOnly, displayTypeCode,
						fieldID, valueID, supportAmt, supportQty, adminOnly, customValue
					from [local].qryRegistrantTicketInstanceDetails
					where instanceID = #val(local.registrantPackageInstances.instanceID)#
				</cfquery>
				
				<cfset local.strTemp.arrInstances[local.instanceNumber].arrSeats = []>
				<cfloop query="local.qryThisInstanceTicketSeatDetails">
					<cfset local.strTemp.arrInstances[local.instanceNumber].arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow] = {}>
					<cfset local.strTemp.arrInstances[local.instanceNumber].arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow].seatNum = local.qryThisInstanceTicketSeatDetails.currentRow>
					<cfset local.strTemp.arrInstances[local.instanceNumber].arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow].seatID = local.qryThisInstanceTicketSeatDetails.seatID>
					<cfset local.strTemp.arrInstances[local.instanceNumber].arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow].memberID = local.qryThisInstanceTicketSeatDetails.memberID>

					<cfquery name="local.qryThisInstanceTicketCustom" dbtype="query">
						select * 
						from [local].qryThisInstanceTicketCustomDetails
						where seatID = #local.qryThisInstanceTicketSeatDetails.seatID#
						<cfif listFindNoCase("registrant,registrantprint",arguments.emailMode)>
							and adminOnly = 0
						</cfif>
						order by fieldOrder
					</cfquery>
					<cfif local.qryThisInstanceTicketCustom.recordcount>
						<cfset local.strTemp.arrInstances[local.instanceNumber].arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow].custom = getRegistrantFieldDetails(registrantID=arguments.registrantID, 
									defaultCurrencyType=local.defaultCurrencyType, tdStyle=local.tdStyle, freeRateDisplay=local.freeRateDisplay, 
									qryRegTransactions=local.qryRegTransactions, qryRegistrantCustomDetails=local.qryThisInstanceTicketCustomDetails, 
									qryCustom=local.qryThisInstanceTicketCustom, mode="tcustom")>
					</cfif>
				</cfloop>
			</cfoutput>

			<cfset arrayAppend(local.arrTickets,local.strTemp)>
		</cfoutput>

		<cfif val(local.qryTotals.total) gt 0>
			<cfstoredproc procedure="ev_getRegistrantPaymentDetail" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
				<cfprocresult name="local.qryRegPaymentDetail" resultset="1">
			</cfstoredproc>
		</cfif>

		<!--- get the selected registrant roles --->
		<cfquery name="local.qryRegRoles" datasource="#application.dsn.membercentral.dsn#">
			select STRING_AGG(c.categoryName,', ') as roleList
			from dbo.ev_registrantCategories as rc 
			inner join dbo.cms_categories as c on c.categoryID = rc.categoryID
			where rc.registrantID = <cfqueryparam value="#arguments.registrantID#" cfsqltype="CF_SQL_INTEGER">
			and c.isActive = 1
		</cfquery>

		<cfsavecontent variable="local.returnStruct.emailcontent">
			<cfinclude template="dsp_emailConfirmation.cfm">
		</cfsavecontent>
		<cfset local.returnStruct.emailcontent = application.objResourceRenderer.qualifyAllLinks(content=local.returnStruct.emailcontent, siteid=arguments.siteID)>		

		<cfset local.returnStruct.emailTitle = "#local.mc_siteInfo.sitename# Registration Confirmation">

		<!--- determine replyto --->
		<cfset local.replyto = trim(local.strEvent.qryEventRegMeta.replyToEmail)>
		<cfif NOT len(local.replyto)>
			<cfset local.replyto = local.mc_siteInfo.supportProviderEmail>
		</cfif>

		<!--- determine subject --->
		<cfif len(trim(arguments.emailsubject))>
			<cfset local.emailsubject = arguments.emailsubject>
		<cfelse>
			<cfset local.emailsubject = 'Registration for #local.strEvent.qryEventMeta.EventContentTitle#'>
		</cfif>

		<cfset local.returnStruct.memberID = local.qryRegistrant.memberID>
		<cfset local.returnStruct.registrantName = "#local.qryRegistrant.firstName# #local.qryRegistrant.lastname#">

		<!--- mail collection --->
		<cfscript>
		local.returnStruct.mailCollection = structNew();

		if (arguments.emailMode eq "Registrant") {
			local.returnStruct.mailCollection["from"] = local.mc_siteInfo.networkEmailFrom & ' ("' & local.mc_siteinfo.orgname & '")';
			local.returnStruct.mailCollection["replyto"] = local.replyto;
		} else if (arguments.emailMode eq "Staff") {
			local.returnStruct.mailCollection["from"] = local.mc_siteInfo.networkEmailFrom & ' ("#local.qryRegistrant.FirstName# #local.qryRegistrant.lastname#")';
			if (len(local.qryMainEmail.email))
				local.returnStruct.mailCollection["replyto"] = local.qryMainEmail.email;
			else
				local.returnStruct.mailCollection["replyto"] = local.replyto;
		}
		local.returnStruct.mailCollection["subject"] = local.emailsubject;
		local.returnStruct.mailCollection["mailerid"] = local.mc_siteinfo.sitename;

		if (len(arguments.emailTo))
			local.returnStruct.mailCollection["to"] = arguments.emailTo;
		else if (arguments.emailMode eq "Registrant") 
			local.returnStruct.mailCollection["to"] = local.qryMainEmail.email;
		else if (arguments.emailMode eq "Staff") 
			local.returnStruct.mailCollection["to"] = local.strEvent.qryEventRegMeta.notifyEmail;
		else 
			local.returnStruct.mailCollection["to"] = "";
		
		if (len(local.returnStruct.mailCollection["to"])) {
			local.stToEmail = replace(replace(local.returnStruct.mailCollection["to"],',',';','ALL'),' ','','ALL');
			local.toEmailArr = listToArray(local.stToEmail,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				if (len(local.toEmailArr[local.i]) and not isValid("regex",local.toEmailArr[local.i],application.regEx.email)) {
					arrayDeleteAt(local.toEmailArr,local.i);
				}
			}
			local.returnStruct.mailCollection["to"] = arrayToList(local.toEmailArr,'; ');
		}

		if (arguments.emailMode eq "Registrant" and len(arguments.emailCC) and local.qryMainEmail.email NEQ arguments.emailCC) {
			local.stCCEmail = replace(replace(arguments.emailCC,',',';','ALL'),' ','','ALL');
			local.ccEmailArr = listToArray(local.stCCEmail,';');
			for (local.i=1; local.i lte arrayLen(local.ccEmailArr); local.i++) {
				if (len(local.ccEmailArr[local.i]) and not isValid("regex",local.ccEmailArr[local.i],application.regEx.email)) {
					arrayDeleteAt(local.ccEmailArr,local.i);
				}
			}
			if (arrayLen(local.ccEmailArr))
				local.returnStruct.mailCollection["cc"] = arrayToList(local.ccEmailArr,'; ');
		}

		if (listFindNoCase("Registrant,Staff",arguments.emailMode)) {
			local.returnStruct.arrEmailTo = [];
			local.toEmailArr = listToArray(local.returnStruct.mailCollection["to"],';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				if (arguments.emailMode eq "Registrant")
					local.returnStruct.arrEmailTo.append({ name:local.returnStruct.registrantName, email:local.toEmailArr[local.i] });
				else
					local.returnStruct.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
			}
		}

		if (arguments.emailMode eq "Registrant") {
			// add primary email address
			if (len(local.qryMainEmail.email) AND NOT listFindNoCase(local.returnStruct.mailCollection["to"],local.qryMainEmail.email,';'))
				local.returnStruct.arrEmailTo.append({ name:local.returnStruct.registrantName, email:local.qryMainEmail.email });
			// add override email address
			if (len(local.qryRegistrantDetails.overrideEmail) AND NOT listFindNoCase(local.returnStruct.mailCollection["to"],local.qryRegistrantDetails.overrideEmail,';'))
				local.returnStruct.arrEmailTo.append({ name:local.returnStruct.registrantName, email:local.qryRegistrantDetails.overrideEmail });
		}

		local.returnStruct.mailCollectionParams = structNew();
		local.returnStruct.mailCollectionParams["X-MemberCentral-Site"] = local.mc_siteinfo.sitecode;
		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getRegistrantFieldDetails" access="public" output="no" returntype="struct">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="defaultCurrencyType" type="string" required="yes">
		<cfargument name="tdStyle" type="string" required="yes">
		<cfargument name="freeRateDisplay" type="string" required="yes">
		<cfargument name="qryRegTransactions" type="query" required="yes">
		<cfargument name="qryRegistrantCustomDetails" type="query" required="yes">
		<cfargument name="qryCustom" type="query" required="yes">
		<cfargument name="mode" type="string" required="yes">
		<cfargument name="isBS4" type="string" required="false" default="0">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		
		<cfif arguments.mode eq "tpcustom">
			<cfset arguments.tdStyle = arguments.tdStyle & "padding-left:55px;">
			<cfset local.AmtItemType = "ticketPackInstCustom">
		<cfelseif arguments.mode eq "tcustom">
			<cfset arguments.tdStyle = arguments.tdStyle & "padding-left:75px;">
			<cfset local.AmtItemType = "ticketPackSeatCustom">
		<cfelseif arguments.mode eq "custom">
			<cfset local.AmtItemType = "Custom">
		<cfelse>
			<cfset local.AmtItemType = "">
		</cfif>

		<cfsavecontent variable="local.returnStruct.customDetails">
			<cfoutput query="arguments.qryCustom" group="fieldID">
				<cfif arguments.qryCustom.displayTypeCode is 'TEXTBOX'>
					<cfquery name="local.qryTextBoxDetails" dbtype="query">
						SELECT dataID, customValue
						FROM arguments.qryRegistrantCustomDetails
						WHERE fieldID = #arguments.qryCustom.fieldID#
						<cfif arguments.mode eq "tpcustom">
							AND instanceID = #arguments.qryCustom.instanceID#
						<cfelseif arguments.mode eq "tcustom">
							AND seatID = #arguments.qryCustom.seatID#
						</cfif>
					</cfquery>
					<cfif len(local.qryTextBoxDetails.customValue)>
						<cfif arguments.qryCustom.supportQty AND val(local.qryTextBoxDetails.customValue)>
							<cfquery name="local.qryAmt" dbtype="query">
								select sum(amount) as rcAmount
								from arguments.qryRegTransactions
								where itemType = '#local.AmtItemType#'
								and itemID = #local.qryTextBoxDetails.dataID#
							</cfquery>
							<cfif arguments.isBS4>
								<div class="form-group row">
									<div class="col-4 #arguments.mode#FieldDesc">#htmlEditFormat(arguments.qryCustom.fieldText)#</div>
									<div class="col-4 #arguments.mode#Qty">QTY: #val(local.qryTextBoxDetails.customValue)#</div> <!--- dont show item price here since they could be diff amounts --->									
									<div class="col-4 text-right text-nowrap #arguments.mode#AmtDisp">
										<cfif len(local.qryAmt.rcAmount) IS 0>#arguments.freeRateDisplay#<cfelse>#dollarFormat(abs(local.qryAmt.rcAmount))#<cfif len(arguments.defaultCurrencyType)>#arguments.defaultCurrencyType#</cfif></cfif>
									</div>
								</div>
							<cfelse>
								<tr valign="top">
									<td style="#arguments.tdStyle#" class="#arguments.mode#FieldDesc">
										#htmlEditFormat(arguments.qryCustom.fieldText)#
									</td>
									<td style="#arguments.tdStyle#" class="#arguments.mode#Qty">
										QTY: #val(local.qryTextBoxDetails.customValue)# <!--- dont show item price here since they could be diff amounts --->
									</td>
									<cfif arguments.qryCustom.supportAmt>
										<td style="#arguments.tdStyle#text-align:right;" class="#arguments.mode#AmtDisp" nowrap>
											<cfif len(local.qryAmt.rcAmount) IS 0>
												#arguments.freeRateDisplay#
											<cfelse>
												#dollarFormat(abs(local.qryAmt.rcAmount))#<cfif len(arguments.defaultCurrencyType)>#arguments.defaultCurrencyType#</cfif>
											</cfif>
										</td>
									</cfif>	
								</tr>
							</cfif>
							
						<cfelseif arguments.qryCustom.supportAmt AND val(local.qryTextBoxDetails.customValue)>
							<cfquery name="local.qryAmt" dbtype="query">
								select sum(amount) as rcAmount
								from arguments.qryRegTransactions
								where itemType = '#local.AmtItemType#'
								and itemID = #local.qryTextBoxDetails.dataID#
							</cfquery>
							<cfif arguments.isBS4>
								<div class="form-group row">
									<div class="col-4 #arguments.mode#FieldDesc">#htmlEditFormat(arguments.qryCustom.fieldText)#</div>
									<div class="col-4 offset-4 text-right text-nowrap #arguments.mode#AmtDisp">
										#dollarFormat(abs(local.qryAmt.rcAmount))#<cfif len(arguments.defaultCurrencyType)>#arguments.defaultCurrencyType#</cfif>
									</div>
								</div>
							<cfelse>
								<tr valign="top">
									<td style="#arguments.tdStyle#" class="#arguments.mode#FieldDesc">
										#htmlEditFormat(arguments.qryCustom.fieldText)#
									</td>
									<td></td>
									<td style="#arguments.tdStyle#text-align:right;" class="#arguments.mode#AmtDisp" nowrap>
										#dollarFormat(abs(local.qryAmt.rcAmount))#<cfif len(arguments.defaultCurrencyType)>#arguments.defaultCurrencyType#</cfif>
									</td>
								</tr>
							</cfif>
						<cfelseif NOT arguments.qryCustom.supportQty and NOT arguments.qryCustom.supportAmt>
							<cfif arguments.isBS4>
								<div class="form-group row #arguments.mode#FieldDesc">
									<div class="col-12">
										#htmlEditFormat(arguments.qryCustom.fieldText)#
										<div class="pl-4">#local.qryTextBoxDetails.customValue#</div>
									</div>
								</div>
							<cfelse>
								<tr valign="top">
									<td style="#arguments.tdStyle#" class="#arguments.mode#FieldDesc" colspan="3">
										#htmlEditFormat(arguments.qryCustom.fieldText)#<br/>
										<div style="margin-left:35px;">#local.qryTextBoxDetails.customValue#</div>
									</td>
								</tr>
							</cfif>
						</cfif>
					</cfif>
					
				<cfelseif listFind("SELECT,RADIO",arguments.qryCustom.displayTypeCode)>
					<cfquery name="local.qryOptionDetails" dbtype="query">
						SELECT dataID, customValue
						FROM arguments.qryRegistrantCustomDetails
						WHERE fieldID = #arguments.qryCustom.fieldID#
						<cfif arguments.mode eq "tpcustom">
							AND instanceID = #arguments.qryCustom.instanceID#
						<cfelseif arguments.mode eq "tcustom">
							AND seatID = #arguments.qryCustom.seatID#
						</cfif>
					</cfquery>
					<cfif len(local.qryOptionDetails.customValue)>
						<cfquery name="local.qryAmount" dbtype="query">
							select sum(amount) as coAmount
							from arguments.qryRegTransactions
							where itemType = '#local.AmtItemType#'
							and itemID = #local.qryOptionDetails.dataID#
						</cfquery>
						<cfif arguments.isBS4>
							<div class="form-group row">
								<div class="col-4 #arguments.mode#FieldDesc">
									#htmlEditFormat(arguments.qryCustom.fieldText)#
									<div class="pl-4">#local.qryOptionDetails.customValue#</div>
								</div>
								<div class="col-4 offset-4 text-right #arguments.mode#AmtDisp" nowrap>
									<cfif local.qryAmount.coAmount GT 0>
										#dollarFormat(local.qryAmount.coAmount)#<cfif len(arguments.defaultCurrencyType)>#arguments.defaultCurrencyType#</cfif>
									</cfif>
								</div>
							</div>
						<cfelse>
							<tr>
								<td style="#arguments.tdStyle#" class="#arguments.mode#FieldDesc">
									#htmlEditFormat(arguments.qryCustom.fieldText)#<br/>
									<div style="margin-left:35px;">#local.qryOptionDetails.customValue#</div>
								</td>
								<td></td>
								<td style="#arguments.tdStyle#text-align:right;" class="#arguments.mode#AmtDisp" nowrap>
									<cfif local.qryAmount.coAmount GT 0>
										#dollarFormat(local.qryAmount.coAmount)#<cfif len(arguments.defaultCurrencyType)>#arguments.defaultCurrencyType#</cfif>
									</cfif>
								</td>
							</tr>
						</cfif>
					</cfif>

				<cfelseif arguments.qryCustom.displayTypeCode is 'CHECKBOX'>
					<cfquery name="local.qryCheckoxDetails" dbtype="query">
						SELECT dataID, customValue
						FROM arguments.qryRegistrantCustomDetails
						WHERE fieldID = #arguments.qryCustom.fieldID#
						<cfif arguments.mode eq "tpcustom">
							AND instanceID = #arguments.qryCustom.instanceID#
						<cfelseif arguments.mode eq "tcustom">
							AND seatID = #arguments.qryCustom.seatID#
						</cfif>
					</cfquery>
					<cfif local.qryCheckoxDetails.recordCount>
						<cfif arguments.isBS4>
							<div class="form-group row">
								<div class="col-12 #arguments.mode#FieldDesc">#htmlEditFormat(arguments.qryCustom.fieldText)#</div>
							</div>
							<cfloop query="local.qryCheckoxDetails">
								<div class="form-group row">
									<div class="col-4 #arguments.mode#FieldDesc">
										<div class="pl-4">#local.qryCheckoxDetails.customValue#</div>
									</div>
									<div class="col-4 offset-4 text-right text-nowrap #arguments.mode#AmtDisp">
										<cfquery name="local.qryAmount" dbtype="query">
											select sum(amount) as coAmount
											from arguments.qryRegTransactions
											where itemType = '#local.AmtItemType#'
											and itemID = #local.qryCheckoxDetails.dataID#
										</cfquery>
										<cfif local.qryAmount.coAmount GT 0>
											#dollarFormat(local.qryAmount.coAmount)#<cfif len(arguments.defaultCurrencyType)>#arguments.defaultCurrencyType#</cfif>
										</cfif>
									</div>
								</div>
							</cfloop>
						<cfelse>
							<tr>
								<td style="#arguments.tdStyle#" class="#arguments.mode#FieldDesc" colspan="3">
									<div>#htmlEditFormat(arguments.qryCustom.fieldText)#</div>
								</td>
							</tr>
							<cfloop query="local.qryCheckoxDetails">
								<tr>
									<td style="#arguments.tdStyle#" class="#arguments.mode#FieldDesc">
										<div style="margin-left:35px;">#local.qryCheckoxDetails.customValue#</div>
									</td>
									<td></td>
									<td style="#arguments.tdStyle#text-align:right;" class="#arguments.mode#AmtDisp" nowrap>
										<cfquery name="local.qryAmount" dbtype="query">
											select sum(amount) as coAmount
											from arguments.qryRegTransactions
											where itemType = '#local.AmtItemType#'
											and itemID = #local.qryCheckoxDetails.dataID#
										</cfquery>
										<div>
											<cfif local.qryAmount.coAmount GT 0>
												#dollarFormat(local.qryAmount.coAmount)#<cfif len(arguments.defaultCurrencyType)>#arguments.defaultCurrencyType#</cfif>
											<cfelse>
												&nbsp;
											</cfif>
										</div>
									</td>
								</tr>
							</cfloop>
						</cfif>
					</cfif>

				<cfelseif listFind("DATE,TEXTAREA",arguments.qryCustom.displayTypeCode)>
					<cfquery name="local.qryDateTextAreaFields" dbtype="query">
						SELECT customValue
						FROM arguments.qryRegistrantCustomDetails
						WHERE fieldID = #arguments.qryCustom.fieldID#
						<cfif arguments.mode eq "tpcustom">
							AND instanceID = #arguments.qryCustom.instanceID#
						<cfelseif arguments.mode eq "tcustom">
							AND seatID = #arguments.qryCustom.seatID#
						</cfif>
					</cfquery>
					<cfif len(local.qryDateTextAreaFields.customValue)>
						<cfif arguments.isBS4>
							<div class="form-group row">
								<div class="col-12 #arguments.mode#FieldDesc">
									#htmlEditFormat(arguments.qryCustom.fieldText)#
									<div class="pl-4">#local.qryDateTextAreaFields.customValue#</div>
								</div>
							</div>
						<cfelse>
							<tr valign="top">
								<td style="#arguments.tdStyle#" class="#arguments.mode#FieldDesc" colspan="3">
									#htmlEditFormat(arguments.qryCustom.fieldText)#<br/>
									<div style="margin-left:35px;">#local.qryDateTextAreaFields.customValue#</div>
								</td>
							</tr>
						</cfif>
					</cfif>

				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getRegistrantTicketCustomDetails" access="private" output="no" returntype="query">
		<cfargument name="instanceID" type="numeric" required="yes">
		
		<cfset var qryRegistrantTicketInstanceDetails = "">
		
		<cfquery name="qryRegistrantTicketInstanceDetails" datasource="#application.dsn.membercentral.dsn#">
			select rp.instanceID, fd.dataID, rp.seatID, f.fieldReference, f.fieldText, f.fieldOrder, f.displayOnly, ft.displayTypeCode,
				f.fieldID, fv.valueID, ft.supportAmt, ft.supportQty, f.adminOnly,
				case when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(max))
					when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
					when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(15))
					when ft.dataTypeCode = 'BIT' then case when fv.valueBit = 1 then 'Yes' else 'No' end
					when ft.dataTypeCode = 'DATE' then convert(varchar(12), fv.valueDate, 101)
					when ft.dataTypeCode = 'DOCUMENTOBJ' then cast(fv.valueSiteResourceID as varchar(15))
					else null end as customValue
			from dbo.ev_registrantPackageInstanceSeats as rp
			inner join dbo.cf_fieldData as fd on fd.itemID = rp.seatID 
				and fd.itemType = 'ticketPackSeatCustom'
				and rp.status = 'A'
			inner join dbo.cf_fields as f on f.fieldID = fd.fieldID and f.isActive = 1
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and fv.valueID = fd.valueID 
			where rp.instanceID = <cfqueryparam value="#arguments.instanceID#" cfsqltype="CF_SQL_INTEGER">
			order by rp.instanceID, f.fieldOrder, fv.valueOrder;
		</cfquery>
		
		<cfreturn qryRegistrantTicketInstanceDetails>
	</cffunction>
	
	<cffunction name="getRegistrantTicketSeatDetails" access="public" output="no" returntype="query">
		<cfargument name="instanceID" type="numeric" required="yes">
		
		<cfset var qryRegistrantTicketSeatDetails = "">
		
		<cfquery name="qryRegistrantTicketSeatDetails" datasource="#application.dsn.membercentral.dsn#">
			select seatID, memberID
			from dbo.ev_registrantPackageInstanceSeats
			where instanceID = <cfqueryparam value="#arguments.instanceID#" cfsqltype="CF_SQL_INTEGER">
			and status = 'A';
		</cfquery>
		
		<cfreturn qryRegistrantTicketSeatDetails>
	</cffunction>
	
	<cffunction name="getTicketForReg" access="public" output="no" returntype="query">
		<cfargument name="registrationID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryTickets = "">

		<cfquery name="qryTickets" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @rateID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">,
				@registrationID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">;

			select t.ticketID, t.ticketName, tp.ticketPackageID, tp.ticketPackageName, 
				tpa.amount as ticketPackageAmount, isnull(t.GLAccountID,0) as GLAccountID, 
				<cfif arguments.rateID gt 0> 
					isnull(rtp.quantity,0) as qtyIncludedByRate,
					case when retp.ticketPackageID is not null then 1 else 0 end as isExcludedByRate,
				<cfelse>
					0 as qtyIncludedByRate, 0 as isExcludedByRate,
				</cfif>
				tp.maxPerRegistrant as maxPackagesPerRegistrant, tp.ticketCount as ticketsInPackage, 
				t.assignToMembers as ticketAssignSeats, tpa.priceID as availablePriceID, ps.rangeName, ps.startDate, ps.endDate, 
				t.inventory as ticketInventory, 
				(select count(rpid.seatID) 
					from dbo.ev_registrantPackageInstanceSeats as rpid
					inner join dbo.ev_registrantPackageInstances as rpi on rpi.instanceID = rpid.instanceID and rpi.status = 'A'
					inner join dbo.ev_ticketPackages as etp1 on etp1.ticketPackageID = rpi.ticketPackageID
					inner join dbo.ev_tickets as et on et.ticketID = etp1.ticketID
					where et.ticketID = t.ticketID
					and rpid.status = 'A') as ticketInventoryCount,
				tp.inventory as ticketPackageInventory,
				(select count(rpi.instanceID) 
					from dbo.ev_registrantPackageInstances as rpi
					inner join dbo.ev_ticketPackages as etp2 on etp2.ticketPackageID = rpi.ticketPackageID
					where etp2.ticketPackageID = tp.ticketPackageID
					and rpi.status = 'A') as ticketPackageInventoryCount
				<cfif arguments.rateID gt 0> 
					, ROW_NUMBER() OVER (PARTITION BY tp.ticketPackageID order by ps.startDate, isnull(rtp.quantity,0)) as row
				</cfif>
			from dbo.ev_tickets as t
			inner join dbo.ev_ticketPackages as tp on tp.ticketID = t.ticketID
			inner join dbo.ev_ticketPackageAvailable as tpa on tpa.ticketPackageID = tp.ticketPackageID and tpa.isActive = 1
			inner join dbo.ev_priceSchedule as ps on ps.scheduleID = tpa.scheduleID
			<cfif arguments.rateID gt 0>
				left outer join dbo.ev_rateTicketPackages as rtp on rtp.ticketPackageID = tp.ticketPackageID
					and rtp.rateID = @rateID
				left outer join dbo.ev_rateExcludeTicketPackages as retp on retp.ticketPackageID = tp.ticketPackageID
					and retp.rateID = @rateID
			</cfif>
			where t.registrationID = @registrationID
			order by t.sortOrder, t.ticketID, tp.sortOrder, tp.ticketPackageID, ps.startDate
		</cfquery>

		<cfreturn qryTickets>
	</cffunction>

	<cffunction name="manageRegTickets" access="package" output="false" returntype="string">
		<cfargument name="Event" type="any">	

		<cfscript>
			var local = structNew();
			local.objEvent = CreateObject("component","event");
			local.registrantID = arguments.event.getValue('registrantID',0);
			local.qryTicketDetails = getTicketForReg(registrationid=arguments.event.getValue('registrationID'), rateID=arguments.event.getValue('rateID'));
			local.defaultCurrencyType = "";
			if (arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1)
				local.defaultCurrencyType = " #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#";
		</cfscript>

		<cfif local.registrantID gt 0>
			<cfset local.qryTicketPackagesSelected = getTicketPackagesSelected(registrantID=arguments.event.getValue('registrantID'))>
			<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrantID')#">
				<cfprocresult name="local.qryTotals" resultset="1">
				<cfprocresult name="local.qryRegTransactions" resultset="2">
				<cfprocresult name="local.qryPaymentAllocations" resultset="3">
			</cfstoredproc>
			<cfquery name="local.qryCheckRateChange" datasource="#application.dsn.membercentral.dsn#">
				select rateID
				from dbo.ev_registrants
				where registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrantID')#">
				and rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('rateID',0)#">
			</cfquery>

			<cfset local.showRateChangeScreen = false>
			<cfif val(local.qryCheckRateChange.rateID) eq 0>
				<cfquery name="local.qryDistinctPackages" dbtype="query">
					select distinct ticketPackageID, ticketName, ticketPackageName
					from [local].qryTicketDetails
				</cfquery>
				<cfquery name="local.qryOldRegistrantPackageInstances" dbtype="query">
					select count(instanceID) as quantity, ticketPackageID, includedFromRate
					from [local].qryTicketPackagesSelected
					group by ticketPackageID, includedFromRate
				</cfquery>
				<cfquery name="local.qryNewRatePackagesIncluded" datasource="#application.dsn.membercentral.dsn#">
					select ticketPackageID, quantity
					from dbo.ev_rateTicketPackages
					where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('rateID',0)#">
				</cfquery>

				<cfloop query="local.qryDistinctPackages">
					<cfquery name="local.qryThisOldRateIncPackage" dbtype="query">
						select *
						from [local].qryOldRegistrantPackageInstances
						where ticketPackageID = #local.qryDistinctPackages.ticketPackageID#
						and includedFromRate = 1
					</cfquery>
					<cfquery name="local.qryThisRegSelectedPackage" dbtype="query">
						select *
						from [local].qryOldRegistrantPackageInstances
						where ticketPackageID = #local.qryDistinctPackages.ticketPackageID#
						and includedFromRate = 0
					</cfquery>
					<cfquery name="local.qryThisNewRateIncPackage" dbtype="query">
						select *
						from [local].qryNewRatePackagesIncluded
						where ticketPackageID = #local.qryDistinctPackages.ticketPackageID#
					</cfquery>

					<cfset local.thisTicketPackageIncByOldRate = val(local.qryThisOldRateIncPackage.quantity)>
					<cfset local.thisTicketPackageSelected = val(local.qryThisRegSelectedPackage.quantity)>
					<cfset local.thisTicketPackageIncByNewRate = val(local.qryThisNewRateIncPackage.quantity)>

					<cfif local.thisTicketPackageIncByOldRate gt 0 and local.thisTicketPackageIncByNewRate gte 0 and local.thisTicketPackageIncByNewRate lt local.thisTicketPackageIncByOldRate>
						<cfset local.showRateChangeScreen = true>
						<cfif not structKeyExists(local, "singleTicketToModify")>
							<cfset local.singleTicketToModify = true>
						<cfelse>
							<cfset local.singleTicketToModify = false>
						</cfif>
					<cfelseif local.thisTicketPackageIncByNewRate gt 0 and local.thisTicketPackageSelected gt 0 and local.thisTicketPackageIncByNewRate gt local.thisTicketPackageIncByOldRate>
						<cfset local.showRateChangeScreen = true>
					</cfif>
				</cfloop>
			</cfif>
		</cfif>

		<!--- rate change detected --->
		<cfif local.registrantID gt 0 and local.showRateChangeScreen>
			<cfquery name="local.registrantPackageCustomDetails" datasource="#application.dsn.membercentral.dsn#">
				select rp.instanceID, fd.dataID, f.fieldReference, f.fieldText, f.fieldOrder, f.displayOnly, ft.displayTypeCode,
					f.fieldID, fv.valueID, ft.supportAmt, ft.supportQty, f.adminOnly,
					case when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(max))
						when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
						when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(15))
						when ft.dataTypeCode = 'BIT' then case when fv.valueBit = 1 then 'Yes' else 'No' end
						when ft.dataTypeCode = 'DATE' then convert(varchar(12), fv.valueDate, 101)
						when ft.dataTypeCode = 'DOCUMENTOBJ' then cast(fv.valueSiteResourceID as varchar(15))
						else null end as customValue
				from dbo.ev_registrantPackageInstances as rp
				inner join dbo.cf_fieldData as fd on fd.itemID = rp.instanceID 
					and fd.itemType = 'ticketPackInstCustom'
					and rp.status = 'A'
				inner join dbo.cf_fields as f on f.fieldID = fd.fieldID and f.isActive = 1
				inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and fv.valueID = fd.valueID 
				where rp.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.registrantID#">
				order by rp.instanceID, f.fieldOrder, fv.valueOrder;
			</cfquery>

			<cfset local.regInstanceStruct = {}>
			<cfloop query="local.qryTicketPackagesSelected">
				<cfset local.strTemp = {}>
				<cfquery name="local.qryThisPackageInstanceCustom" dbtype="query">
					select *
					from [local].registrantPackageCustomDetails
					where instanceID = #local.qryTicketPackagesSelected.instanceID#
					order by fieldOrder
				</cfquery>
				<cfif local.qryThisPackageInstanceCustom.recordcount>
					<cfset local.strTemp.custom = getRegistrantFieldDetails(registrantID=local.registrantID, defaultCurrencyType=local.defaultCurrencyType, tdStyle='', 
								freeRateDisplay='', qryRegTransactions=local.qryRegTransactions, qryRegistrantCustomDetails=local.registrantPackageCustomDetails, 
								qryCustom=local.qryThisPackageInstanceCustom, mode="tpcustom", isBS4=1)>
				</cfif>

				<cfset local.qryThisInstanceTicketSeatDetails = getRegistrantTicketSeatDetails(instanceID=local.qryTicketPackagesSelected.instanceID)>
				<cfset local.qryThisInstanceTicketCustomDetails = getRegistrantTicketCustomDetails(instanceID=local.qryTicketPackagesSelected.instanceID)>
				<cfset local.strTemp.arrSeats = []>
				<cfloop query="local.qryThisInstanceTicketSeatDetails">
					<cfset local.strTemp.arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow] = {}>
					<cfset local.strTemp.arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow].seatNum = local.qryThisInstanceTicketSeatDetails.currentRow>
					<cfset local.strTemp.arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow].seatID = local.qryThisInstanceTicketSeatDetails.seatID>
					<cfset local.strTemp.arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow].memberID = local.qryThisInstanceTicketSeatDetails.memberID>

					<cfquery name="local.qryThisInstanceTicketCustom" dbtype="query">
						select * 
						from [local].qryThisInstanceTicketCustomDetails
						where seatID = #local.qryThisInstanceTicketSeatDetails.seatID#
						order by fieldOrder
					</cfquery>
					<cfif local.qryThisInstanceTicketCustom.recordcount>
						<cfset local.strTemp.arrSeats[local.qryThisInstanceTicketSeatDetails.currentRow].custom = getRegistrantFieldDetails(registrantID=local.registrantID, 
									defaultCurrencyType=local.defaultCurrencyType, tdStyle='', freeRateDisplay='', qryRegTransactions=local.qryRegTransactions, 
									qryRegistrantCustomDetails=local.qryThisInstanceTicketCustomDetails, qryCustom=local.qryThisInstanceTicketCustom, mode="tcustom", isBS4=1)>
					</cfif>
				</cfloop>
				<cfset structInsert(local.regInstanceStruct, local.qryTicketPackagesSelected.instanceID, local.strTemp)>
			</cfloop>

			<cfsavecontent variable="local.data">
				<cfinclude template="eventReg_stepRateChangeTickets.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.regTicketFieldsStr = manageRegTicketFields(event=arguments.event)>
			<cfsavecontent variable="local.data">
				<cfinclude template="eventReg_stepTickets.cfm">
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="manageRegTicketSelections" access="package" output="false" returntype="string">
		<cfargument name="Event" type="any">	

		<cfscript>
			var local = structNew();
			local.registrantID = arguments.event.getValue('registrantID',0);
			local.qryTicketDetails = getTicketForReg(registrationid=arguments.event.getValue('registrationID'), rateID=arguments.event.getValue('rateID'));
			local.defaultCurrencyType = "";
			if (arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1)
				local.defaultCurrencyType = " #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#";
		</cfscript>

		<cfif local.registrantID gt 0>
			<cfset local.qryTicketPackagesSelected = getTicketPackagesSelected(registrantID=local.registrantID)>
			<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.registrantID#">
				<cfprocresult name="local.qryTotals" resultset="1">
				<cfprocresult name="local.qryRegTransactions" resultset="2">
				<cfprocresult name="local.qryPaymentAllocations" resultset="3">
			</cfstoredproc>
		</cfif>

		<cfset local.regTicketFieldsStr = manageRegTicketFields(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="eventReg_stepTickets.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="manageRegTicketFields" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">	

		<cfset var local = structNew()>
		<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event")>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		
		<cfset local.qryTicketDetails = getTicketForReg(registrationid=arguments.event.getValue('registrationID'), rateID=arguments.event.getValue('rateID'))>
		<cfif arguments.event.getValue('registrantID',0) gt 0>
			<cfset local.qryTicketPackagesSelected = getTicketPackagesSelected(registrantID=arguments.event.getValue('registrantID'))>
		</cfif>
		<!--- put the selected packages into a struct --->
		<cfset local.formRegPackages = structNew()>
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
			<cfif (left(local.thisField,14) eq "ticketPackage_" and arguments.event.getTrimValue(local.thisField) gt 0) or (left(local.thisField,20) eq "ticketPackageCredit_" and left(arguments.event.getTrimValue(local.thisField),10) eq 'createNew_')>
				<cfif not structKeyExists(local.formRegPackages,GetToken(local.thisField,2,'_'))>
					<cfset structInsert(local.formRegPackages, GetToken(local.thisField,2,'_'), 0, true)>
				</cfif>
				<cfif left(local.thisField,14) eq "ticketPackage_" and arguments.event.getTrimValue(local.thisField) gt 0>
					<cfset local.thisRegSelection = arguments.event.getTrimValue(local.thisField)>
				<cfelse>
					<cfset local.thisRegSelection = 1>
				</cfif>
				<cfset local.formRegPackages[GetToken(local.thisField,2,'_')] = local.formRegPackages[GetToken(local.thisField,2,'_')] + local.thisRegSelection>
			</cfif>
		</cfloop>

		<!--- we should show this step if any ticket selected tracks guests, has ticket custom fields, or package custom fields --->
		<cfset local.showTicketFields = false>
		<cfset local.arrPackages = arrayNew(1)>
		<cfquery name="local.qryTicketDetailsDistinct" dbtype="query">
			select distinct ticketID, ticketPackageID, ticketAssignSeats, ticketsInPackage, ticketName, ticketPackageName, qtyIncludedByRate
			from [local].qryTicketDetails
		</cfquery>
		
		<cfloop query="local.qryTicketDetailsDistinct">
			<cfset local.thisPackageCustomFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', 
														areaName='TicketPackage', csrid=local.eventAdminSiteResourceID, detailID=local.qryTicketDetailsDistinct.ticketPackageID, hideAdminOnly=0)>
			<cfset local.thisPackageTicketCustomFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', 
																areaName='Ticket', csrid=local.eventAdminSiteResourceID, detailID=local.qryTicketDetailsDistinct.ticketID, hideAdminOnly=0)>
		
			<cfset local.tmpStr = { ticketPackageID=local.qryTicketDetailsDistinct.ticketPackageID, 
									ticketID=local.qryTicketDetailsDistinct.ticketID, 
									ticketName=local.qryTicketDetailsDistinct.ticketName,
									ticketAssignSeats=local.qryTicketDetailsDistinct.ticketAssignSeats,
									hasPackageFields="#arrayLen(xmlParse(local.thisPackageCustomFieldsXML.returnXML).xmlRoot.xmlChildren) gt 0#",
									hasTicketFields="#arrayLen(xmlParse(local.thisPackageTicketCustomFieldsXML.returnXML).xmlRoot.xmlChildren) gt 0#",
									packageName="#local.qryTicketDetailsDistinct.ticketName# - #local.qryTicketDetailsDistinct.ticketPackageName#",
									ticketsInPackage=local.qryTicketDetailsDistinct.ticketsInPackage,
									instances=0,
									qtyIncludedByRate=val(local.qryTicketDetailsDistinct.qtyIncludedByRate) }>
			
			<cfif arguments.event.getValue('registrantID',0) gt 0>
				<cfquery name="local.qryThisRegPackageInstance" dbtype="query">
					select instanceID, ticketPackageID
					from [local].qryTicketPackagesSelected
					where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
					and includedFromRate = 0
					order by instanceID
				</cfquery>
				<cfset local.tmpStr.instances = local.qryThisRegPackageInstance.recordCount>
				<cfloop query="local.qryThisRegPackageInstance">
					<cfif left(arguments.event.getValue('ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#',''),10) eq 'createNew_' or val(arguments.event.getValue('ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#','')) gt 0>
						<cfset local.tmpStr.instances = local.tmpStr.instances - 1>
					</cfif>
				</cfloop>
				
				<cfquery name="local.qryThisRegPackageInstanceRateIncluded" dbtype="query">
					select instanceID, ticketPackageID
					from [local].qryTicketPackagesSelected
					where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
					and includedFromRate = 1
					order by instanceID
				</cfquery>
				<cfif local.qryThisRegPackageInstanceRateIncluded.recordCount gt local.tmpStr.qtyIncludedByRate>
					<cfset local.tmpStr.instances = local.tmpStr.instances + (local.qryThisRegPackageInstanceRateIncluded.recordCount - local.tmpStr.qtyIncludedByRate)>
				</cfif>
			</cfif>
			
			<cfif structKeyExists(local.formRegPackages,local.qryTicketDetailsDistinct.ticketPackageID)>
				<cfset local.tmpStr.instances = local.tmpStr.instances + local.formRegPackages[local.qryTicketDetailsDistinct.ticketPackageID] + local.tmpStr.qtyIncludedByRate>
			<cfelseif local.tmpStr.qtyIncludedByRate gt 0>
				<cfset local.tmpStr.instances = local.tmpStr.instances + local.tmpStr.qtyIncludedByRate>
			</cfif>
			<cfif local.tmpStr.instances gt 0 and (local.tmpStr.ticketAssignSeats is 1 or local.tmpStr.hasPackageFields is 1 or local.tmpStr.hasTicketFields is 1)>
				<cfset local.showTicketFields = true>
			</cfif>
			<cfset arrayAppend(local.arrPackages, local.tmpStr)>
		</cfloop>

		<cfset local.data = structNew()>
		<cfset local.data['arrPackages'] = local.arrPackages>
		<cfset local.data['showTicketFields'] = local.showTicketFields>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="loadRegConfirmation" access="package" output="false" returntype="string">
		<cfargument name="Event" type="any">	

		<cfscript>
			var local = structNew();
			local.objEvents = CreateObject("component","model.events.events");
			local.objAdminEvent	= CreateObject("component","model.admin.events.event");
			local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
			local.strEvent = local.objEvents.getEvent(eventID=arguments.event.getValue('eventID',0), siteID=arguments.event.getValue('mc_siteinfo.siteid'), languageID=1);

			local.registrantID = arguments.event.getValue('registrantID',0);
			local.rateID = arguments.event.getValue('rateid',0);
			local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.defaultCurrencyType = "";
			if (arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1)
				local.defaultCurrencyType = " #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#";
		</cfscript>

		<cfset local.memberID = application.objMember.getActiveMemberID(val(arguments.event.getValue('mid')))>
		<cfset local.qryCurrentRegMember = getRegistrantInfo(mid=local.memberID)>
		<cfset local.registrantEmail = local.qryCurrentRegMember.email>
		<cfset local.registrantOverrideEmail = arguments.event.getTrimValue('regEmail','')>
		<cfif len(local.registrantOverrideEmail) AND local.registrantEmail EQ local.registrantOverrideEmail>
			<cfset local.registrantOverrideEmail = "">
		</cfif>
		<cfif (len(local.registrantEmail) or len(local.registrantOverrideEmail)) and local.strEvent.qryEventMeta.calendarDefRegConfirmation is 1>		
			<cfset local.chkEmailRegistrant = 1>
		<cfelse>
			<cfset local.chkEmailRegistrant = 0>
		</cfif>
		<cfif len(local.strEvent.qryEventRegMeta.notifyEmail) and local.strEvent.qryEventMeta.calendarDefStaffConfirmation is 1>
			<cfset local.chkEmailRegistrant2 = 1>
		<cfelse>
			<cfset local.chkEmailRegistrant2 = 0>
		</cfif>

		<cfset local.qryRate = getRegRateByRateID(rateid=local.rateID)>
		<cfset local.rateMessage = trim(local.qryRate.rateMessage)>
		<cfset local.rateName = "#local.strEvent.qryEventMeta.eventContentTitle# - #local.qryRate.rateName#">
		<cfset local.rateAmount = local.qryRate.rate>

		<cfset local.hasRateIDChanged = 0>
		<cfset local.hasAppliedCoupon = 0>
		<cfset local.appliedCouponCode = ''>
		<cfset local.appliedDiscountAmt = 0>		
		
		<cfif local.registrantID gt 0>
			<cfset local.strRegistrantRate = local.objAdminEvent.getRegistrantRateIDAndFee(registrantID=local.registrantID)>
			<cfif local.strRegistrantRate.rateID eq local.rateID>
				<cfset local.rateAmount = local.strRegistrantRate.rateFee>
			<cfelse>
				<cfset local.hasRateIDChanged = 1>
			</cfif>

			<cfquery name="local.qryAppliedCouponToTrans" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				declare @orgID int = <cfqueryparam value="#val(arguments.event.getValue('mc_siteinfo.orgID'))#" cfsqltype="CF_SQL_INTEGER">;
				declare @siteID int = <cfqueryparam value="#val(arguments.event.getValue('mc_siteinfo.siteID'))#" cfsqltype="CF_SQL_INTEGER">;

				select top 1 d.couponID, c.couponCode, t.amount
				from dbo.tr_transactionDiscounts d
				inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = d.transactionID and t.statusID = 1
				inner join dbo.tr_coupons c on c.siteID = @siteID and c.couponID = d.couponID
				where d.orgID = @orgID
				and d.isActive = 1
				and d.itemType = 'EventRate'
				and d.itemID = <cfqueryparam value="#arguments.event.getValue('registrantID',0)#" cfsqltype="CF_SQL_INTEGER">;
			</cfquery>

			<cfif local.qryAppliedCouponToTrans.recordCount>
				<cfset local.hasAppliedCoupon = 1>
				<cfset local.appliedCouponID = local.qryAppliedCouponToTrans.couponID>
				<cfset local.appliedCouponCode = local.qryAppliedCouponToTrans.couponCode>
				<cfset local.appliedDiscountAmt = local.qryAppliedCouponToTrans.amount>
			</cfif>
		</cfif>
		<cfif local.rateAmount eq 0>
			<cfset local.ratePriceDisplay = local.qryRate.freeRateDisplay>
		<cfelse>
			<cfset local.ratePriceDisplay = "#dollarformat(local.rateAmount)##local.defaultCurrencyType#">
		</cfif>

		<cfset local.hasQualifiedCoupons = 0>
		<cfif not local.hasAppliedCoupon and local.rateID>
			<cfxml variable="local.cartItemsXML">
				<cfoutput>
					<cart>
						<item mid="#local.memberID#" rateid="#local.rateID#" itemtype="event" />
					</cart>
				</cfoutput>
			</cfxml>
			<cfset local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<cfstoredproc procedure="tr_hasValidCoupons" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="Events">
				<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR " value="#local.cartItemsXML#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocparam type="OUT" cfsqltype="CF_SQL_BIT" variable="local.hasQualifiedCoupons">
			</cfstoredproc>
		</cfif>

		<cfset local.qryCustomDetails = QueryNew("fieldID,displayTypeCode,supportAmt,confTitle,confVal,confQty,confAmt,qty,qtyAmtList,totalAmt,overrideFieldName","integer,varchar,bit,varchar,varchar,varchar,varchar,integer,varchar,double,varchar")>

		<cfif arguments.event.getTrimValue('evRegistrantRoles','') NEQ ''>
			<cfset local.strRoleCustomFields = structNew()>
			<cfquery name="local.qryRegistrantRoles" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare @controllingSiteResourceID int, @catTreeID int;
				select @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.eventAdminSiteResourceID#">;
				select @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@controllingSiteResourceID, 'Event Roles');
				
				select categoryID, categoryName
				from cms_categories
				where categoryTreeID = @catTreeID
				and categoryID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.event.getValue('evRegistrantRoles')#">)
				and isActive = 1 and parentCategoryID is NULL
				order by categoryName;
			</cfquery>

			<cfloop query="local.qryRegistrantRoles">
				<cfset local.strRoleCustomFields[local.qryRegistrantRoles.categoryName] = { qryCustomDetails=duplicate(local.qryCustomDetails) }>
				<cfset local.qryEventRoleFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='EventAdmin', areaName='Role', csrid=local.eventAdminSiteResourceID, detailID=local.qryRegistrantRoles.categoryID, hideAdminOnly=0)>
				<cfset local.xmlEventRoleCustomFields = xmlParse(local.qryEventRoleFieldsXML.returnXML).xmlRoot>
				<cfif arrayLen(local.xmlEventRoleCustomFields.xmlChildren)>
					<cfset local.tmpStr = structNew()>
					<cfloop array="#local.xmlEventRoleCustomFields.xmlChildren#" index="local.thisfield">
						<cfset local.tmpAtt = local.thisfield.xmlattributes>
						<cfset structInsert(local.tmpStr,local.tmpAtt.fieldID,arguments.event.getValue('cf_#local.tmpAtt.fieldID#_',''))>

						<cfif len(local.tmpStr[local.tmpAtt.fieldID])>
							<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpAtt.displayTypeCode)>
								<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
									<cfif listFind(local.tmpStr[local.tmpAtt.fieldID],local.thisoption.xmlAttributes.valueID) and QueryAddRow(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails)>
										<cfset QuerySetCell(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
										<cfset QuerySetCell(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
										<cfset QuerySetCell(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
										<cfset QuerySetCell(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails,"confVal",local.thisoption.xmlattributes.fieldValue)>
									</cfif>
								</cfloop>
							<cfelseif QueryAddRow(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails)>
								<cfset QuerySetCell(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
								<cfset QuerySetCell(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
								<cfset QuerySetCell(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
								<cfset QuerySetCell(local.strRoleCustomFields[local.qryRegistrantRoles.categoryName].qryCustomDetails,"confVal",local.tmpStr[local.tmpAtt.fieldID])>
							</cfif>
						</cfif>
					</cfloop>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.strCustomFields = structNew()>
		<cfset local.regCustomFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', areaName='Registrant', 
											csrid=local.strEvent.qryEventMeta.siteResourceID, detailID=0, hideAdminOnly=0)>
		<cfset local.regCustomFields = xmlParse(local.regCustomFieldsXML.returnXML).xmlRoot>
		<cfset local.strCustomFields = { qryCustomDetails=duplicate(local.qryCustomDetails) }>
		
		<cfif arrayLen(local.regCustomFields.xmlChildren)>
			<cfset local.tmpStr = structNew()>
			<cfset local.tmpQtyRemoveStr = structNew()>
			<cfset local.tmpNewQtyValueStr = structNew()>
			<cfloop array="#local.regCustomFields.xmlChildren#" index="local.thisfield">
				<cfset local.tmpAtt = local.thisfield.xmlattributes>
				<cfset structInsert(local.tmpStr,local.tmpAtt.fieldID,arguments.event.getValue('cf_#local.tmpAtt.fieldID#_',''))>
				
				<cfif local.registrantID gt 0 and local.tmpAtt.displayTypeCode is 'TEXTBOX' and local.tmpAtt.supportAmt is 1 and local.tmpAtt.supportQty is 1>
					<cfset structInsert(local.tmpNewQtyValueStr,local.tmpAtt.fieldID,val(local.tmpStr[local.tmpAtt.fieldID]))>
					<cfset local.thisQtyFieldAmtList = "">
					<cfset local.removeQTY = 0>
					<cfset local.totalQtyAmount = 0>

					<cfif len(arguments.event.getTrimValue('cfqty_#local.tmpAtt.fieldID#_',''))>
						<cfset structInsert(local.tmpQtyRemoveStr,local.tmpAtt.fieldID,arguments.event.getTrimValue('cfqty_#local.tmpAtt.fieldID#_'))>
						<cfset local.removeQTY = ListLen(arguments.event.getTrimValue('cfqty_#local.tmpAtt.fieldID#_'))>
						<cfset local.removeQtyList = arguments.event.getTrimValue('cfqty_#local.tmpAtt.fieldID#_')>
					</cfif>

					<cfset local.qtyIDs = local.objResourceCustomFields.getFieldQTYDetails(itemType='eventRegCustom', itemID=local.registrantID, fieldID=local.tmpAtt.fieldID, applicationType='Events', trItemType='custom')>
					<cfif local.removeQTY gt 0>
						<cfquery name="local.thisQtyFieldAmt" dbtype="query">
							select amount
							from [local].qtyIDs
							where subItemID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.removeQtyList#">)
						</cfquery>
						<cfset local.existingQtyFieldAmtList = valueList(local.thisQtyFieldAmt.amount)>
					<cfelse>
						<cfset local.existingQtyFieldAmtList = valueList(local.qtyIDs.amount)>
					</cfif>

					<cfset local.tmpStr[local.tmpAtt.fieldID] = val(local.tmpStr[local.tmpAtt.fieldID]) + ListLen(valueList(local.qtyIDs.subItemID)) - local.removeQTY>
					<cfloop from="1" to="#local.tmpStr[local.tmpAtt.fieldID]#" index="local.thisQty">
						<cfif local.thisQty lte listLen(local.existingQtyFieldAmtList)>
							<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,GetToken(local.existingQtyFieldAmtList,local.thisQty,','))>
							<cfset local.totalQtyAmount = local.totalQtyAmount + GetToken(local.existingQtyFieldAmtList,local.thisQty,',')>
						<cfelse>
							<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,abs(local.tmpAtt.amount))>
							<cfset local.totalQtyAmount = local.totalQtyAmount + abs(local.tmpAtt.amount)>
						</cfif>
					</cfloop>
				</cfif>

				<cfif len(local.tmpStr[local.tmpAtt.fieldID])>
					<cfif local.tmpAtt.displayTypeCode is 'TEXTBOX' and local.tmpAtt.supportQty is 1 and local.tmpAtt.supportAmt is 1 and val(local.tmpStr[local.tmpAtt.fieldID]) gt 0 and QueryAddRow(local.strCustomFields.qryCustomDetails)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"supportAmt",1)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confVal","")>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"qty",val(local.tmpStr[local.tmpAtt.fieldID]))>
						<cfif local.registrantID gt 0>
							<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confQty","QTY: #val(local.tmpStr[local.tmpAtt.fieldID])#")>
							<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confAmt","#dollarformat(local.totalQtyAmount)##local.defaultCurrencyType#")>
							<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"qtyAmtList",local.thisQtyFieldAmtList)>
							<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"totalAmt","#local.totalQtyAmount#")>
						<cfelse>
							<cfset local.thisQtyFieldAmtList = "">
							<cfif val(local.tmpStr[local.tmpAtt.fieldID]) gt 0>
								<cfloop from="1" to="#local.tmpStr[local.tmpAtt.fieldID]#" index="local.thisQty">
									<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,abs(local.tmpAtt.amount))>
								</cfloop>
							</cfif>
							<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confQty","#val(local.tmpStr[local.tmpAtt.fieldID])# x #dollarformat(abs(local.tmpAtt.amount))#")>
							<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confAmt","#dollarformat(val(local.tmpStr[local.tmpAtt.fieldID]) * abs(local.tmpAtt.amount))##local.defaultCurrencyType#")>
							<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"qtyAmtList",local.thisQtyFieldAmtList)>
							<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"totalAmt","#val(local.tmpStr[local.tmpAtt.fieldID]) * abs(local.tmpAtt.amount)#")>
						</cfif>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"overrideFieldName","override_cf_#local.tmpAtt.fieldID#_")>
					<cfelseif local.tmpAtt.displayTypeCode is 'TEXTBOX' and local.tmpAtt.supportQty is 0 and local.tmpAtt.supportAmt is 1 and (val(ReReplace(local.tmpStr[local.tmpAtt.fieldID],'[^0-9\.]','','ALL')) is not 0) and QueryAddRow(local.strCustomFields.qryCustomDetails)>
						<cfset local.thisAmtVal = val(ReReplace(local.tmpStr[local.tmpAtt.fieldID],'[^0-9\.]','','ALL'))>
						<cfif local.registrantID gt 0>
							<cfset local.thisValue = local.objResourceCustomFields.getFieldResponseEntered(itemType='eventRegCustom', itemID=local.registrantID, fieldID=local.tmpAtt.fieldID)>
							<cfset local.thisValueActualFee = local.objResourceCustomFields.getFieldActualFee(itemType='eventRegCustom', itemID=local.registrantID, fieldID=local.tmpAtt.fieldID, applicationType='Events', trItemType='custom')>
							<cfif local.thisValue eq local.thisAmtVal>
								<cfset local.thisAmtVal = local.thisValueActualFee>
							</cfif>
						</cfif>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"supportAmt",1)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confVal","")>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confQty","")>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confAmt","#dollarformat(local.thisAmtVal)##local.defaultCurrencyType#")>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"qty",0)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"totalAmt",local.thisAmtVal)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"overrideFieldName","override_cf_#local.tmpAtt.fieldID#_")>
					<cfelseif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpAtt.displayTypeCode)>
						<cfif local.registrantID gt 0>
							<cfset local.thisOptionsSelected = local.objResourceCustomFields.getFieldOptionsSelected(itemType='eventRegCustom', itemID=local.registrantID, fieldID=local.tmpAtt.fieldID)>
						</cfif>
						<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
							<cfif listFind(local.tmpStr[local.tmpAtt.fieldID],local.thisoption.xmlAttributes.valueID) and QueryAddRow(local.strCustomFields.qryCustomDetails)>
								<cfset local.thisOptionAmount = 0>
								<cfif isDefined("local.thisoption.xmlAttributes.amount") and local.thisoption.xmlattributes.amount is not 0>
									<cfset local.thisOptionAmount = local.thisoption.xmlattributes.amount>
								</cfif>
								<cfif local.registrantID gt 0 and len(local.thisOptionsSelected) and listFind(local.thisOptionsSelected,local.thisoption.xmlAttributes.valueID)>
									<cfset local.thisOptionAmount = local.objResourceCustomFields.getFieldOptionActualFee(itemType='eventRegCustom', itemID=local.registrantID, fieldID=local.tmpAtt.fieldID, valueID=local.thisoption.xmlAttributes.valueID, applicationType='Events', trItemType='custom')>
								</cfif>
								<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
								<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
								<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"supportAmt",local.tmpAtt.supportAmt)>
								<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
								<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confVal",local.thisoption.xmlattributes.fieldValue)>
								<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confQty","")>
								<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"overrideFieldName","override_cf_#local.tmpAtt.fieldID#_#local.thisoption.xmlAttributes.valueID#_")>
								<cfif local.thisOptionAmount gt 0>
									<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confAmt","#DollarFormat(local.thisOptionAmount)##local.defaultCurrencyType#")>
									<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"qty",0)>
									<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"totalAmt",local.thisOptionAmount)>
								<cfelse>
									<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confAmt","")>
									<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"totalAmt",0)>
								</cfif>
							</cfif>
						</cfloop>
					<cfelseif QueryAddRow(local.strCustomFields.qryCustomDetails)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"supportAmt",local.tmpAtt.supportAmt)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confVal",local.tmpStr[local.tmpAtt.fieldID])>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confQty","")>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"confAmt","")>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"qty",0)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"totalAmt",0)>
						<cfset QuerySetCell(local.strCustomFields.qryCustomDetails,"overrideFieldName","override_cf_#local.tmpAtt.fieldID#_")>
					</cfif>
				</cfif>
			</cfloop>
		</cfif>

		<!--- total reg fee calc --->
		<cfset local.totalRegFee = val(local.rateAmount)>

		<cfset local.regFieldsTotal = 0>
		<cfif local.strCustomFields.qryCustomDetails.recordCount>
			<cfset local.qryThisAreaCustom = local.strCustomFields.qryCustomDetails>
			<cfquery name="local.qryCustomTotal" dbtype="query">
				select sum(totalAmt) as totalAmt from [local].qryThisAreaCustom
			</cfquery>
			<cfset local.regFieldsTotal = val(local.qryCustomTotal.totalAmt)>
			<cfset local.totalRegFee = local.totalRegFee + local.regFieldsTotal>
		</cfif>

		<!--- tickets info --->
		<cfset local.ticketsTotal = 0>

		<cfset local.strTicketDetails = getTicketDetailsForConfirmation(event=arguments.event, registrationID=arguments.event.getValue('registrationID',0), registrantID=local.registrantID, 
																		rateID=arguments.event.getValue('rateid',0), eventAdminSiteResourceID=local.eventAdminSiteResourceID, 
																		objResourceCustomFields=local.objResourceCustomFields, displayedCurrencyType=local.defaultCurrencyType)>

		<cfif local.strTicketDetails.qrySelectedTickets.recordcount>
			<cfquery name="local.qrySelectedTicketTotals" dbtype="query">
				select sum(totalAmt) as totalAmt from [local].strTicketDetails.qryTicketTotals
			</cfquery>
			<cfset local.ticketsTotal = val(local.qrySelectedTicketTotals.totalAmt)>
			<cfset local.totalRegFee = local.totalRegFee + local.ticketsTotal>
		</cfif>

		<cfset local.totalRegFeeConfAmt = "#DollarFormat(local.totalRegFee)##local.defaultCurrencyType#">

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfinclude template="eventReg_stepFinal.cfm">
			<cfinclude template="eventReg_stepConfirmation.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getTicketDetailsForConfirmation" access="private" output="no" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="registrationID" type="numeric" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="eventAdminSiteResourceID" type="numeric" required="yes">
		<cfargument name="objResourceCustomFields" type="any" required="yes">
		<cfargument name="displayedCurrencyType" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.qryTicketDetails = getTicketForReg(registrationid=arguments.registrationID, rateID=arguments.rateID)>		
		
		<!--- no tickets defined --->
		<cfif not local.qryTicketDetails.recordcount>
			<cfset local.strTicketDetails = { qrySelectedTickets=QueryNew("ticketID","integer"), selectedTicketStr=structNew(), containsNonDeletedPackage=false }>
			<cfreturn local.strTicketDetails>
		</cfif>

		<cfset local.containsNonDeletedPackage = false>
		<cfset local.qryTicketPackagesSelected = getTicketPackagesSelected(registrantID=arguments.registrantID)>
		<cfset local.formRegPackages = structNew()>
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
			<cfif (left(local.thisField,14) eq "ticketPackage_" and arguments.event.getTrimValue(local.thisField) gt 0) or (left(local.thisField,20) eq "ticketPackageCredit_" and left(arguments.event.getTrimValue(local.thisField),10) eq 'createNew_')>
				<cfif not structKeyExists(local.formRegPackages,GetToken(local.thisField,2,'_'))>
					<cfset structInsert(local.formRegPackages, GetToken(local.thisField,2,'_'), 0, true)>
				</cfif>
				<cfif left(local.thisField,14) eq "ticketPackage_" and arguments.event.getTrimValue(local.thisField) gt 0>
					<cfset local.thisRegSelection = arguments.event.getTrimValue(local.thisField)>
				<cfelse>
					<cfset local.thisRegSelection = 1>
				</cfif>
				<cfset local.formRegPackages[GetToken(local.thisField,2,'_')] = local.formRegPackages[GetToken(local.thisField,2,'_')] + local.thisRegSelection>
			</cfif>
		</cfloop>

		<cfif arguments.registrantID gt 0>
			<!--- get all existing transactions for this registration --->
			<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
				<cfprocresult name="local.qryTotals" resultset="1">
				<cfprocresult name="local.qryRegTransactions" resultset="2">
				<cfprocresult name="local.qryPaymentAllocations" resultset="3">
			</cfstoredproc>
		</cfif>

		<cfset local.arrPackagesSelected = arrayNew(1)>
		<cfquery name="local.qryTicketDetailsDistinct" dbtype="query">
			select distinct ticketID, ticketPackageID, ticketAssignSeats, ticketsInPackage, ticketName, ticketPackageName, qtyIncludedByRate, GLAccountID
			from [local].qryTicketDetails
		</cfquery>
		
		<cfloop query="local.qryTicketDetailsDistinct">
			<cfquery name="local.qryTicketPackageAvailablePriceID" dbtype="query">
				select availablePriceID
				from [local].qryTicketDetails
				where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
			</cfquery>

			<cfset local.thisPackageCustomFieldsXML = arguments.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', areaName='TicketPackage', csrid=arguments.eventAdminSiteResourceID, detailID=local.qryTicketDetailsDistinct.ticketPackageID, hideAdminOnly=0)>
			<cfset local.thisPackageTicketCustomFieldsXML = arguments.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', areaName='Ticket', csrid=arguments.eventAdminSiteResourceID, detailID=local.qryTicketDetailsDistinct.ticketID, hideAdminOnly=0)>

			<cfset local.strPackageCustomFieldsXML[local.qryTicketDetailsDistinct.ticketPackageID] = xmlParse(local.thisPackageCustomFieldsXML.returnXML).xmlRoot>
			<cfset local.strTicketCustomFieldsXML[local.qryTicketDetailsDistinct.ticketID] = xmlParse(local.thisPackageTicketCustomFieldsXML.returnXML).xmlRoot>
		
			<cfset local.tmpStr = { ticketPackageID=local.qryTicketDetailsDistinct.ticketPackageID, 
									ticketID=local.qryTicketDetailsDistinct.ticketID, 
									ticketAssignSeats=local.qryTicketDetailsDistinct.ticketAssignSeats,
									hasPackageFields="#arrayLen(local.strPackageCustomFieldsXML[local.qryTicketDetailsDistinct.ticketPackageID].xmlChildren) gt 0#",
									hasTicketFields="#arrayLen(local.strTicketCustomFieldsXML[local.qryTicketDetailsDistinct.ticketID].xmlChildren) gt 0#",
									packageName="#local.qryTicketDetailsDistinct.ticketName# - #local.qryTicketDetailsDistinct.ticketPackageName#",
									ticketsInPackage=local.qryTicketDetailsDistinct.ticketsInPackage,
									ticketPackageAvailablePriceIDList=valueList(local.qryTicketPackageAvailablePriceID.availablePriceID),
									instances=0,
									deletedInstanceCount=0,
									instanceArray=arrayNew(1),
									qtyIncludedByRate=val(local.qryTicketDetailsDistinct.qtyIncludedByRate) }>
			
			<cfquery name="local.qryThisRegPackageInstance" dbtype="query">
				select instanceID, ticketPackageID
				from [local].qryTicketPackagesSelected
				where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
				and includedFromRate = 0
				order by instanceID
			</cfquery>
			<cfset local.tmpStr.instances = local.qryThisRegPackageInstance.recordCount>
			<cfloop query="local.qryThisRegPackageInstance">
				<cfif left(arguments.event.getValue('ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#',''),10) eq 'createNew_' or val(arguments.event.getValue('ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#','')) gt 0>
					<cfset local.tmpStr.instances = local.tmpStr.instances - 1>
				</cfif>
			</cfloop>
			
			<cfquery name="local.qryThisRegPackageInstanceRateIncluded" dbtype="query">
				select instanceID
				from [local].qryTicketPackagesSelected
				where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
				and includedFromRate = 1
				order by instanceID
			</cfquery>
			<cfif local.qryThisRegPackageInstanceRateIncluded.recordCount gt local.tmpStr.qtyIncludedByRate>
				<cfset local.tmpStr.instances = local.tmpStr.instances + (local.qryThisRegPackageInstanceRateIncluded.recordCount - local.tmpStr.qtyIncludedByRate)>
			</cfif>
			
			<cfif structKeyExists(local.formRegPackages,local.qryTicketDetailsDistinct.ticketPackageID)>
				<cfset local.tmpStr.instances = local.tmpStr.instances + local.formRegPackages[local.qryTicketDetailsDistinct.ticketPackageID] + local.tmpStr.qtyIncludedByRate>
			<cfelseif local.tmpStr.qtyIncludedByRate gt 0>
				<cfset local.tmpStr.instances = local.tmpStr.instances + local.tmpStr.qtyIncludedByRate>
			</cfif>
			<cfif local.tmpStr.instances gt 0>
				<cfset local.tmpStr.thisPackageInstanceNumList = "">
				<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
					<cfif left(local.thisField,16) eq "packageInstance_" and ListLen(local.thisField,"_") eq 4 and GetToken(local.thisField,2,'_') eq local.qryTicketDetailsDistinct.ticketPackageID and val(GetToken(local.thisField,4,'_')) gt 0>
						<cfset local.tmpStr.thisPackageInstanceNumList = ListAppend(local.tmpStr.thisPackageInstanceNumList,GetToken(local.thisField,4,'_'))>
					</cfif>
				</cfloop>
				<cfset local.tmpStr.thisPackageInstanceNumList = listSort(local.tmpStr.thisPackageInstanceNumList,'numeric','asc')>

				<cfquery name="local.qryThisPackageInstance" dbtype="query">
					select *
					from [local].qryTicketPackagesSelected
					where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
					order by instanceID
				</cfquery>
				<cfset local.tmpStr.includedFromRateCount = 0>
				<cfloop from="1" to="#local.tmpStr.instances#" index="local.thisInstanceNum">
					<cfset local.tmpInstanceStruct = { instanceID=0, includedFromRate=0, prevIncludedFromRate=0, availablePriceID=0, qryInstanceSeats=QueryNew("seatID,memberID","integer,integer") }>
					
					<cfset local.instanceIDList = listSort(valueList(local.qryThisPackageInstance.instanceID),'numeric','asc')>

					<cfif ListLen(local.instanceIDList) gte local.thisInstanceNum>
						<cfset local.tmpInstanceStruct.instanceID = GetToken(local.instanceIDList,local.thisInstanceNum,",")>

						<cfquery name="local.qryThisInstanceDetails" dbtype="query">
							select includedFromRate, availablePriceID
							from [local].qryTicketPackagesSelected
							where instanceID = #val(local.tmpInstanceStruct.instanceID)#
						</cfquery>

						<cfset local.tmpInstanceStruct.includedFromRate = local.qryThisInstanceDetails.includedFromRate>
						<cfset local.tmpInstanceStruct.availablePriceID = local.qryThisInstanceDetails.availablePriceID>
						<cfset local.tmpInstanceStruct.prevIncludedFromRate = local.qryThisInstanceDetails.includedFromRate>
						<cfset local.tmpStr.includedFromRateCount = local.tmpStr.includedFromRateCount + local.tmpInstanceStruct.includedFromRate>
						<cfquery name="local.tmpInstanceStruct.qryInstanceSeats" datasource="#application.dsn.membercentral.dsn#">
							select seatID, memberID
							from dbo.ev_registrantPackageInstanceSeats
							where instanceID = <cfqueryparam value="#local.tmpInstanceStruct.instanceID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
					</cfif>
					
					<cfset local.tmpInstanceStruct.isDeletedInstance = 0>
					<cfif local.tmpInstanceStruct.instanceID gt 0 and arguments.event.getValue('removeTicketPackageInstance_#local.tmpInstanceStruct.instanceID#',0) is 1>
						<cfset local.tmpInstanceStruct.isDeletedInstance = 1>
						<cfset local.tmpStr.deletedInstanceCount = local.tmpStr.deletedInstanceCount + 1>
					<cfelseif not local.containsNonDeletedPackage>
						<cfset local.containsNonDeletedPackage = true>
					</cfif>

					<cfif local.tmpInstanceStruct.includedFromRate eq 1 and local.tmpStr.includedFromRateCount gt local.tmpStr.qtyIncludedByRate>
						<cfset local.tmpInstanceStruct.includedFromRate = 0>
						<cfset local.tmpStr.includedFromRateCount = local.tmpStr.includedFromRateCount - 1>
						<cfif val(arguments.event.getValue('ticketPackageDebit_#local.qryTicketDetailsDistinct.ticketPackageID#_#local.tmpInstanceStruct.instanceID#',0)) gt 0>
							<cfset local.tmpInstanceStruct.availablePriceID = arguments.event.getValue('ticketPackageDebit_#local.qryTicketDetailsDistinct.ticketPackageID#_#local.tmpInstanceStruct.instanceID#')>
						</cfif>
					<cfelseif local.tmpInstanceStruct.includedFromRate eq 0 and local.tmpStr.includedFromRateCount lt local.tmpStr.qtyIncludedByRate>
						<cfif local.tmpInstanceStruct.instanceID gt 0 and val(arguments.event.getTrimValue('ticketPackageCredit_#local.qryTicketDetailsDistinct.ticketPackageID#_#local.thisInstanceNum#','')) gt 0>
							<cfset local.tmpInstanceStruct.includedFromRate = 1>
							<cfset local.tmpStr.includedFromRateCount = local.tmpStr.includedFromRateCount + 1>
						<cfelseif local.tmpInstanceStruct.instanceID eq 0>
							<cfset local.tmpInstanceStruct.includedFromRate = 1>
							<cfset local.tmpStr.includedFromRateCount = local.tmpStr.includedFromRateCount + 1>
						</cfif>
					</cfif>
					
					<cfif local.tmpInstanceStruct.includedFromRate eq 0 and local.tmpInstanceStruct.availablePriceID eq 0>
						<cfif ListLen(local.tmpStr.thisPackageInstanceNumList) gte local.thisInstanceNum>
							<cfset local.thisFormInstanceNum = GetToken(local.tmpStr.thisPackageInstanceNumList,local.thisInstanceNum,",")>
							<cfset local.tmpInstanceStruct.availablePriceID = arguments.event.getValue('tpiAvailablePriceID_#local.qryTicketDetailsDistinct.ticketPackageID#_#local.thisFormInstanceNum#',0)>
						</cfif>
					</cfif>

					<cfset local.tmpInstanceStruct.packageAmount = 0>
					<cfif local.tmpInstanceStruct.includedFromRate eq 0 and local.tmpInstanceStruct.availablePriceID gt 0>
						<cfif local.tmpInstanceStruct.instanceID gt 0 and local.tmpInstanceStruct.includedFromRate eq local.tmpInstanceStruct.prevIncludedFromRate>
							<cfquery name="local.qryThisTicketPackagePrice" dbtype="query">
								select sum(amount) as amount
								from [local].qryRegTransactions
								where itemType = 'TicketPackInst'
								and itemID = #val(local.tmpInstanceStruct.instanceID)#
							</cfquery>
						<cfelse>
							<cfquery name="local.qryThisTicketPackagePrice" dbtype="query">
								select ticketPackageAmount as amount
								from [local].qryTicketDetails
								where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
								and availablePriceID = #val(local.tmpInstanceStruct.availablePriceID)#
							</cfquery>
						</cfif>
						<cfset local.tmpInstanceStruct.packageAmount = val(local.qryThisTicketPackagePrice.amount)>
					</cfif>
					<cfset arrayAppend(local.tmpStr.instanceArray,local.tmpInstanceStruct)>
				</cfloop>
				<cfset arrayAppend(local.arrPackagesSelected, local.tmpStr)>
			</cfif>
		</cfloop>
		
		<cfset local.strTicketDetails = getTicketCustomDetails(event=arguments.event, registrantID=arguments.registrantID, arrPackagesSelected=local.arrPackagesSelected, 
																strPackageCustomFieldsXML=local.strPackageCustomFieldsXML, strTicketCustomFieldsXML=local.strTicketCustomFieldsXML, 
																objResourceCustomFields=arguments.objResourceCustomFields, displayedCurrencyType=arguments.displayedCurrencyType)>

		<cfquery name="local.strTicketDetails.qrySelectedTickets" dbtype="query">
			select distinct ticketID
			from [local].qryTicketDetails
			where ticketID in (0#structKeyList(local.strTicketDetails.selectedTicketStr)#)
		</cfquery>

		<cfset local.strTicketDetails.containsNonDeletedPackage = local.containsNonDeletedPackage>

		<cfreturn local.strTicketDetails>
	</cffunction>

	<cffunction name="getTicketCustomDetails" access="private" output="no" returntype="struct">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="arrPackagesSelected" type="array" required="true">
		<cfargument name="strPackageCustomFieldsXML" type="struct" required="true">
		<cfargument name="strTicketCustomFieldsXML" type="struct" required="true">
		<cfargument name="objResourceCustomFields" type="any" required="true">
		<cfargument name="displayedCurrencyType" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.selectedTicketStr = structNew()>
		<cfset local.qryTicketTotals = QueryNew("itemName,itemType,totalAmt","varchar,varchar,double")>
		<cfset local.qryCustomDetails = QueryNew("fieldID,displayTypeCode,supportAmt,confTitle,confVal,confQty,confAmt,qty,qtyAmtList,totalAmt,overrideFieldName","integer,varchar,bit,varchar,varchar,varchar,varchar,integer,varchar,double,varchar")>

		<cfloop array="#arguments.arrPackagesSelected#" index="local.thisPackageSelected">
			<cfset local.thisTicketPackageID = local.thisPackageSelected.ticketPackageID>
			<cfset local.thisTicketID = local.thisPackageSelected.ticketID>
			<cfset local.thisPackageInstanceNumList = local.thisPackageSelected.thisPackageInstanceNumList>

			<cfif not structKeyExists(local.selectedTicketStr,local.thisTicketID)>
				<cfset local.tmpTicketDetailStr = { packageIDs='', hasTicketFields=local.thisPackageSelected.hasTicketFields }>
				<cfset structInsert(local.selectedTicketStr,local.thisTicketID,local.tmpTicketDetailStr)>
			</cfif>
			<cfif not structKeyExists(local.selectedTicketStr[local.thisTicketID],local.thisTicketPackageID)>
				<cfset local.tmpTicketPackageDetailStr = { confTitle=local.thisPackageSelected.packageName, 
															totalIncludedFromRate=local.thisPackageSelected.qtyIncludedByRate, arrPackage=arrayNew(1), 
															totalQty=local.thisPackageSelected.instances + local.thisPackageSelected.deletedInstanceCount, confQty='',
															totalQtyNotDeleted=local.thisPackageSelected.instances,
															confAmt='', hasPackageFields=local.thisPackageSelected.hasPackageFields }>
				<cfset structInsert(local.selectedTicketStr[local.thisTicketID],local.thisTicketPackageID,local.tmpTicketPackageDetailStr)>
				<cfset local.selectedTicketStr[local.thisTicketID].packageIDs = ListAppend(local.selectedTicketStr[local.thisTicketID].packageIDs,local.thisTicketPackageID)>
			</cfif>

			<cfset local.thisInstanceNum = 0>
			<cfset local.thisPackageTotalAmt = 0>
			<cfloop list="#local.thisPackageInstanceNumList#" index="local.thisFormInstanceNum">
				<cfset local.thisInstanceNum = local.thisInstanceNum + 1>
				<cfset local.tmpTicketStr = { custom=structNew(), instanceID=0, isDeleted=0, newQtyValue=structNew(), qtyRemoveValue=structNew(), arrSeats=arrayNew(1), includedFromRate=0, qryCustomDetails=duplicate(local.qryCustomDetails), confAmt='' }>

				<cfset local.tmpTicketStr.instanceID = local.thisPackageSelected.instanceArray[local.thisInstanceNum].instanceID>
				<cfset local.tmpTicketStr.isDeleted = local.thisPackageSelected.instanceArray[local.thisInstanceNum].isDeletedInstance>

				<cfif not local.tmpTicketStr.isDeleted>
					<cfset local.tmpTicketStr.includedFromRate = local.thisPackageSelected.instanceArray[local.thisInstanceNum].includedFromRate>
					<cfset local.tmpTicketStr.packagePrice = local.thisPackageSelected.instanceArray[local.thisInstanceNum].packageAmount>
					<cfset local.tmpTicketStr.overridePackagePriceName = "MCEVTP_#local.thisTicketPackageID#_#local.thisFormInstanceNum#">
					<cfset local.tmpTicketStr.confAmt = "#DollarFormat(local.thisPackageSelected.instanceArray[local.thisInstanceNum].packageAmount)##arguments.displayedCurrencyType#">
					<cfif not local.tmpTicketStr.includedFromRate and QueryAddRow(local.qryTicketTotals)>
						<cfset local.thisPackageTotalAmt = local.thisPackageTotalAmt + local.thisPackageSelected.instanceArray[local.thisInstanceNum].packageAmount>
						<cfset QuerySetCell(local.qryTicketTotals,"itemName",local.thisPackageSelected.packageName)>
						<cfset QuerySetCell(local.qryTicketTotals,"itemType","ticketpackage")>
						<cfset QuerySetCell(local.qryTicketTotals,"totalAmt",local.thisPackageSelected.instanceArray[local.thisInstanceNum].packageAmount)>
					</cfif>
					
					<cfloop array="#arguments.strPackageCustomFieldsXML[local.thisTicketPackageID].xmlChildren#" index="local.thisfield">
						<cfset local.tmpAtt = local.thisfield.xmlattributes>
						<cfset local.ticketPackageCustomFieldName = "ev_tpc_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.tmpAtt.fieldID#">
						<cfset local.tmpTicketStr['custom'][local.tmpAtt.fieldID] = arguments.event.getTrimValue(local.ticketPackageCustomFieldName,'')>
						
						<cfif local.tmpTicketStr.instanceID gt 0 and local.tmpAtt.displayTypeCode is 'TEXTBOX' and local.tmpAtt.supportQty is 1 and local.tmpAtt.supportAmt is 1>
							<cfset structInsert(local.tmpTicketStr.newQtyValue,local.tmpAtt.fieldID,local.tmpTicketStr['custom'][local.tmpAtt.fieldID])>
							<cfset local.thisQtyFieldAmtList = "">
							<cfset local.removeQTY = 0>
							<cfset local.totalQtyAmount = 0>

							<cfif len(arguments.event.getTrimValue('evtpcqty_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.tmpAtt.fieldID#',''))>
								<cfset structInsert(local.tmpTicketStr.qtyRemoveValue,local.tmpAtt.fieldID,arguments.event.getTrimValue('evtpcqty_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.tmpAtt.fieldID#'))>
								<cfset local.removeQTY = ListLen(arguments.event.getTrimValue('evtpcqty_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.tmpAtt.fieldID#'))>
								<cfset local.removeQtyList = arguments.event.getTrimValue('evtpcqty_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.tmpAtt.fieldID#')>
							</cfif>

							<cfset local.qtyIDs = arguments.objResourceCustomFields.getFieldQTYDetails(itemType='ticketPackInstCustom', itemID=local.tmpTicketStr.instanceID, fieldID=local.tmpAtt.fieldID, applicationType='Events', trItemType='ticketPackInstCustom')>
							<cfif local.removeQTY gt 0>
								<cfquery name="local.thisQtyFieldAmt" dbtype="query">
									select amount
									from [local].qtyIDs
									where subItemID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.removeQtyList#">)
								</cfquery>
								<cfset local.existingQtyFieldAmtList = valueList(local.thisQtyFieldAmt.amount)>
							<cfelse>
								<cfset local.existingQtyFieldAmtList = valueList(local.qtyIDs.amount)>
							</cfif>

							<cfset local.tmpTicketStr['custom'][local.tmpAtt.fieldID] = val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID]) + ListLen(valueList(local.qtyIDs.subItemID)) - local.removeQTY>
							<cfloop from="1" to="#local.tmpTicketStr['custom'][local.tmpAtt.fieldID]#" index="local.thisQty">
								<cfif local.thisQty lte listLen(local.existingQtyFieldAmtList)>
									<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,GetToken(local.existingQtyFieldAmtList,local.thisQty,','))>
									<cfset local.totalQtyAmount = local.totalQtyAmount + GetToken(local.existingQtyFieldAmtList,local.thisQty,',')>
								<cfelse>
									<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,abs(local.tmpAtt.amount))>
									<cfset local.totalQtyAmount = local.totalQtyAmount + abs(local.tmpAtt.amount)>
								</cfif>
							</cfloop>
						</cfif>

						<cfif len(local.tmpTicketStr['custom'][local.tmpAtt.fieldID])>
							<cfif local.tmpAtt.displayTypeCode is 'TEXTBOX' and local.tmpAtt.supportQty is 1 and local.tmpAtt.supportAmt is 1 and val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID]) gt 0 and QueryAddRow(local.qryTicketTotals) and QueryAddRow(local.tmpTicketStr.qryCustomDetails)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"supportAmt",1)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confVal","")>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"qty",val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID]))>
								<cfif local.tmpTicketStr.instanceID gt 0>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confQty","QTY: #val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID])#")>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confAmt","#dollarformat(local.totalQtyAmount)##arguments.displayedCurrencyType#")>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"qtyAmtList",local.thisQtyFieldAmtList)>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"totalAmt","#local.totalQtyAmount#")>
								<cfelse>
									<cfset local.thisQtyFieldAmtList = "">
									<cfif val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID]) gt 0>
										<cfloop from="1" to="#local.tmpTicketStr['custom'][local.tmpAtt.fieldID]#" index="local.thisQty">
											<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,abs(local.tmpAtt.amount))>
										</cfloop>
									</cfif>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confQty","#val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID])# x #dollarformat(abs(local.tmpAtt.amount))#")>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confAmt","#dollarformat(val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID]) * abs(local.tmpAtt.amount))##arguments.displayedCurrencyType#")>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"qtyAmtList",local.thisQtyFieldAmtList)>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"totalAmt","#val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID]) * abs(local.tmpAtt.amount)#")>
								</cfif>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"overrideFieldName","override_#local.ticketPackageCustomFieldName#")>
								<cfset QuerySetCell(local.qryTicketTotals,"itemName","#local.thisPackageSelected.packageName# - #local.tmpAtt.fieldReference#")>
								<cfset QuerySetCell(local.qryTicketTotals,"itemType","tpcustom")>
								<cfset QuerySetCell(local.qryTicketTotals,"totalAmt","#val(local.tmpTicketStr['custom'][local.tmpAtt.fieldID]) * abs(local.tmpAtt.amount)#")>
							<cfelseif local.tmpAtt.displayTypeCode is 'TEXTBOX' and local.tmpAtt.supportQty is 0 and local.tmpAtt.supportAmt is 1 and (val(ReReplace(local.tmpTicketStr['custom'][local.tmpAtt.fieldID],'[^0-9\.]','','ALL')) is not 0) and QueryAddRow(local.qryTicketTotals) and QueryAddRow(local.tmpTicketStr.qryCustomDetails)>
								<cfset local.thisAmtVal = val(ReReplace(local.tmpTicketStr['custom'][local.tmpAtt.fieldID],'[^0-9\.]','','ALL'))>
								<cfif local.tmpTicketStr.instanceID gt 0>
									<cfset local.thisValue = arguments.objResourceCustomFields.getFieldResponseEntered(itemType='ticketPackInstCustom', itemID=local.tmpTicketStr.instanceID, fieldID=local.tmpAtt.fieldID)>
									<cfset local.thisValueActualFee = arguments.objResourceCustomFields.getFieldActualFee(itemType='ticketPackInstCustom', itemID=local.tmpTicketStr.instanceID, fieldID=local.tmpAtt.fieldID, applicationType='Events', trItemType='ticketPackInstCustom')>
									<cfif local.thisValue eq local.thisAmtVal>
										<cfset local.thisAmtVal = local.thisValueActualFee>
									</cfif>
								</cfif>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"supportAmt",1)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confVal","")>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confQty","")>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confAmt","#dollarformat(local.thisAmtVal)##arguments.displayedCurrencyType#")>
									<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"qty",0)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"totalAmt",local.thisAmtVal)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"overrideFieldName","override_#local.ticketPackageCustomFieldName#")>
								<cfset QuerySetCell(local.qryTicketTotals,"itemName","#local.thisPackageSelected.packageName# - #local.tmpAtt.fieldReference#")>
								<cfset QuerySetCell(local.qryTicketTotals,"itemType","tpcustom")>
								<cfset QuerySetCell(local.qryTicketTotals,"totalAmt",local.thisAmtVal)>
							<cfelseif listFind("SELECT,RADIO,CHECKBOX",local.tmpAtt.displayTypeCode)>
								<cfif local.tmpTicketStr.instanceID gt 0>
									<cfset local.thisOptionsSelected = arguments.objResourceCustomFields.getFieldOptionsSelected(itemType='ticketPackInstCustom', itemID=local.tmpTicketStr.instanceID, fieldID=local.tmpAtt.fieldID)>
								</cfif>
								<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">		
									<cfif listFind(local.tmpTicketStr['custom'][local.tmpAtt.fieldID],local.thisoption.xmlAttributes.valueID) and QueryAddRow(local.tmpTicketStr.qryCustomDetails)>
										<cfset local.thisOptionAmount = 0>
										<cfif isDefined("local.thisoption.xmlAttributes.amount") and local.thisoption.xmlattributes.amount is not 0>
											<cfset local.thisOptionAmount = local.thisoption.xmlattributes.amount>
										</cfif>
										<cfif local.tmpTicketStr.instanceID gt 0 and len(local.thisOptionsSelected) and listFind(local.thisOptionsSelected,local.thisoption.xmlAttributes.valueID)>
											<cfset local.thisOptionAmount = arguments.objResourceCustomFields.getFieldOptionActualFee(itemType='ticketPackInstCustom', itemID=local.tmpTicketStr.instanceID, fieldID=local.tmpAtt.fieldID, valueID=local.thisoption.xmlAttributes.valueID, applicationType='Events', trItemType='ticketPackInstCustom')>
										</cfif>
										<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
										<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
										<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"supportAmt",local.tmpAtt.supportAmt)>
										<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
										<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confVal",local.thisoption.xmlattributes.fieldValue)>
										<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confQty","")>
										<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"overrideFieldName","override_#local.ticketPackageCustomFieldName#_#local.thisoption.xmlAttributes.valueID#")>
										<cfif local.thisOptionAmount gt 0 and QueryAddRow(local.qryTicketTotals)>
											<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confAmt","#DollarFormat(local.thisOptionAmount)##arguments.displayedCurrencyType#")>
											<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"totalAmt",local.thisOptionAmount)>
											<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"qty",0)>
											<cfset QuerySetCell(local.qryTicketTotals,"itemName","#local.thisPackageSelected.packageName# - #local.tmpAtt.fieldReference#: #local.thisoption.xmlattributes.fieldValue#")>
											<cfset QuerySetCell(local.qryTicketTotals,"itemType","tpcustom")>
											<cfset QuerySetCell(local.qryTicketTotals,"totalAmt",local.thisOptionAmount)>
										<cfelse>
											<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confAmt","")>
											<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"totalAmt",0)>
										</cfif>
									</cfif>
								</cfloop>
							<cfelseif QueryAddRow(local.tmpTicketStr.qryCustomDetails)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"fieldID",local.tmpAtt.fieldID)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"displayTypeCode",local.tmpAtt.displayTypeCode)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"supportAmt",local.tmpAtt.supportAmt)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confTitle",local.tmpAtt.fieldText)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confVal",local.tmpTicketStr['custom'][local.tmpAtt.fieldID])>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confQty","")>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"confAmt","")>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"qty",0)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"totalAmt",0)>
								<cfset QuerySetCell(local.tmpTicketStr.qryCustomDetails,"overrideFieldName","override_#local.ticketPackageCustomFieldName#")>
							</cfif>
						</cfif>
					</cfloop>
				</cfif>
				
				<cfset arrayAppend(local.selectedTicketStr[local.thisTicketID][local.thisTicketPackageID].arrPackage,local.tmpTicketStr)>
				
				<cfif not local.tmpTicketStr.isDeleted>
					<cfif arguments.registrantID gt 0 and local.tmpTicketStr.instanceID gt 0>
						<cfquery name="local.qryThisInstanceSeats" datasource="#application.dsn.membercentral.dsn#">
							select seatID, memberID, ROW_NUMBER() over (order by seatID) as seatNum
							from dbo.ev_registrantPackageInstanceSeats
							where instanceID = <cfqueryparam value="#local.tmpTicketStr.instanceID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
					</cfif>
					<!--- Ticket custom fields  --->
					<cfloop from="1" to="#local.thisPackageSelected.ticketsInPackage#" index="local.thisSeatNum">
						<cfset local.tmpThisTicketStr = { custom=structNew(), seatID=0, mID=val(arguments.event.getValue('ev_tgmid_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.thisSeatNum#','')),newQtyValue=structNew(), qtyRemoveValue=structNew(), qryCustomDetails=duplicate(local.qryCustomDetails) }>

						<cfif arguments.registrantID gt 0 and local.tmpTicketStr.instanceID gt 0 and local.qryThisInstanceSeats.recordcount eq local.thisPackageSelected.ticketsInPackage>
							<cfset local.tmpThisTicketStr.seatID = GetToken(valueList(local.qryThisInstanceSeats.seatID),local.thisSeatNum,",")>
						</cfif>

						<cfloop array="#arguments.strTicketCustomFieldsXML[local.thisTicketID].xmlChildren#" index="local.thisTicketField">
							<cfset local.tmpTicketAtt = local.thisTicketField.xmlattributes>
							<cfset local.ticketCustomFieldName = 'ev_tc_#local.thisTicketPackageID#_#local.thisTicketID#_#local.tmpTicketAtt.fieldID#_#local.thisSeatNum#_#local.thisFormInstanceNum#'>
							<cfset local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID] = arguments.event.getTrimValue(local.ticketCustomFieldName,'')>

							<cfif local.tmpThisTicketStr.seatID gt 0 and local.tmpTicketAtt.displayTypeCode is 'TEXTBOX' and local.tmpTicketAtt.supportQty is 1 and local.tmpTicketAtt.supportAmt is 1>
								<cfset structInsert(local.tmpThisTicketStr['newQtyValue'],local.tmpTicketAtt.fieldID,local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID])>
								<cfset local.thisQtyFieldAmtList = "">
								<cfset local.removeQTY = 0>
								<cfset local.totalQtyAmount = 0>

								<cfif len(arguments.event.getTrimValue('evtcqty_#local.thisTicketPackageID#_#local.thisTicketID#_#local.thisSeatNum#_#local.thisFormInstanceNum#_#local.tmpTicketAtt.fieldID#',''))>
									<cfset structInsert(local.tmpThisTicketStr['qtyRemoveValue'],local.tmpTicketAtt.fieldID,arguments.event.getTrimValue('evtcqty_#local.thisTicketPackageID#_#local.thisTicketID#_#local.thisSeatNum#_#local.thisFormInstanceNum#_#local.tmpTicketAtt.fieldID#'))>
									<cfset local.removeQTY = ListLen(arguments.event.getTrimValue('evtcqty_#local.thisTicketPackageID#_#local.thisTicketID#_#local.thisSeatNum#_#local.thisFormInstanceNum#_#local.tmpTicketAtt.fieldID#'))>
									<cfset local.removeQtyList = arguments.event.getTrimValue('evtcqty_#local.thisTicketPackageID#_#local.thisTicketID#_#local.thisSeatNum#_#local.thisFormInstanceNum#_#local.tmpTicketAtt.fieldID#')>
								</cfif>

								<cfset local.qtyIDs = arguments.objResourceCustomFields.getFieldQTYDetails(itemType='ticketPackSeatCustom', itemID=local.tmpThisTicketStr.seatID, fieldID=local.tmpTicketAtt.fieldID, applicationType='Events', trItemType='ticketPackSeatCustom')>
								<cfif local.removeQTY gt 0>
									<cfquery name="local.thisQtyFieldAmt" dbtype="query">
										select amount
										from [local].qtyIDs
										where subItemID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.removeQtyList#">)
									</cfquery>
									<cfset local.existingQtyFieldAmtList = valueList(local.thisQtyFieldAmt.amount)>
								<cfelse>
									<cfset local.existingQtyFieldAmtList = valueList(local.qtyIDs.amount)>
								</cfif>

								<cfset local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID] = val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]) + ListLen(valueList(local.qtyIDs.subItemID)) - local.removeQTY>
								<cfloop from="1" to="#local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]#" index="local.thisQty">
									<cfif local.thisQty lte listLen(local.existingQtyFieldAmtList)>
										<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,GetToken(local.existingQtyFieldAmtList,local.thisQty,','))>
										<cfset local.totalQtyAmount = local.totalQtyAmount + GetToken(local.existingQtyFieldAmtList,local.thisQty,',')>
									<cfelse>
										<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,abs(local.tmpTicketAtt.amount))>
										<cfset local.totalQtyAmount = local.totalQtyAmount + abs(local.tmpTicketAtt.amount)>
									</cfif>
								</cfloop>
							</cfif>

							<cfif len(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID])>
								<cfif local.tmpTicketAtt.displayTypeCode is 'TEXTBOX' and local.tmpTicketAtt.supportQty is 1 and local.tmpTicketAtt.supportAmt is 1 and val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]) gt 0 and QueryAddRow(local.qryTicketTotals) and QueryAddRow(local.tmpThisTicketStr.qryCustomDetails)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"fieldID",local.tmpTicketAtt.fieldID)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"displayTypeCode",local.tmpTicketAtt.displayTypeCode)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"supportAmt",1)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confTitle",local.tmpTicketAtt.fieldText)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confVal","")>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"qty",val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]))>
									<cfif local.tmpThisTicketStr.seatID gt 0>
										<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confQty","QTY: #val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID])#")>
										<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confAmt","#dollarformat(local.totalQtyAmount)##arguments.displayedCurrencyType#")>
										<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"qtyAmtList",local.thisQtyFieldAmtList)>
										<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"totalAmt","#local.totalQtyAmount#")>
									<cfelse>
										<cfset local.thisQtyFieldAmtList = "">
										<cfif val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]) gt 0>
											<cfloop from="1" to="#local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]#" index="local.thisQty">
												<cfset local.thisQtyFieldAmtList = listAppend(local.thisQtyFieldAmtList,abs(local.tmpTicketAtt.amount))>
											</cfloop>
										</cfif>
										<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confQty","#val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID])# x #dollarformat(abs(local.tmpTicketAtt.amount))#")>
										<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confAmt","#dollarformat(val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]) * abs(local.tmpTicketAtt.amount))##arguments.displayedCurrencyType#")>
										<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"qtyAmtList",local.thisQtyFieldAmtList)>
										<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"totalAmt","#val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]) * abs(local.tmpTicketAtt.amount)#")>
									</cfif>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"overrideFieldName","override_#local.ticketCustomFieldName#")>
									<cfset QuerySetCell(local.qryTicketTotals,"itemName","#local.thisPackageSelected.packageName# - Ticket #local.thisSeatNum# - #local.tmpTicketAtt.fieldReference#")>
									<cfset QuerySetCell(local.qryTicketTotals,"itemType","tcustom")>
									<cfset QuerySetCell(local.qryTicketTotals,"totalAmt","#val(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID]) * abs(local.tmpTicketAtt.amount)#")>
								<cfelseif local.tmpTicketAtt.displayTypeCode is 'TEXTBOX' and local.tmpTicketAtt.supportQty is 0 and local.tmpTicketAtt.supportAmt is 1 and (val(ReReplace(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID],'[^0-9\.]','','ALL')) is not 0) and QueryAddRow(local.qryTicketTotals) and QueryAddRow(local.tmpThisTicketStr.qryCustomDetails)>
									<cfset local.thisAmtVal = val(ReReplace(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID],'[^0-9\.]','','ALL'))>
									<cfif local.tmpThisTicketStr.seatID gt 0>
										<cfset local.thisValue = arguments.objResourceCustomFields.getFieldResponseEntered(itemType='ticketPackSeatCustom', itemID=local.tmpThisTicketStr.seatID, fieldID=local.tmpTicketAtt.fieldID)>
										<cfset local.thisValueActualFee = arguments.objResourceCustomFields.getFieldActualFee(itemType='ticketPackSeatCustom', itemID=local.tmpThisTicketStr.seatID, fieldID=local.tmpTicketAtt.fieldID, applicationType='Events', trItemType='ticketPackSeatCustom')>
										<cfif local.thisValue eq local.thisAmtVal>
											<cfset local.thisAmtVal = local.thisValueActualFee>
										</cfif>
									</cfif>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"fieldID",local.tmpTicketAtt.fieldID)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"displayTypeCode",local.tmpTicketAtt.displayTypeCode)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"supportAmt",1)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confTitle",local.tmpTicketAtt.fieldText)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confVal","")>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confQty","")>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confAmt","#dollarformat(local.thisAmtVal)##arguments.displayedCurrencyType#")>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"qty",0)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"totalAmt",local.thisAmtVal)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"overrideFieldName","override_#local.ticketCustomFieldName#")>
									<cfset QuerySetCell(local.qryTicketTotals,"itemName","#local.thisPackageSelected.packageName# - Ticket #local.thisSeatNum# - #local.tmpTicketAtt.fieldReference#")>
									<cfset QuerySetCell(local.qryTicketTotals,"itemType","tcustom")>
									<cfset QuerySetCell(local.qryTicketTotals,"totalAmt",local.thisAmtVal)>
								<cfelseif listFind("SELECT,RADIO,CHECKBOX",local.tmpTicketAtt.displayTypeCode)>
									<cfif local.tmpThisTicketStr.seatID gt 0>
										<cfset local.thisOptionsSelected = arguments.objResourceCustomFields.getFieldOptionsSelected(itemType='ticketPackSeatCustom', itemID=local.tmpThisTicketStr.seatID, fieldID=local.tmpTicketAtt.fieldID)>
									</cfif>
									<cfloop array="#local.thisTicketField.xmlchildren#" index="local.thisoption">
										<cfif listFind(local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID],local.thisoption.xmlAttributes.valueID) and QueryAddRow(local.tmpThisTicketStr.qryCustomDetails)>
											<cfset local.thisOptionAmount = 0>
											<cfif isDefined("local.thisoption.xmlAttributes.amount") and local.thisoption.xmlattributes.amount is not 0>
												<cfset local.thisOptionAmount = local.thisoption.xmlattributes.amount>
											</cfif>
											<cfif local.tmpThisTicketStr.seatID gt 0 and len(local.thisOptionsSelected) and listFind(local.thisOptionsSelected,local.thisoption.xmlAttributes.valueID)>
												<cfset local.thisOptionAmount = arguments.objResourceCustomFields.getFieldOptionActualFee(itemType='ticketPackSeatCustom', itemID=local.tmpThisTicketStr.seatID, fieldID=local.tmpTicketAtt.fieldID, valueID=local.thisoption.xmlAttributes.valueID, applicationType='Events', trItemType='ticketPackSeatCustom')>
											</cfif>

											<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"fieldID",local.tmpTicketAtt.fieldID)>
											<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"displayTypeCode",local.tmpTicketAtt.displayTypeCode)>
											<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"supportAmt",local.tmpTicketAtt.supportAmt)>
											<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confTitle",local.tmpTicketAtt.fieldText)>
											<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confVal",local.thisoption.xmlattributes.fieldValue)>
											<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confQty","")>
											<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"overrideFieldName","override_#local.ticketCustomFieldName#_#local.thisoption.xmlAttributes.valueID#")>
											<cfif local.thisOptionAmount gt 0 and QueryAddRow(local.qryTicketTotals)>
												<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confAmt","#DollarFormat(local.thisOptionAmount)##arguments.displayedCurrencyType#")>
												<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"totalAmt",local.thisOptionAmount)>
												<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"qty",0)>
												<cfset QuerySetCell(local.qryTicketTotals,"itemName","#local.thisPackageSelected.packageName# - Ticket #local.thisSeatNum# - #local.tmpTicketAtt.fieldReference#: #local.thisoption.xmlattributes.fieldValue#")>
												<cfset QuerySetCell(local.qryTicketTotals,"itemType","tcustom")>
												<cfset QuerySetCell(local.qryTicketTotals,"totalAmt",local.thisOptionAmount)>
											<cfelse>
												<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confAmt","")>
												<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"totalAmt",0)>
											</cfif>
										</cfif>
									</cfloop>
								<cfelseif QueryAddRow(local.tmpThisTicketStr.qryCustomDetails)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"fieldID",local.tmpTicketAtt.fieldID)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"displayTypeCode",local.tmpTicketAtt.displayTypeCode)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"supportAmt",local.tmpTicketAtt.supportAmt)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confTitle",local.tmpTicketAtt.fieldText)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confVal",local.tmpThisTicketStr['custom'][local.tmpTicketAtt.fieldID])>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confQty","")>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"confAmt","")>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"qty",0)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"totalAmt",0)>
									<cfset QuerySetCell(local.tmpThisTicketStr.qryCustomDetails,"overrideFieldName","override_#local.ticketCustomFieldName#")>
								</cfif>
							</cfif>
						</cfloop>
						<cfset arrayAppend(local.selectedTicketStr[local.thisTicketID][local.thisTicketPackageID]['arrPackage'][local.thisInstanceNum]['arrSeats'],local.tmpThisTicketStr)>	
					</cfloop>
				<cfelse>
					<cfset local.tmpThisTicketStr = { custom=structNew(), seatID=0, mID=0, newQtyValue=structNew(), qtyRemoveValue=structNew(), qryCustomDetails=duplicate(local.qryCustomDetails) }>
					<cfset arraySet(local.selectedTicketStr[local.thisTicketID][local.thisTicketPackageID]['arrPackage'][local.thisInstanceNum]['arrSeats'],1,local.thisPackageSelected.ticketsInPackage,local.tmpThisTicketStr)>
				</cfif>
			</cfloop>
		</cfloop>

		<cfset local.returnStruct = { selectedTicketStr=local.selectedTicketStr, qryTicketTotals=local.qryTicketTotals }>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveRegistrant_insert" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="strEvent" type="struct" required="true">
		<cfargument name="qryRegistrantRoles" type="query" required="true">

		<cfset var local = structNew()>
		<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event")>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>

		<cfset local.memberID = val(arguments.event.getValue('mid'))>
		<cfset local.activeMemberID = application.objMember.getActiveMemberID(local.memberID)>
		<cfset local.eventID = arguments.event.getValue('eid')>
		<cfset local.registrationID = arguments.strEvent.qryEventRegMeta.registrationID>
		<cfif arguments.event.valueExists('override_mcev_rateAmt_#arguments.event.getValue('ev_rateID')#_')>
			<cfset local.rateAmount = val(rereplace(arguments.event.getValue('override_mcev_rateAmt_#arguments.event.getValue('ev_rateID')#_'),"[^0-9.]","","ALL"))>
		<cfelse>
			<cfset local.rateAmount = val(local.objAdminEvent.getRateAmount(arguments.event.getValue('ev_rateID')))>
		</cfif>
		<cfset local.couponID = arguments.event.getValue('mcev_couponID',0)>
		<cfset local.discountAmt = arguments.event.getValue('mcev_discount',0)>
		<cfset local.eventRegistrantRoles = arguments.event.getTrimValue('evRegistrantRoles','')>
		<cfset local.qryGetRateAndEventGLAccountID = getRateandEventGLAccountID(eventID=local.eventID, rateID=arguments.event.getValue('ev_rateID'))>
		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfif NOT IsValid("date",arguments.event.getValue('transactionDate'))>
			<cfset arguments.event.setValue('transactionDate',dateformat(now(),"m/d/yyyy"))>
		</cfif>
		<cfset local.taxInfoStruct = { stateIDForTax=val(arguments.event.getValue('stateIDforTax',0)), zipForTax=arguments.event.getTrimValue('zipForTax',''), transactionDate=arguments.event.getValue('transactionDate') }>
		<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.taxInfoStruct.zipForTax, billingStateID=local.taxInfoStruct.stateIDForTax)>
		<cfif local.strBillingZip.isvalidzip>
			<cfset local.taxInfoStruct.zipForTax = local.strBillingZip.billingzip>
		<cfelse>
			<cfthrow message="Invalid State/Zip.">
		</cfif>

		<!--- put registrant role custom fields into array --->
		<cfset local.arrRoleCustomFields = []>
		<cfloop list="#local.eventRegistrantRoles#" index="local.thisRoleCatID">
			<cfset local.qryEventRoleFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='EventAdmin', areaName='Role', csrid=local.eventAdminSiteResourceID, detailID=local.thisRoleCatID, hideAdminOnly=0)>
			<cfset local.xmlEventRoleCustomFields = xmlParse(local.qryEventRoleFieldsXML.returnXML).xmlRoot>
			<cfif arrayLen(local.xmlEventRoleCustomFields.xmlChildren)>
				<cfloop array="#local.xmlEventRoleCustomFields.xmlChildren#" index="local.thisfield">
					<cfset local.tmpAtt = local.thisfield.xmlattributes>
					<cfset local.tmpStr = { roleCategoryID=local.thisRoleCatID,
											fieldID=local.tmpAtt.fieldID,
											displayTypeCode=local.tmpAtt.displayTypeCode, 
											dataTypeCode=local.tmpAtt.dataTypeCode, 
											value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','') }>

					<cfif local.tmpStr.displayTypeCode eq 'DOCUMENT'>
						<cfset local.newVal = arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','')>
						<cfif len(local.newVal)>
							<cfset local.fileUploaded = TRUE>
							<cftry>
								<cfset local.newFile = local.objDocument.uploadFile('cf_#local.tmpAtt.fieldID#_')>
								<cfcatch type="any">
									<cfset local.fileUploaded = FALSE>
								</cfcatch> 
							</cftry>
							<cfif local.fileUploaded>
								<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
								<cfset local.uploadedFileName = local.newFile.clientFile>
								<cfset local.uploadedFileExt = local.newFile.clientFileExt>
								<!---  add new document --->
								<cfset local.rootSectionID = local.objSection.getSectionFromSectionCode(siteID=arguments.event.getValue('mc_siteinfo.siteid'), sectionCode="MCAMSEventDocuments").sectionID>
								<cfset local.insertResults = local.objDocument.insertDocument(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
											resourceType='ApplicationCreatedDocument', parentSiteResourceID=local.eventAdminSiteResourceID,
											sectionID=local.rootSectionID, docTitle='', docDesc='', author='', fileData=local.newFile,
											isVisible=false, contributorMemberID=session.cfcuser.memberdata.memberid, recordedByMemberID=session.cfcuser.memberdata.memberid,
											oldFileExt=local.uploadedFileExt)>
								<cfset local.tmpStr.value = local.insertResults.documentSiteResourceID>
							</cfif>
						</cfif>
					</cfif>
					<cfset arrayAppend(local.arrRoleCustomFields,local.tmpStr)>
				</cfloop>
			</cfif>
		</cfloop>

		<!--- put custom fields and field types into array --->
		<cfset local.arrCustomFields = []>
		<cfset local.strCustomFields = structNew()>
		<cfset local.regCustomFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', 
				areaName='Registrant', csrid=arguments.strEvent.qryEventMeta.siteResourceID, detailID=0, hideAdminOnly=0)>
		<cfset local.strCustomFields = xmlParse(local.regCustomFieldsXML.returnXML).xmlRoot>
		<cfif arrayLen(local.strCustomFields.xmlChildren)>
			<cfloop array="#local.strCustomFields.xmlChildren#" index="local.thisfield">
				<cfset local.tmpAtt = local.thisfield.xmlattributes>
				<cfset local.thisFieldName = "cf_#local.tmpAtt.fieldID#_">
				<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
										displayTypeCode=local.tmpAtt.displayTypeCode, 
										dataTypeCode=local.tmpAtt.dataTypeCode, 
										offerQty=local.tmpAtt.supportQty,
										offerAmount=local.tmpAtt.supportAmt,
										fieldinventory=local.tmpAtt.fieldinventory, 
										qtyIndivAmt=0,
										GLAccountID=val(local.tmpAtt.GLAccountID),
										value=arguments.event.getTrimValue('#local.thisFieldName#',''),
										arrQtyAmt=[], 
										strOptionAmt={} }>
				<cfif val(local.tmpStr.GLAccountID) eq 0>
					<cfset local.tmpStr.GLAccountID = val(local.qryGetRateAndEventGLAccountID.GLAccountID)>
				</cfif>
				<cfif local.tmpStr.displayTypeCode eq 'TEXTBOX' and local.tmpStr.offerAmount eq 1>
					<cfset local.tmpStr.value = val(ReReplace(local.tmpStr.value,'[^0-9\.]','','ALL'))>

					<cfif local.tmpStr.offerQty eq 1>
						<cfset local.tmpStr.qtyIndivAmt = abs(local.tmpAtt.amount)>
						<cfif local.tmpStr.value gt 0>
							<cfset local.strQtyAmtTax = structNew()>
							<cfset local.strTaxArgs = { saleGLAccountID=local.tmpStr.GLAccountID, saleAmount=local.tmpStr.qtyIndivAmt, 
														transactionDate="#dateformat(local.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
														stateIDForTax=local.taxInfoStruct.stateIDForTax, zipForTax=local.taxInfoStruct.zipForTax }>
							
							<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
							<cfset local.strQtyAmtTax[local.tmpStr.qtyIndivAmt] = val(local.strTaxRate.totalTaxAmt)>

							<cfloop from="1" to="#val(local.tmpStr.value)#" index="local.thisQtyNum">
								<cfset local.qtyStr = { amount=local.tmpStr.qtyIndivAmt, taxAmount=local.strQtyAmtTax[local.tmpStr.qtyIndivAmt] }>
								<cfset local.thisFieldOverrideName = 'override_#local.thisFieldName#qty#local.thisQtyNum#_'>
								<cfif arguments.event.valueExists('#local.thisFieldOverrideName#') and local.qtyStr.amount neq rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
									<cfset local.thisQtyActualAmount = rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
									<cfif not structKeyExists(local.strQtyAmtTax,local.thisQtyActualAmount)>
										<cfset local.strTaxArgs.saleAmount = local.thisQtyActualAmount>
										<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
										<cfset local.strQtyAmtTax[local.thisQtyActualAmount] = val(local.strTaxRate.totalTaxAmt)>
									</cfif>
									<cfset local.qtyStr.amount = local.thisQtyActualAmount>
									<cfset local.qtyStr.taxAmount = local.strQtyAmtTax[local.thisQtyActualAmount]>
								</cfif>
								<cfset arrayAppend(local.tmpStr.arrQtyAmt,local.qtyStr)>
							</cfloop>
						</cfif>
					<cfelseif local.tmpStr.offerQty eq 0 and arguments.event.valueExists('override_#local.thisFieldName#')>
						<cfset local.tmpStr.value = rereplace(arguments.event.getTrimValue('override_#local.thisFieldName#'),"[^0-9.]","","ALL")>
					</cfif>
				<cfelseif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpStr.displayTypeCode) and local.tmpStr.offerAmount eq 1 and len(local.tmpStr.value)>
					<cfset local.strOptionAmtTax = structNew()>
					<cfset local.strTaxArgs = { saleGLAccountID=local.tmpStr.GLAccountID, saleAmount=0, 
												transactionDate="#dateformat(local.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
												stateIDForTax=local.taxInfoStruct.stateIDForTax, zipForTax=local.taxInfoStruct.zipForTax }>

					<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
						<cfif listFind(local.tmpStr.value,local.thisoption.xmlAttributes.valueID)>
							<cfset local.thisFieldOverrideName = 'override_#local.thisFieldName##local.thisoption.xmlAttributes.valueID#_'>
							<cfset local.thisOptionAmount = val(local.thisoption.xmlAttributes.amount)>
							<cfif arguments.event.valueExists('#local.thisFieldOverrideName#')>
								<cfset local.thisOptionAmount = val(rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL"))>
							</cfif>
							<cfif not structKeyExists(local.strOptionAmtTax,local.thisOptionAmount)>
								<cfset local.strTaxArgs.saleAmount = local.thisOptionAmount>
								<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
								<cfset local.strQtyAmtTax[local.thisOptionAmount] = val(local.strTaxRate.totalTaxAmt)>
							</cfif>
							<cfset local.tmpStr.strOptionAmt[local.thisoption.xmlAttributes.valueID] = { amount=local.thisOptionAmount, taxAmount=local.strQtyAmtTax[local.thisOptionAmount] }>
						</cfif>
					</cfloop>
				</cfif>
				<cfset arrayAppend(local.arrCustomFields,local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.overrideEmailAddress = arguments.event.getTrimValue('regEmail','')>
		<cfif len(local.overrideEmailAddress)>
			<cfset local.registrantEmail = application.objMember.getMainEmail(memberID=local.memberID).email>
			<cfif NOT isValid("regex",local.overrideEmailAddress,application.regEx.email) OR local.overrideEmailAddress EQ local.registrantEmail>
				<cfset local.overrideEmailAddress = "">
			</cfif>
		</cfif>

		<cfsavecontent variable="local.addRegistrantSQL">
			<cfoutput>
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @invoiceID int, @rateID int, @orgID int, @siteID int, @loggedInMemberID int, @statsSessionID int, 
					@GLAccountID int, @QuestionGLAccountID int, @registrantID int, @eventID int, @registrationID int, @valueID int,
					@rateTransactionID int, @customTransactionID int, @paymentTransactionID int, @historyID int, @fieldID int,
					@stateIDforTax int, @zipForTax varchar(25), @invoiceProfileID int, @deferredGLAccountID int, @subregistrantID int,
					@invoiceNumber varchar(19), @deferredDateStr varchar(10), @detail varchar(max), @eventRoles varchar(max), @dataID int,
					@transDate datetime, @accrualDate datetime, @deferredDate datetime, @xmlSchedule xml, @assignedToMemberID int,
					@registrationMerchantProfiles varchar(1000), @taxAmount decimal(18,2), @instanceID int, @ticketTransactionID int, @qtyIndivAmt decimal(18,2), 
					@TicketGLAccountID int, @seatID int, @guestRegistrantID int, @registrantTypeID int,
					@currentInventory int, @fieldInventoryLimit int, @invDetail varchar(max), @detailInt int, @counter int, @detailAmt decimal(18,2), 
					@inventoryReducedTo int, @invDetailSold varchar(max), @registrantCap int, @currentRegCount int, @regCapReached bit, 
					@eventTitle varchar(200), @trashID int, @couponID int, @discountAmount decimal(18,2), @adjTransactionID int, @maxOverallUsageCount int, 
					@maxMemberUsageCount int, @redemptionCount int, @redemptionCountPerMember int, @couponCode varchar(15), @evRedemptionCount int;
				DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int, amount decimal(18,2));
				DECLARE @tblMonetaryQtyFieldDetails TABLE (qty int, amount decimal(18,2), taxAmount decimal(18,2));

				set @orgID = #arguments.event.getValue('mc_siteinfo.orgid')#;
				set @siteID = #arguments.event.getValue('mc_siteinfo.siteid')#;
				set @eventID = #local.eventID#;
				set @registrationID = #int(val(local.registrationID))#;
				set @loggedInMemberID = #val(session.cfcuser.memberdata.memberid)#;
				set @statsSessionID = #val(session.cfcUser.statsSessionID)#;
				set @assignedToMemberID = #int(val(local.activeMemberID))#;
				set @stateIDforTax = <cfif local.taxInfoStruct.stateIDForTax gt 0>#local.taxInfoStruct.stateIDForTax#<cfelse>null</cfif>;
				set @zipForTax = <cfif len(local.taxInfoStruct.zipForTax)>'#local.taxInfoStruct.zipForTax#'<cfelse>null</cfif>;
				set @eventRoles = '#local.eventRegistrantRoles#';
				set @transDate = '#dateformat(arguments.event.getValue('transactionDate'),"m/d/yyyy")# #timeformat(now(),"h:mm tt")#';
				set @registrantCap = #val(arguments.strEvent.qryEventRegMeta.registrantCap)#;
				set @currentRegCount = null;
				set @regCapReached = 0;

				-- double check the registrant hasnt already been created
				select @registrantID = r.registrantID
					from dbo.ev_registrants as r
					inner join dbo.ams_members as m on m.memberID = r.memberID
					where r.registrationID = @registrationID
					and m.activeMemberID = @assignedToMemberID
					and r.status = 'A';
				if @registrantID is not null 
					RAISERROR('registrant already exists',16,1);

				select @currentRegCount = count(registrantID)
					from dbo.ev_registrants
					where registrationID = @registrationID
					and status = 'A';
				select @currentRegCount = isNull(@currentRegCount,0);

				IF @registrantCap > 0 AND @currentRegCount + 1 > @registrantCap BEGIN
					SET @regCapReached = 1;

					GOTO on_regcomplete;
				END
					

				set @rateID = #int(val(arguments.event.getValue('ev_rateID')))#;
				set @GLAccountID = #val(local.qryGetRateAndEventGLAccountID.GLAccountID)#;
				select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;

				select @registrationMerchantProfiles = NULLIF(dbo.sortedIntList(profileID), '')
				from dbo.ev_rates as r
				inner join dbo.ev_registrationMerchantProfiles as regmp on regmp.registrationID = r.registrationID and r.rateID = @rateID;

				SELECT top 1 @eventTitle = eventcontent.contentTitle, @accrualDate = et.startTime
				FROM dbo.ev_events as e 
				inner join dbo.ev_times et on e.eventID = et.eventID   
				INNER JOIN dbo.sites s on s.siteID = @siteid AND s.defaultTimeZoneID = et.timeZoneID
				inner join dbo.cms_contentLanguages as eventContent on eventContent.contentID = e.eventContentID and eventContent.languageID = 1
				WHERE e.eventID = @eventID;

				select @detail = @eventTitle + isnull(' - ' + rateName,'')
				from dbo.ev_rates 
				where rateID = @rateID;

				-- this date is used if we need to create deferred entries. We will use the LATEST of event start date, date registered aka transaction date.
				select @deferredDate = case when @accrualDate > @transDate then @accrualDate else @transDate end;
				select @deferredDateStr = convert(varchar(10),@deferredDate,101);
				
				BEGIN TRAN;
					-- create invoice assigned to registrant based on invoice profile of rate
					EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
						@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@accrualDate, 
						@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

					insert into @tblInvoices (invoiceID, invoiceProfileID)
					values (@invoiceID, @invoiceProfileID);

					IF @registrationMerchantProfiles is not null
						EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;

					-- add registrant
					EXEC dbo.ev_addRegistrant @registrationID=@registrationID, @memberID=@assignedToMemberID, @recordedOnSiteID=@siteid, 
						@rateID=@rateID, @dateRegistered=@transDate, @identificationMethod='CP_ADMIN', @registrantID=@registrantID OUTPUT;
					IF @registrantID = 0 
						RAISERROR('unable to add registrant',16,1);

					<cfif len(local.overrideEmailAddress)>
						EXEC dbo.ams_insertUpdateEmailAppOverrides @itemType='eventreg', @itemID=@registrantID, @email='#local.overrideEmailAddress.replace("'","''","all")#';
					</cfif>

					<cfif len(arguments.event.getTrimValue('internalNotes',''))>
						-- set internal notes if defined
						UPDATE dbo.ev_registrants 
						SET internalNotes = '#replace(arguments.event.getTrimValue('internalNotes',''),"'","''","ALL")#'
						WHERE registrantID = @registrantID;
					</cfif>

					<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=val(local.qryGetRateAndEventGLAccountID.GLAccountID), 
													saleAmount=val(local.rateAmount), transactionDate="#dateformat(local.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
													stateIDForTax=local.taxInfoStruct.stateIDForTax, zipForTax=local.taxInfoStruct.zipForTax)>
					-- record rate transaction
					set @taxAmount = #val(local.strTaxRate.totalTaxAmt)#;

					select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
					IF @deferredGLAccountID is not null
						set @xmlSchedule = '<rows><row amt="#val(local.rateAmount)#" dt="' + @deferredDateStr + '" /></rows>';

					EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
						@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID,
						@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, 
						@parentTransactionID=null, @amount=#val(local.rateAmount)#, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, 
						@invoiceID=@invoiceID, @stateIDForTax=@stateIDforTax, @zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, 
						@bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@rateTransactionID OUTPUT;
					EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@rateTransactionID, 
						@itemType='Rate', @itemID=@registrantID, @subItemID=null;

					-- promo code applied
					<cfif local.couponID gt 0 and local.discountAmt gt 0>
						select @couponID = null, @couponCode = null, @discountAmount = null, @maxOverallUsageCount = null, @maxMemberUsageCount = null, 
							@redemptionCount = null, @redemptionCountPerMember = null, @evRedemptionCount = null;

						set @couponID = #int(local.couponID)#;
						set @discountAmount = #local.discountAmt# * - 1;

						-- check if coupon max usage count met
						select @couponCode = couponCode, @maxOverallUsageCount = maxOverallUsageCount, @maxMemberUsageCount = maxMemberUsageCount,
							@evRedemptionCount = eventsXML.value('(/event/ev/e[text()=sql:variable("@eventID")]/@rc)[1]', 'int')
						from dbo.tr_coupons
						where siteID = @siteID
						and couponID = @couponID;

						set @evRedemptionCount = isnull(@evRedemptionCount,1);

						; with redemptions as(
							select distinct td.itemType, td.itemID, td.redemptionCount, 
								case when m.memberID is not null then td.redemptionCount else 0 end as memberRedemptionCount
							from dbo.tr_coupons as c
							inner join dbo.tr_transactionDiscounts as td on td.orgID = @orgID 
								and td.couponID = c.couponID 
								and td.isActive = 1
								and c.couponID = @couponID
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
							left outer join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedToMemberID and m.activeMemberID = @assignedToMemberID
						)
						select @redemptionCount = SUM(redemptionCount), @redemptionCountPerMember = SUM(memberRedemptionCount) 
						from redemptions;

						IF @maxOverallUsageCount > 0 AND ISNULL(@redemptionCount,0) + @evRedemptionCount > @maxOverallUsageCount
							UPDATE dbo.ev_registrants
							SET isFlagged = 1,
								internalNotes = isnull(internalNotes,'') + ' - Overall Redemptions Count for Coupon Code(' + @couponCode + ') exceeded; '
							WHERE registrantID = @registrantID;

						IF @maxMemberUsageCount > 0 AND ISNULL(@redemptionCountPerMember,0) + @evRedemptionCount > @maxMemberUsageCount
							UPDATE dbo.ev_registrants
							SET isFlagged = 1,
								internalNotes = isnull(internalNotes,'') + ' - Overall Redemptions Count Per Member for Coupon Code(' + @couponCode + ') exceeded; '
							WHERE registrantID = @registrantID;

						EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@transDate, 
							@saleTransactionID=@rateTransactionID, @invoiceID=@invoiceID, @couponID=@couponID, 
							@itemType='EventRate', @itemID=@registrantID, @redemptionCount=@evRedemptionCount, 
							@transactionID=@adjTransactionID OUTPUT;
					</cfif>

					<!--- event roles --->
					<cfif arguments.qryRegistrantRoles.recordcount and listLen(local.eventRegistrantRoles)>
						-- add new registrant roles
						insert into dbo.ev_registrantCategories(registrantID, categoryID)
						select @registrantID, listitem
						from dbo.fn_intListToTable(@eventRoles, ',');

						-- add registrant role details
						<cfloop array="#local.arrRoleCustomFields#" index="local.cf">
							<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
								<cfloop list="#local.cf.value#" index="local.valueitem">
									<cfif val(local.valueitem) gt 0>
										set @fieldID = #val(local.cf.fieldID)#;
										set @valueID = #val(local.valueitem)#;
										set @dataID = null;

										EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@registrantID, @itemType='EventRole', 
											@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
									</cfif>
								</cfloop>
							<cfelseif len(local.cf.value)>
								set @fieldID = #val(local.cf.fieldID)#;
								set @detail =  '#replace(local.cf.value,"'","''","ALL")#';
								set @dataID = null;

								EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@registrantID, @itemType='EventRole', 
											@valueID=NULL, @fieldValue=@detail, @dataID=@dataID OUTPUT;
							</cfif>
						</cfloop>
					</cfif>

					<!--- custom fields --->
					<cfloop array="#local.arrCustomFields#" index="local.cf">
						<cfswitch expression="#local.cf.displayTypeCode#">
						<cfcase value="TEXTBOX">
							<cfset local.tempSQL = addReg_cf_TEXTBOX(mode='event', fieldID=local.cf.fieldID, customText=local.cf.value,
																	offerQty=local.cf.offerQty,
																	offerAmount=local.cf.offerAmount,
																	fieldinventory=val(local.cf.fieldinventory),
																	objAccounting=local.objAccounting,
																	GLAccountID=val(local.cf.GLAccountID),
																	taxInfoStruct=local.taxInfoStruct,
																	arrQtyAmt=local.cf.arrQtyAmt)>
							#local.tempSQL#
						</cfcase>
						<cfcase value="SELECT,RADIO,CHECKBOX">
							<cfset local.tempSQL = addReg_cf_SELECTRADIOCHECKBOX(mode='event', fieldID=local.cf.fieldID, valueIDList=local.cf.value, GLAccountID=val(local.cf.GLAccountID), 
																	offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, strOptionAmt=local.cf.strOptionAmt)>
							#local.tempSQL#
						</cfcase>
						<cfcase value="DATE">
							<cfset local.tempSQL = addReg_cf_DATE(mode='event', fieldID=local.cf.fieldID, customText=dateformat(local.cf.value,"m/d/yyyy"))>
							#local.tempSQL#
						</cfcase>
						<cfcase value="TEXTAREA">
							<cfset local.tempSQL = addReg_cf_TEXTAREA(mode='event', fieldID=local.cf.fieldID, customText=local.cf.value)>
							#local.tempSQL#
						</cfcase>
						</cfswitch>
					</cfloop>

					<!--- forceselect sub events --->
					<cfset local.qrySubEvents = getForceSelectSubEvents(eventID=local.eventID, rateID=int(val(arguments.event.getValue('ev_rateID'))),
						defaultTimeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'), memberID=int(val(local.activeMemberID)))>

					<cfloop query="local.qrySubEvents">
						select @subregistrantID = null, @GLAccountID = null, @invoiceProfileID = null, @invoiceID = null, @detail = null, 
							@accrualDate = null, @deferredDate = null, @deferredDateStr = null;

						-- add registrant and link to parent registrant
						EXEC dbo.ev_addRegistrant @registrationID=#local.qrySubEvents.subEventRegistrationID#, 
							@memberID=@assignedToMemberID, @recordedOnSiteID=@siteid, @rateID=#local.qrySubEvents.subEventRateID#, 
							@dateRegistered=@transDate, @identificationMethod='CP_ADMIN', @registrantID=@subregistrantID OUTPUT;
						IF @subregistrantID = 0 RAISERROR('unable to add registrant',16,1);

						<cfif len(local.overrideEmailAddress)>
							EXEC dbo.ams_insertUpdateEmailAppOverrides @itemType='eventreg', @itemID=@subregistrantID, @email='#local.overrideEmailAddress.replace("'","''","all")#';
						</cfif>

						UPDATE dbo.ev_registrants
						SET parentRegistrantID = @registrantID
						where registrantID = @subregistrantID;

						set @GLAccountID = #local.qrySubEvents.useGLAccountID#;
						select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
						select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

						SELECT Top 1 @detail = cl.contentTitle, @accrualDate = et.startTime
						FROM dbo.ev_events as e 
						inner join dbo.ev_times as et on e.eventID = et.eventID   
						INNER JOIN dbo.sites as s on s.siteID = @siteid AND s.defaultTimeZoneID = et.timeZoneID
						INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
						WHERE e.eventID = #local.qrySubEvents.subEventID#;

						select @detail = @detail + isnull(' - ' + rateName,'')
						from dbo.ev_rates 
						where rateID = #local.qrySubEvents.subEventRateID#;

						-- if necessary, create invoice assigned to payer based on invoice profile
						IF @invoiceID is null BEGIN
							EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
								@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@accrualDate, 
								@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

							insert into @tblInvoices (invoiceID, invoiceProfileID)
							values (@invoiceID, @invoiceProfileID);

							IF @registrationMerchantProfiles is not null
								EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
						END

						-- this date is used if we need to create deferred entries. We will use the LATEST of event start date, date registered aka transaction date.
						select @deferredDate = case when @accrualDate > @transDate then @accrualDate else @transDate end;
						select @deferredDateStr = convert(varchar(10),@deferredDate,101);

						-- handle deferred revenue
						select @xmlSchedule = null, @deferredGLAccountID = null, @rateTransactionID = null;
						select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
						IF @deferredGLAccountID is not null
							set @xmlSchedule = '<rows><row amt="#val(local.qrySubEvents.subEventRate)#" dt="' + @deferredDateStr + '" /></rows>';

						-- record rate transaction
						<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qrySubEvents.useGLAccountID, saleAmount=val(local.qrySubEvents.subEventRate), 
														transactionDate="#dateformat(local.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
														stateIDForTax=local.taxInfoStruct.stateIDForTax, zipForTax=local.taxInfoStruct.zipForTax)>
						set @taxAmount = #val(local.strTaxRate.totalTaxAmt)#;
						EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
							@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID,
							@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, 
							@parentTransactionID=null, @amount=#val(local.qrySubEvents.subEventRate)#, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, 
							@invoiceID=@invoiceID, @stateIDForTax=@stateIDforTax, @zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, 
							@xmlSchedule=@xmlSchedule, @transactionID=@rateTransactionID OUTPUT;
						EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@rateTransactionID, 
							@itemType='Rate', @itemID=@subregistrantID, @subItemID=null;
					</cfloop>

					<cfset local.tempSQL = saveRegistrant_insert_tickets(event=arguments.event, registrationID=local.registrationID, eventAdminSiteResourceID=local.eventAdminSiteResourceID, objAccounting=local.objAccounting, objResourceCustomFields=local.objResourceCustomFields, taxInfoStruct=local.taxInfoStruct)>
					#local.tempSQL#

					-- close any invoices in this registration
					declare @invoiceIDList varchar(max);
					select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10)) from @tblInvoices;
					if len(@invoiceIDList) > 0
						EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=@invoiceIDList;
				COMMIT TRAN

				on_regcomplete:
				SELECT 1 as success, @regCapReached as regCapReached;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addRegistrantSQL>
	</cffunction>

	<cffunction name="saveRegistrant_update" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="strEvent" type="struct" required="true">
		<cfargument name="qryRegistrantRoles" type="query" required="true">

		<cfset var local = structNew()>
		<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event")>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>

		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.memberID = val(arguments.event.getValue('mid'))>
		<cfset local.activeMemberID = application.objMember.getActiveMemberID(local.memberID)>
		<cfset local.stateIDForTax = val(arguments.event.getValue('stateIDforTax',0))>
		<cfset local.zipForTax = arguments.event.getTrimValue('zipForTax','')>
		<cfset local.eventID = arguments.event.getValue('eid')>
		<cfset local.registrationID = arguments.strEvent.qryEventRegMeta.registrationID>
		<cfset local.registrantID = int(val(arguments.event.getValue('registrantID',0)))>
		<cfset local.eventRegistrantRoles = arguments.event.getTrimValue('evRegistrantRoles','')>
		<cfset local.qryGetRateAndEventGLAccountID = getRateandEventGLAccountID(eventID=local.eventID, rateID=arguments.event.getValue('ev_rateID'))>
		<cfset local.taxInfoStruct = { stateIDForTax=local.stateIDForTax, zipForTax=local.zipForTax, transactionDate=dateformat(now(),"m/d/yyyy") }>
		<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.taxInfoStruct.zipForTax, billingStateID=local.taxInfoStruct.stateIDForTax)>
		<cfif local.strBillingZip.isvalidzip>
			<cfset local.taxInfoStruct.zipForTax = local.strBillingZip.billingzip>
		<cfelse>
			<cfthrow message="Invalid State/Zip.">
		</cfif>
		<cfif arguments.event.valueExists('override_mcev_rateAmt_#arguments.event.getValue('ev_rateID')#_')>
			<cfset local.rateAmount = val(rereplace(arguments.event.getValue('override_mcev_rateAmt_#arguments.event.getValue('ev_rateID')#_'),"[^0-9.]","","ALL"))>
		<cfelse>
			<cfset local.rateAmount = val(local.objAdminEvent.getRateAmount(arguments.event.getValue('ev_rateID')))>
		</cfif>
		<cfset local.couponID = arguments.event.getValue('mcev_couponID',0)>
		<cfset local.discountAmt = arguments.event.getValue('mcev_discount',0)>

		<cfset local.hasAppliedCoupon = arguments.event.getValue('mcev_hasAppliedCoupon',0)>
		<cfset local.hasRateIDChanged = arguments.event.getValue('mcev_hasRateIDChanged',0)>
		<cfset local.hasRemovedCoupon = arguments.event.getValue('mcev_hasRemovedCoupon',0)>
	
		<!--- put registrant role custom fields into array --->
		<cfset local.arrRoleCustomFields = []>
		<cfloop list="#local.eventRegistrantRoles#" index="local.thisRoleCatID">
			<cfset local.qryEventRoleFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='EventAdmin', areaName='Role', csrid=local.eventAdminSiteResourceID, detailID=local.thisRoleCatID, hideAdminOnly=0)>
			<cfset local.xmlEventRoleCustomFields = xmlParse(local.qryEventRoleFieldsXML.returnXML).xmlRoot>
			<cfif arrayLen(local.xmlEventRoleCustomFields.xmlChildren)>
				<cfloop array="#local.xmlEventRoleCustomFields.xmlChildren#" index="local.thisfield">
					<cfset local.tmpAtt = local.thisfield.xmlattributes>
					<cfset local.tmpStr = { roleCategoryID=local.thisRoleCatID,
											fieldID=local.tmpAtt.fieldID,
											displayTypeCode=local.tmpAtt.displayTypeCode, 
											dataTypeCode=local.tmpAtt.dataTypeCode, 
											value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','') }>

					<cfif local.tmpStr.displayTypeCode eq 'DOCUMENT'>
						<cfset local.tmpStr.value = ''>
						<cfset local.newVal = arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','')>
						<cfset local.oldVal = arguments.event.getTrimValue('cf_oldDoc_#local.tmpAtt.fieldID#_','')>
						<cfset local.removeOldFile = arguments.event.getTrimValue('cf_remDoc_#local.tmpAtt.fieldID#_','')>
						<cfif not len(local.newVal) and val(local.oldVal) gt 0 and local.removeOldFile is 1>
							<cfset local.objResourceCustomFields.deleteFieldDocument(fieldID=local.tmpAtt.fieldID, siteResourceID=val(local.oldVal))>
						</cfif>
						<cfif len(local.newVal)>
							<cfset local.fileUploaded = TRUE>
							<cftry>
								<cfset local.newFile = local.objDocument.uploadFile('cf_#local.tmpAtt.fieldID#_')>
								<cfcatch type="any">
									<cfset local.fileUploaded = FALSE>
								</cfcatch> 
							</cftry>
							<cfif local.fileUploaded>
								<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
								<cfset local.uploadedFileName = local.newFile.clientFile>
								<cfset local.uploadedFileExt = local.newFile.clientFileExt>
							
								<!--- if there's an existing document, insert new version --->
								<cfif val(local.oldVal) gt 0>
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getDocumentLanguageID">
										SELECT dbo.fn_getDocumentLanguageIDFromSiteResourceID(#val(local.oldVal)#,#session.mcstruct.languageID#) as documentLanguageID
									</cfquery>

									<cfset local.documentVersionID = local.objDocument.insertVersion(orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), 
												sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), fileData=local.newFile, 
												documentLanguageID=local.getDocumentLanguageID.documentLanguageID,
												contributorMemberID=session.cfcuser.memberdata.memberID, recordedByMemberID=session.cfcuser.memberdata.memberID,
												author='', oldFileExt=local.uploadedFileExt)>
									
								<!--- otherwise add new document --->
								<cfelse>
									<cfset local.rootSectionID = local.objSection.getSectionFromSectionCode(siteID=arguments.event.getValue('mc_siteinfo.siteid'), sectionCode="MCAMSEventDocuments").sectionID>

									<cfset local.insertResults = local.objDocument.insertDocument(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
												resourceType='ApplicationCreatedDocument', parentSiteResourceID=local.eventAdminSiteResourceID,
												sectionID=local.rootSectionID, docTitle='', docDesc='', author='', fileData=local.newFile,
												isVisible=false, contributorMemberID=session.cfcuser.memberdata.memberid, recordedByMemberID=session.cfcuser.memberdata.memberid,
												oldFileExt=local.uploadedFileExt)>

									<cfset local.tmpStr.value = local.insertResults.documentSiteResourceID>
								</cfif>									
							</cfif>
						</cfif>
					</cfif>
					<cfset arrayAppend(local.arrRoleCustomFields,local.tmpStr)>
				</cfloop>
			</cfif>
		</cfloop>

		<!--- put custom fields and field types into array --->
		<cfset local.arrCustomFields = []>
		<cfset local.strCustomFields = structNew()>
		<cfset local.regCustomFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', areaName='Registrant', 
																						csrid=arguments.strEvent.qryEventMeta.siteResourceID, detailID=0, hideAdminOnly=0)>
		<cfset local.strCustomFields = xmlParse(local.regCustomFieldsXML.returnXML).xmlRoot>
		<cfif arrayLen(local.strCustomFields.xmlChildren)>
			<cfloop array="#local.strCustomFields.xmlChildren#" index="local.thisfield">
				<cfset local.tmpAtt = local.thisfield.xmlattributes>
				<cfset local.thisFieldName = "cf_#local.tmpAtt.fieldID#_">
				<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
										displayTypeCode=local.tmpAtt.displayTypeCode, 
										dataTypeCode=local.tmpAtt.dataTypeCode, 
										offerQty=local.tmpAtt.supportQty,
										offerAmount=local.tmpAtt.supportAmt,
										fieldinventory=local.tmpAtt.fieldinventory, 
										qtyIndivAmt=0,
										GLAccountID=val(local.tmpAtt.GLAccountID),
										qtyRemoveValue='',
										value=arguments.event.getTrimValue('#local.thisFieldName#',''),
										arrQtyAmt=[], 
										strOptionAmt={} }>
				<cfif val(local.tmpStr.GLAccountID) eq 0>
					<cfset local.tmpStr.GLAccountID = val(local.qryGetRateAndEventGLAccountID.GLAccountID)>
				</cfif>

				<cfif local.tmpStr.displayTypeCode eq 'TEXTBOX' and local.tmpStr.offerAmount eq 1>
					<cfset local.tmpStr.value = val(ReReplace(local.tmpStr.value,'[^0-9\.]','','ALL'))>

					<cfif local.tmpStr.offerQty eq 1>
						<cfset local.tmpStr.qtyIndivAmt = abs(local.tmpAtt.amount)>
						<cfset local.tmpStr.qtyRemoveValue = arguments.event.getTrimValue('cfqty_#local.tmpAtt.fieldID#_','')>

						<cfif local.tmpStr.value gt 0>
							<cfset local.strQtyAmtTax = structNew()>
							<cfset local.strTaxArgs = { saleGLAccountID=local.tmpStr.GLAccountID, saleAmount=local.tmpStr.qtyIndivAmt, 
														transactionDate="#dateformat(local.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
														stateIDForTax=local.taxInfoStruct.stateIDForTax, zipForTax=local.taxInfoStruct.zipForTax }>
							
							<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
							<cfset local.strQtyAmtTax[local.tmpStr.qtyIndivAmt] = val(local.strTaxRate.totalTaxAmt)>

							<cfloop from="1" to="#val(local.tmpStr.value)#" index="local.thisQtyNum">
								<cfset local.qtyStr = { amount=local.tmpStr.qtyIndivAmt, taxAmount=local.strQtyAmtTax[local.tmpStr.qtyIndivAmt] }>
								<cfset local.thisFieldOverrideName = 'override_#local.thisFieldName#qty#local.thisQtyNum#_'>
								<cfif arguments.event.valueExists('#local.thisFieldOverrideName#') and local.qtyStr.amount neq rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
									<cfset local.thisQtyActualAmount = rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
									<cfif not structKeyExists(local.strQtyAmtTax,local.thisQtyActualAmount)>
										<cfset local.strTaxArgs.saleAmount = local.thisQtyActualAmount>
										<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
										<cfset local.strQtyAmtTax[local.thisQtyActualAmount] = val(local.strTaxRate.totalTaxAmt)>
									</cfif>
									<cfset local.qtyStr.amount = local.thisQtyActualAmount>
									<cfset local.qtyStr.taxAmount = local.strQtyAmtTax[local.thisQtyActualAmount]>
								</cfif>
								<cfset arrayAppend(local.tmpStr.arrQtyAmt,local.qtyStr)>
							</cfloop>
						</cfif>
					<cfelseif local.tmpStr.offerQty eq 0 and arguments.event.valueExists('override_#local.thisFieldName#')>
						<cfset local.tmpStr.value = rereplace(arguments.event.getTrimValue('override_#local.thisFieldName#'),"[^0-9.]","","ALL")>
					</cfif>
				<cfelseif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpStr.displayTypeCode) and local.tmpStr.offerAmount eq 1 and len(local.tmpStr.value)>
					<cfset local.strOptionAmtTax = structNew()>
					<cfset local.strTaxArgs = { saleGLAccountID=local.tmpStr.GLAccountID, saleAmount=0, 
												transactionDate="#dateformat(local.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
												stateIDForTax=local.taxInfoStruct.stateIDForTax, zipForTax=local.taxInfoStruct.zipForTax }>

					<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
						<cfif listFind(local.tmpStr.value,local.thisoption.xmlAttributes.valueID)>
							<cfset local.thisFieldOverrideName = 'override_#local.thisFieldName##local.thisoption.xmlAttributes.valueID#_'>
							<cfset local.thisOptionAmount = val(local.thisoption.xmlAttributes.amount)>
							<cfif arguments.event.valueExists('#local.thisFieldOverrideName#')>
								<cfset local.thisOptionAmount = val(rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL"))>
							</cfif>
							<cfif not structKeyExists(local.strOptionAmtTax,local.thisOptionAmount)>
								<cfset local.strTaxArgs.saleAmount = local.thisOptionAmount>
								<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
								<cfset local.strQtyAmtTax[local.thisOptionAmount] = val(local.strTaxRate.totalTaxAmt)>
							</cfif>
							<cfset local.tmpStr.strOptionAmt[local.thisoption.xmlAttributes.valueID] = { amount=local.thisOptionAmount, taxAmount=local.strQtyAmtTax[local.thisOptionAmount] }>
						</cfif>
					</cfloop>
				</cfif>

				<cfset arrayAppend(local.arrCustomFields,local.tmpStr)>
			</cfloop>
		</cfif>

		<!--- get all existing transactions for this registration --->
		<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.registrantID#">
			<cfprocresult name="local.qryTotals" resultset="1">
			<cfprocresult name="local.qryRegTransactions" resultset="2">
			<cfprocresult name="local.qryPaymentAllocations" resultset="3">
		</cfstoredproc>

		<!--- set couponID and discount for reapplying the coupon to newly selected rate if applicable --->
		<cfif local.hasAppliedCoupon AND local.hasRateIDChanged AND NOT local.hasRemovedCoupon>
			<cfset local.validateResult = validateCouponCode(mcproxy_siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
				couponCode=arguments.event.getValue('mcev_AppliedCouponCode',''), memberID=local.activeMemberID, 
				rateID=arguments.event.getValue('ev_rateID'), rateAmtOverride=local.rateAmount)>

			<cfif local.validateResult.isValidCoupon>
				<cfset local.couponID = local.validateResult.couponID>
				<cfset local.discountAmt = local.validateResult.discount>
			</cfif>
		</cfif>

		<cfset local.overrideEmailAddress = arguments.event.getTrimValue('regEmail','')>
		<cfif len(local.overrideEmailAddress)>
			<cfset local.registrantEmail = application.objMember.getMainEmail(memberID=local.memberID).email>
			<cfset local.currentOverrideEmail = local.objAdminEvent.getRegistrantOverrideEmail(registrantID=local.registrantID)>
			
			<cfif NOT isValid("regex",local.overrideEmailAddress,application.regEx.email) 
				OR local.overrideEmailAddress EQ local.registrantEmail
				OR local.overrideEmailAddress EQ local.currentOverrideEmail>
				<cfset local.overrideEmailAddress = "">
			</cfif>
		</cfif>

		<cfsavecontent variable="local.updateRegistrantSQL">
			<cfoutput>
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @invoiceid int, @orgID int, @siteID int, @loggedInMemberID int, @statsSessionID int, @eventID int,
					@GLAccountID int, @QuestionGLAccountID int, @registrantID int, @oldRateID int, @newRateID int, 
					@oldassignedToMemberID int, @newrateTransactionID int, @oldpaymentID int, @trashID int, @adjTransactionID int,
					@customTypeID int, @customTransactionIDForAdj int, @oldValueID int, @newValueID int, @olddetailID int, 
					@newCustomTransactionID int, @minDetailID int, @fieldID int, @oldpaymentTransactionID int, @ARdetailID int, 
					@ALdetailID int, @stateIDforTax int, @zipForTax varchar(25), @invoiceProfileID int, @deferredGLAccountID int, 
					@oldtotalRegRateFee decimal(18,2), @oldRegRateFee decimal(18,2), @dataID int, @oldRegRateFeeNeg decimal(18,2), @newtotalRegRateFee decimal(18,2), 
					@oldpaymentamountToAdjust decimal(18,2), @amtLeftToAdjust decimal(18,2), @EventsAppTypeID int, @oldtotalRegCustomFee decimal(18,2),
					@oldtotalRegCustomFeeNeg decimal(18,2), @newtotalRegCustomFee decimal(18,2), @invoiceNumber varchar(19), @deferredDateStr varchar(10), 
					@customText varchar(max), @invoiceIDList varchar(max), @oldcustomText varchar(max), @detail varchar(max), @oldRegCustomFee decimal(18,2),
					@eventRoles varchar(max), @xmlSchedule xml, @valueID int, @nowDate datetime, @accrualDate datetime, @deferredDate datetime, 
					@registrationMerchantProfiles varchar(1000), @taxAmount decimal(18,2), @oldRegPackageFee decimal(18,2), @oldRegPackageFeeNeg decimal(18,2), @thisSubItemID int,
					@instanceID int, @ticketTransactionID int, @TicketGLAccountID int, @seatID int, @guestRegistrantID int, @registrantTypeID int, 
					@oldQtyVal int, @rateTransactionID int, @assignedToMemberID int, @transDate datetime, @eventTitle varchar(200), 
					@customTransactionID int, @oldRegCustomFeeNeg decimal(18,2), @newQtyVal int, @minSalesTID int, @allocAmount decimal(18,2),  @minPaymentTID int, @totalQty int, 
					@unAllocAmount decimal(18,2), @dueAmount decimal(18,2), @totalAllocAmount decimal(18,2), @currentInventory int, @fieldInventoryLimit int, @newInventoryCount int, 
					@invDetail varchar(max), @detailInt int, @counter int, @detailAmt decimal(18,2), @inventoryReducedTo int, @invDetailSold varchar(max), @couponID int, 
					@discountAmount decimal(18,2), @maxOverallUsageCount int, @maxMemberUsageCount int, @redemptionCount int, @redemptionCountPerMember int, @couponCode varchar(15),
					@couponTransactionID int, @couponDiscountAmt decimal(18,2), @evRedemptionCount int, @tids xml, @vidPool xml, @couponAdjusteeTransactionID int, @tr_AdjustTrans int;
				DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int, amount decimal(18,2));
				DECLARE @tblExistingTrans TABLE (transactionID int PRIMARY KEY, detail varchar(max), amount decimal(18,2), itemType varchar(30), itemID int, subItemID int, taID int);
				DECLARE @tblOrigPaymentTransactions TABLE (paymentTransactionID int, allocatedAmountToReg decimal(18,2));
				DECLARE @tblCurrentPaymentTransactions TABLE (paymentTransactionID int, allocatedAmountToReg decimal(18,2));
				DECLARE @tblPaymentForReallocationTransactions TABLE (paymentTransactionID int, amountAvailable decimal(18,2));
				DECLARE @tblSalesDue TABLE (saleTransactionID int, amountDue decimal(18,2));
				DECLARE @tblMonetaryQtyFieldDetails TABLE (qty int, amount decimal(18,2), taxAmount decimal(18,2));
			
				set @nowDate = getdate();
				set @orgID = #arguments.event.getValue('mc_siteinfo.orgid')#;
				set @siteID = #arguments.event.getValue('mc_siteinfo.siteid')#;
				set @loggedInMemberID = #val(session.cfcuser.memberdata.memberid)#;
				set @statsSessionID = #val(session.cfcUser.statsSessionID)#;
				set @registrantID = #local.registrantID#;
				set @eventID = #val(local.eventID)#;
				set @stateIDforTax = <cfif local.stateIDForTax gt 0>#local.stateIDForTax#<cfelse>null</cfif>;
				set @zipForTax = <cfif len(local.zipForTax)>'#local.zipForTax#'<cfelse>null</cfif>;
				set @eventRoles = '#local.eventRegistrantRoles#';
				set @transDate = @nowDate;
				set @GLAccountID = #val(local.qryGetRateAndEventGLAccountID.GLAccountID)#;
				set @EventsAppTypeID = dbo.fn_getApplicationTypeIDFromName('Events');
				set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');

				select @assignedToMemberID = m.activeMemberID
				from dbo.ev_registrants as r
				inner join dbo.ams_members as m on m.memberID = r.memberID
				where r.registrantID = @registrantID;

				select @registrationMerchantProfiles = NULLIF(dbo.sortedIntList(profileID), '')
				from dbo.ev_registrants r
				inner join dbo.ev_registrationMerchantProfiles regmp on regmp.registrationID = r.registrationID and r.registrantID = @registrantID;

				-- get detail and accuralDate for the event. accrual date is used in custom fields even if rate doesnt change.
				SELECT TOP 1 @accrualDate = et.startTime, @eventTitle = eventcontent.contentTitle
				FROM dbo.ev_events as e 
				inner join dbo.ev_times et on e.eventID = et.eventID   
				INNER JOIN dbo.sites s on s.siteID = @siteid AND s.defaultTimeZoneID = et.timeZoneID
				inner join dbo.cms_contentLanguages as eventContent on eventContent.contentID = e.eventContentID and eventContent.languageID = 1
				WHERE e.eventID = @eventID;

				-- this date is used if we need to create deferred entries. We will use the LATEST of event start date, now date
				select @deferredDate = case when @accrualDate > @nowDate then @accrualDate else @nowDate end;
				select @deferredDateStr = convert(varchar(10),@deferredDate,101);

				-- get all existing transactions for this registration
				<cfloop query="local.qryRegTransactions">
					INSERT INTO @tblExistingTrans (transactionID, detail, amount, itemType, itemID, subItemID, taID) 
					VALUES (
						#local.qryRegTransactions.transactionID#, '#replace(local.qryRegTransactions.detail,"'","''","ALL")#', 
						#local.qryRegTransactions.amount#, '#local.qryRegTransactions.itemType#', #local.qryRegTransactions.itemID#,
						#val(local.qryRegTransactions.subItemID)#, #local.qryRegTransactions.taID#
					);
				</cfloop>

				-- get all existing payment allocations for this registration
				<cfloop query="local.qryPaymentAllocations">
					INSERT INTO @tblOrigPaymentTransactions (paymentTransactionID, allocatedAmountToReg)
					VALUES (#local.qryPaymentAllocations.transactionID#, #local.qryPaymentAllocations.allocatedAmount#);
				</cfloop>


				-- put all open invoices used for this registration since they were already created and can be used for adjustments
				insert into @tblInvoices (invoiceID, invoiceProfileID)
				select distinct i.invoiceID, i.invoiceProfileID
				from dbo.fn_ev_registrantTransactions(@registrantID) as rt
				inner join dbo.tr_invoiceTransactions as it on it.orgID = rt.ownedByOrgID and it.transactionID = rt.transactionID
				inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
				where i.statusID = 1;


				BEGIN TRAN;

				-- update internal notes
				UPDATE dbo.ev_registrants 
				SET internalNotes = '#replace(arguments.event.getTrimValue('internalNotes',''),"'","''","ALL")#'
				WHERE registrantID = @registrantID;

				<cfif len(local.overrideEmailAddress)>
					EXEC dbo.ams_insertUpdateEmailAppOverrides @itemType='eventreg', @itemID=@registrantID, @email='#local.overrideEmailAddress.replace("'","''","all")#';
				</cfif>
			
				<!--- event roles --->
				<cfif arguments.qryRegistrantRoles.recordcount>
					-- clear any existing registrant roles
					delete from dbo.ev_registrantCategories where registrantID = @registrantID;

					-- clear any existing registrant role field data
					delete fd
					from dbo.cf_fieldData as fd 
					inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
					where fd.itemID=@registrantID
					and fd.itemType='EventRole'
					and fv.valueSiteResourceID is null;

					<cfif listlen(local.eventRegistrantRoles)>
						-- add new registrant roles
						insert into dbo.ev_registrantCategories(registrantID, categoryID)
						select @registrantID, listitem
						from dbo.fn_intListToTable(@eventRoles, ',');	

						-- add registrant role details
						<cfloop array="#local.arrRoleCustomFields#" index="local.cf">
							<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
								<cfloop list="#local.cf.value#" index="local.valueitem">
									<cfif val(local.valueitem) gt 0>
										set @fieldID = #val(local.cf.fieldID)#;
										set @valueID = #val(local.valueitem)#;
										set @dataID = null;

										EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@registrantID, @itemType='EventRole', 
											@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
									</cfif>
								</cfloop>
							<cfelseif len(local.cf.value)>
								set @fieldID = #val(local.cf.fieldID)#;
								set @detail =  '#replace(local.cf.value,"'","''","ALL")#';
								set @dataID = null;

								EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@registrantID, @itemType='EventRole', 
											@valueID=NULL, @fieldValue=@detail, @dataID=@dataID OUTPUT;
							</cfif>
						</cfloop>
					</cfif>
				</cfif>

				<!--- voiding coupon discount adjustment on coupon removal or rate change --->
				<cfif local.hasRemovedCoupon OR (local.hasAppliedCoupon AND local.hasRateIDChanged)>
					select top 1 @couponTransactionID = d.transactionID, @couponDiscountAmt = t.amount, @couponAdjusteeTransactionID = tr.appliedToTransactionID
					from dbo.tr_transactionDiscounts d
					inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = d.transactionID and t.statusID = 1
					inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_AdjustTrans and tr.transactionID = d.transactionID
					where d.orgID = @orgID
					and d.isActive = 1
					and d.itemType = 'EventRate'
					and d.itemID = @registrantID;

					IF @couponTransactionID IS NOT NULL BEGIN
						EXEC dbo.tr_voidTransaction @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @transactionID=@couponTransactionID, @checkInBounds=1, @vidPool=@vidPool OUTPUT, 
							@tids=@tids OUTPUT;		

						-- recalc the transaction amounts in the tblExistingTrans query so adjustment amounts will be handled correctly
						UPDATE @tblExistingTrans
						SET amount = amount + @couponDiscountAmt
						WHERE transactionID = @couponAdjusteeTransactionID;

						-- mark the discount row as not active so it doesnt count against redemption
						UPDATE dbo.tr_transactionDiscounts
						set isActive = 0
						where transactionID = @couponTransactionID
						and isActive = 1;
					END
				</cfif>

				<!--- rate --->
				select @oldRateID = rateID from dbo.ev_registrants where registrantID = @registrantID; 
				set @oldRateID = isnull(@oldRateID,0);
				set @newrateID = #int(val(arguments.event.getValue('ev_rateID',0)))#;
				set @newtotalRegRateFee = #local.rateAmount#;

				select @rateTransactionID = min(transactionID), @oldtotalRegRateFee = sum(amount)
				from @tblExistingTrans 
				where itemType = 'Rate' 
				and itemID = @registrantID;

				<!--- if rate changed --->
				IF @oldRateID <> @newRateID OR @newtotalRegRateFee <> @oldtotalRegRateFee BEGIN

					-- update record to reflect new rateid
					IF @oldRateID <> @newRateID 
						UPDATE dbo.ev_registrants SET rateID = @newRateID WHERE registrantID = @registrantID;

					WHILE @rateTransactionID is not null BEGIN
						set @oldRegRateFee = null;

						select @oldRegRateFee = isnull(amount,0)
						from @tblExistingTrans 
						where itemType = 'Rate' 
						and itemID = @registrantID
						and transactionID = @rateTransactionID;

						-- adjust out rate sale transactions
						IF @oldRegRateFee > 0 BEGIN
							select @invoiceProfileID=null, @invoiceID=null, @oldRegRateFeeNeg=null;

							select @invoiceProfileID = gl.invoiceProfileID
								from dbo.tr_glAccounts as gl
								inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.glAccountID
								where t.transactionID = @rateTransactionID;
							select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
						
							-- if necessary, create invoice assigned to registrant based on invoice profile
							IF @invoiceID is null BEGIN
								EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
									@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
									@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
			
								insert into @tblInvoices (invoiceID, invoiceProfileID)
								values (@invoiceID, @invoiceProfileID);

								IF @registrationMerchantProfiles is not null
									EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
							END		
						
							set @oldRegRateFeeNeg = @oldRegRateFee * -1;
							EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@loggedInMemberID, 
								@statsSessionID=@statsSessionID, @amount=@oldRegRateFeeNeg, @taxAmount=null, @transactionDate=@nowDate, 
								@autoAdjustTransactionDate=1, @saleTransactionID=@rateTransactionID, @invoiceID=@invoiceID, @byPassTax=0, 
								@byPassAccrual=0, @xmlSchedule=null, @transactionID=@adjTransactionID OUTPUT;
						END

						-- mark any discount rows as not active so they doesnt count against redemption
						; WITH allAdjToRateTrans AS (
							select transactionID
							from dbo.tr_relationships
							where orgID = @orgID
							and typeID = @tr_AdjustTrans
							and appliedToTransactionID = @rateTransactionID
						)
						UPDATE td
						set td.isActive = 0
						from dbo.tr_transactionDiscounts as td
						inner join allAdjToRateTrans on allAdjToRateTrans.transactionID = td.transactionID
						where td.orgID = @orgID
						and td.isActive = 1;

						-- disable tr_application for old rate trans
						UPDATE dbo.tr_applications SET [status] = 'D' WHERE transactionID = @rateTransactionID;

						select @rateTransactionID = min(transactionID)
						from @tblExistingTrans 
						where itemType = 'Rate' 
						and itemID = @registrantID
						and transactionID > @rateTransactionID;
					END

					select @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null;
					select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where glAccountID = @GLAccountID;
					select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				
					-- if necessary, create invoice assigned to registrant based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
	
						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);

						IF @registrationMerchantProfiles is not null
							EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
					END

					-- record new rate transaction
					select @detail = @eventTitle + isnull(' - ' + rateName,'') from dbo.ev_rates where rateID = @newRateID;

					-- handle deferred revenue
					select @xmlSchedule = null, @deferredGLAccountID = null;
					select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
					IF @deferredGLAccountID is not null
						set @xmlSchedule = '<rows><row amt="' + cast(@newtotalRegRateFee as varchar(10)) + '" dt="' + @deferredDateStr + '" /></rows>';

					<cfset local.strTaxRate = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=val(local.qryGetRateAndEventGLAccountID.GLAccountID),
													saleAmount=local.rateAmount, transactionDate="#dateformat(local.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
													stateIDForTax=local.taxInfoStruct.stateIDForTax, zipForTax=local.taxInfoStruct.zipForTax)>
					set @taxAmount = #val(local.strTaxRate.totalTaxAmt)#;
					EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
						@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID,
						@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, 
						@parentTransactionID=null, @amount=@newtotalRegRateFee, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID,
						@stateIDforTax=@stateIDforTax, @zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, 
						@bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, 
						@transactionID=@newRateTransactionID OUTPUT;

					-- move the old children transactions to the new rate transactionid
					UPDATE t
					SET t.parentTransactionID = @newRateTransactionID
					FROM dbo.tr_transactions as t
					INNER JOIN @tblExistingTrans as tbl 
						on t.ownedByOrgID = @orgID
						and tbl.transactionID = t.parentTransactionID
						and tbl.itemType = 'Rate' 
						AND tbl.itemID = @registrantID;

					-- record rate-app transaction
					EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@newRateTransactionID, 
						@itemType='Rate', @itemID=@registrantID, @subItemID=null;
						
					-- get the new value since it changed
					set @rateTransactionID = @newRateTransactionID;
				END

				-- promo code applied
				<cfif local.couponID gt 0 and local.discountAmt gt 0>
					IF @newRateTransactionID is null BEGIN
						select @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null;
						
						select @invoiceProfileID = gl.invoiceProfileID
							from dbo.tr_glAccounts as gl
							inner join dbo.tr_transactions as t 
								on t.ownedByOrgID = @orgID
								and t.creditGLAccountID = gl.glAccountID
								and t.transactionID = @rateTransactionID;
						select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
					
						-- if necessary, create invoice assigned to registrant based on invoice profile
						IF @invoiceID is null BEGIN
							EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
								@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
								@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
		
							insert into @tblInvoices (invoiceID, invoiceProfileID)
							values (@invoiceID, @invoiceProfileID);

							IF @registrationMerchantProfiles is not null
								EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
						END
					END

					select @couponID = null, @couponCode = null, @discountAmount = null, @maxOverallUsageCount = null, @maxMemberUsageCount = null, 
						@redemptionCount = null, @redemptionCountPerMember = null, @evRedemptionCount = null;

					set @couponID = #int(local.couponID)#;
					set @discountAmount = #local.discountAmt# * - 1;

					-- check if coupon max usage count met
					select @couponCode = couponCode, @maxOverallUsageCount = maxOverallUsageCount, @maxMemberUsageCount = maxMemberUsageCount,
						@evRedemptionCount = eventsXML.value('(/event/ev/e[text()=sql:variable("@eventID")]/@rc)[1]', 'int')
					from dbo.tr_coupons
					where couponID = @couponID;

					set @evRedemptionCount = isnull(@evRedemptionCount,1);

					; with redemptions as(
						select distinct td.itemType, td.itemID, td.redemptionCount, 
							case when m.memberID is not null then td.redemptionCount else 0 end as memberRedemptionCount
						from dbo.tr_coupons as c
						inner join dbo.tr_transactionDiscounts as td on td.orgID = @orgID 
							and td.couponID = c.couponID 
							and td.isActive = 1
							and c.couponID = @couponID
						inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
						left outer join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedToMemberID and m.activeMemberID = @assignedToMemberID
					)
					select @redemptionCount = SUM(redemptionCount), @redemptionCountPerMember = SUM(memberRedemptionCount) 
					from redemptions;					

					IF @maxOverallUsageCount > 0 AND ISNULL(@redemptionCount,0) + @evRedemptionCount > @maxOverallUsageCount
						UPDATE dbo.ev_registrants
						SET isFlagged = 1,
							internalNotes = isnull(internalNotes,'') + ' - Overall Redemptions Count for Coupon Code(' + @couponCode + ') exceeded; '
						WHERE registrantID = @registrantID;

					IF @maxMemberUsageCount > 0 AND ISNULL(@redemptionCountPerMember,0) + @evRedemptionCount > @maxMemberUsageCount
						UPDATE dbo.ev_registrants
						SET isFlagged = 1,
							internalNotes = isnull(internalNotes,'') + ' - Overall Redemptions Count Per Member for Coupon Code(' + @couponCode + ') exceeded; '
						WHERE registrantID = @registrantID;

					EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
						@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@transDate, 
						@saleTransactionID=@rateTransactionID, @invoiceID=@invoiceID, @couponID=@couponID, 
						@itemType='EventRate', @itemID=@registrantID, @redemptionCount=@evRedemptionCount, 
						@transactionID=@adjTransactionID OUTPUT;
				</cfif>

				<!--- custom fields --->
				<cfloop array="#local.arrCustomFields#" index="local.cf">
					<cfswitch expression="#local.cf.displayTypeCode#">
					<cfcase value="TEXTBOX">
						<cfset local.tempSQL = editReg_cf_TEXTBOX(mode='event', registrantID=local.registrantID, fieldID=local.cf.fieldID, customText=local.cf.value,
																offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, fieldinventory=val(local.cf.fieldinventory), 
																objAccounting=local.objAccounting, GLAccountID=val(local.cf.GLAccountID), taxInfoStruct=local.taxInfoStruct, 
																removeQtyItems=local.cf.qtyRemoveValue, arrQtyAmt=local.cf.arrQtyAmt)>
						#local.tempSQL#
					</cfcase>
					<cfcase value="SELECT,RADIO,CHECKBOX">
						<cfset local.tempSQL = editReg_cf_SELECTRADIOCHECKBOX(mode='event', registrantID=local.registrantID, fieldID=local.cf.fieldID, valueIDList=local.cf.value,
																GLAccountID=val(local.cf.GLAccountID), offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, strOptionAmt=local.cf.strOptionAmt)>
						#local.tempSQL#
					</cfcase>
					<cfcase value="DATE">
						<cfset local.tempSQL = editReg_cf_DATE(mode='event', registrantID=local.registrantID, fieldID=local.cf.fieldID, customText=dateformat(local.cf.value,"m/d/yyyy"))>
						#local.tempSQL#
					</cfcase>
					<cfcase value="TEXTAREA">
						<cfset local.tempSQL = editReg_cf_TEXTAREA(mode='event', registrantID=local.registrantID, fieldID=local.cf.fieldID, customText=local.cf.value)>
						#local.tempSQL#
					</cfcase>
					</cfswitch>
				</cfloop>

				<cfset local.tempSQL = saveRegistrant_update_tickets(event=arguments.event, registrantID=local.registrantID, eventID=arguments.event.getValue('eid'), 
													registrationID=local.registrationID, rateID=arguments.event.getValue('ev_rateID'), 
													eventAdminSiteResourceID=local.eventAdminSiteResourceID, objAccounting=local.objAccounting, 
													objResourceCustomFields=local.objResourceCustomFields, taxInfoStruct=local.taxInfoStruct)>
				#local.tempSQL#
				
				-- close any invoices in this registration
				set @invoiceIDList = null;
				select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10)) from @tblInvoices;
				if len(@invoiceIDList) > 0
					EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=@invoiceIDList;

				-- sales transactions that are due
				INSERT INTO @tblSalesDue (saleTransactionID, amountDue)
				select tbl.transactionID, sum(ts.cache_amountAfterAdjustment) - sum(ts.cache_activePaymentAllocatedAmount)
				from dbo.fn_ev_registrantTransactions(@registrantID) as tbl
				inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = tbl.transactionID
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = t.transactionID	
				group by tbl.transactionID
				having sum(ts.cache_amountAfterAdjustment) - sum(ts.cache_activePaymentAllocatedAmount) > 0;


				-- get current payment transactions after any changes to deallocations

				; WITH currentRegTrans AS (
					select *
					from dbo.fn_ev_registrantTransactions(@registrantID)
				)
				insert into @tblCurrentPaymentTransactions (paymentTransactionID, allocatedAmountToReg)
				select tmpPay.transactionID, sum(atop.allocAmount) as allocatedAmount
				from currentRegTrans as tmpPay
				cross apply dbo.fn_tr_getAllocatedTransactionsofPayment(tmpPay.ownedByOrgID,tmpPay.transactionID) as atop
				inner join currentRegTrans as tmpRev on tmpRev.transactionID = atop.transactionID
				where tmpPay.typeID = 2
				group by tmpPay.transactionID;

				-- get amount of previous allocated payments now available for reallocation

				insert into @tblPaymentForReallocationTransactions (paymentTransactionID, amountAvailable)
				select o.paymentTransactionID, o.allocatedAmountToReg - isnull(c.allocatedAmountToReg,0)
				from @tblOrigPaymentTransactions o
				left outer join @tblCurrentPaymentTransactions c
					on c.paymentTransactionID = o.paymentTransactionID

				-- allocate payment to invoices
				select @minSalesTID = min(saleTransactionID) from @tblSalesDue;
				while @minSalesTID is not null BEGIN
					select @dueAmount=null, @minPaymentTID=null, @totalAllocAmount=0;

					select @dueAmount = amountDue from @tblSalesDue where saleTransactionID = @minSalesTID;

					select @minPaymentTID = min(paymentTransactionID) 
						from @tblPaymentForReallocationTransactions
						where amountAvailable > 0;
					while @minPaymentTID is not null begin
						select @unAllocAmount = null, @allocAmount=null;
						
						select @unAllocAmount = amountAvailable 
						from @tblPaymentForReallocationTransactions
						where paymentTransactionID = @minPaymentTID;

						if @unAllocAmount >= @dueAmount begin
							set @allocAmount = @dueAmount;
							set @unAllocAmount = @unAllocAmount - @dueAmount;
						end
						else begin 
							set @allocAmount = @unAllocAmount;
							set @unAllocAmount = 0;
						end

						set @totalAllocAmount = @totalAllocAmount + @allocAmount;

						EXEC dbo.tr_allocateToSale @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @amount=@allocAmount, @transactionDate=@nowDate, 
							@paymentTransactionID=@minPaymentTID, @saleTransactionID=@minSalesTID;

						update @tblPaymentForReallocationTransactions
						set amountAvailable = @unAllocAmount
						where paymentTransactionID = @minPaymentTID;

						IF @totalAllocAmount = @dueAmount
							BREAK;

						select @minPaymentTID = min(paymentTransactionID) 
							from @tblPaymentForReallocationTransactions
							where paymentTransactionID > @minPaymentTID
							and amountAvailable > 0;
					end
					
					select @minSalesTID = min(saleTransactionID) from @tblSalesDue where saleTransactionID > @minSalesTID;		
				END

				COMMIT TRAN;

				SELECT 1 as success;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.updateRegistrantSQL>
	</cffunction>
	
	<cffunction name="saveRegistrant_insert_tickets" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="eventAdminSiteResourceID" type="numeric" required="true">
		<cfargument name="objAccounting" type="any" required="true">
		<cfargument name="objResourceCustomFields" type="any" required="true">
		<cfargument name="taxInfoStruct" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event")>
		<cfset local.qryTicketDetails = getTicketForReg(registrationid=arguments.registrationID, rateID=arguments.event.getValue('ev_rateID'))>
		
		<cfif local.qryTicketDetails.recordcount>
			<cfset local.qryGetRateAndEventGLAccountID = getRateandEventGLAccountID(eventID=arguments.event.getValue('eid'), rateID=arguments.event.getValue('ev_rateID'))>
			
			<cfset local.formRegPackageInstanceNumStr = structNew()>
			<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
				<cfif left(local.thisField,16) eq "packageInstance_" and ListLen(local.thisField,"_") eq 4>
					<cfset local.packageID = GetToken(local.thisField,2,'_')>
					<cfif not structKeyExists(local.formRegPackageInstanceNumStr,local.packageID)>
						<cfset structInsert(local.formRegPackageInstanceNumStr, local.packageID, "", true)>
					</cfif>
					<cfset local.formRegPackageInstanceNumStr[local.packageID] = ListAppend(local.formRegPackageInstanceNumStr[local.packageID],GetToken(local.thisField,4,'_'))>
				</cfif>
			</cfloop>

			<cfquery name="local.qryTicketDetailsDistinct" dbtype="query">
				select distinct ticketID, ticketPackageID, ticketAssignSeats, ticketsInPackage, ticketName, ticketPackageName, qtyIncludedByRate, GLAccountID
				from [local].qryTicketDetails
			</cfquery>
			
			<cfsavecontent variable="local.addRegistrantSQL">
				<cfoutput>
				<cfloop query="local.qryTicketDetailsDistinct">
					<cfif structKeyExists(local.formRegPackageInstanceNumStr,local.qryTicketDetailsDistinct.ticketPackageID)>
						<cfquery name="local.qryTicketPackageAvailablePriceID" dbtype="query">
							select availablePriceID, ticketPackageAmount
							from [local].qryTicketDetails
							where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
							order by row
						</cfquery>

						<cfset local.thisTicketTicketID = local.qryTicketDetailsDistinct.ticketID>
						<cfset local.thisTicketPackageID = local.qryTicketDetailsDistinct.ticketPackageID>
						<cfset local.thisTicketPackageFirstRegPriceID = ListFirst(valueList(local.qryTicketPackageAvailablePriceID.availablePriceID))>
						<cfset local.thisTicketPackageFirstRegAmount = val(ListFirst(valueList(local.qryTicketPackageAvailablePriceID.ticketPackageAmount)))>
						<cfif local.qryTicketDetailsDistinct.GLAccountID gt 0>
							<cfset local.thisTicketGLAccountID = local.qryTicketDetailsDistinct.GLAccountID>
						<cfelse>
							<cfset local.thisTicketGLAccountID = local.qryGetRateAndEventGLAccountID.GLAccountID>
						</cfif>
						<cfset local.thisTicketSeats = local.qryTicketDetailsDistinct.ticketsInPackage>
						<cfset local.thisTicketPackageSaleDetail = local.qryTicketDetailsDistinct.ticketName & ' - ' & local.qryTicketDetailsDistinct.ticketPackageName>
					
						<cfset local.thisTicketPackageSelected = 0>
						<cfloop query="local.qryTicketPackageAvailablePriceID">
							<cfset local.thisTicketPackageSelected = local.thisTicketPackageSelected + arguments.event.getValue('ticketPackage_#local.thisTicketPackageID#_#local.qryTicketPackageAvailablePriceID.availablePriceID#',0)>
						</cfloop>
						<cfset local.thisTicketPackageQtyIncludedByRate = local.qryTicketDetailsDistinct.qtyIncludedByRate>
						<cfif val(local.thisTicketPackageFirstRegAmount) gt 0>
							<cfset local.strTaxCF = arguments.objAccounting.getTaxForUncommittedSale(saleGLAccountID=val(local.thisTicketGLAccountID), saleAmount=val(local.thisTicketPackageFirstRegAmount), 
																			transactionDate=arguments.taxInfoStruct.transactionDate, stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax)>
							<cfset local.thisTicketPackageTaxAmount = local.strTaxCF.totalTaxAmt>
						<cfelse>
							<cfset local.thisTicketPackageTaxAmount = 0>
						</cfif>
						<cfset local.thisTicketTotalPackageIncluded = local.thisTicketPackageSelected + local.thisTicketPackageQtyIncludedByRate>
						
						<cfset local.thisPackageInstanceNum = 0>
						<cfloop list="#listSort(local.formRegPackageInstanceNumStr[local.thisTicketPackageID],'numeric','asc')#" index="local.thisInstanceFormNum">
							<cfset local.thisPackageInstanceNum = local.thisPackageInstanceNum + 1>

							<cfif arguments.event.getValue('tpiAvailablePriceID_#local.thisTicketPackageID#_#local.thisInstanceFormNum#',0) gt 0>
								<cfset local.thisTicketPackageAvailablePriceID = arguments.event.getValue('tpiAvailablePriceID_#local.thisTicketPackageID#_#local.thisInstanceFormNum#')>
							<cfelse>
								<cfset local.thisTicketPackageAvailablePriceID = local.thisTicketPackageFirstRegPriceID>
							</cfif>

							<cfquery name="local.qryThisPackageAmount" dbtype="query">
								select ticketPackageAmount
								from [local].qryTicketDetails
								where ticketPackageID = #local.thisTicketPackageID#
								and availablePriceID = #local.thisTicketPackageAvailablePriceID#
							</cfquery>
							<cfset local.thisTicketPackageAmount = val(local.qryThisPackageAmount.ticketPackageAmount)>

							<!--- check if package price overrided --->
							<cfif arguments.event.valueExists('MCEVTP_#local.thisTicketPackageID#_#local.thisInstanceFormNum#') and val(rereplace(arguments.event.getValue('MCEVTP_#local.thisTicketPackageID#_#local.thisInstanceFormNum#'),"[^0-9.]","","ALL")) neq local.thisTicketPackageFirstRegAmount>
								<cfset local.thisTicketPackageAmount = val(rereplace(arguments.event.getValue('MCEVTP_#local.thisTicketPackageID#_#local.thisInstanceFormNum#'),"[^0-9.]","","ALL"))>
								<cfif val(local.thisTicketPackageAmount) gt 0>
									<cfset local.strTaxCF = arguments.objAccounting.getTaxForUncommittedSale(saleGLAccountID=val(local.thisTicketGLAccountID), saleAmount=val(local.thisTicketPackageAmount), 
												transactionDate=arguments.taxInfoStruct.transactionDate, stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax)>
									<cfset local.thisTicketPackageTaxAmount = local.strTaxCF.totalTaxAmt>
								<cfelse>
									<cfset local.thisTicketPackageTaxAmount = 0>
								</cfif>
							</cfif>

							<cfset local.thisPackageCustomFieldsXML = arguments.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', areaName='TicketPackage', csrid=arguments.eventAdminSiteResourceID, detailID=local.thisTicketPackageID, hideAdminOnly=0)>
							<cfset local.thisPackageTicketCustomFieldsXML = arguments.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', areaName='Ticket', csrid=arguments.eventAdminSiteResourceID, detailID=local.thisTicketTicketID, hideAdminOnly=0)>

							<cfset local.thisPackageCustomFieldsXML = xmlParse(local.thisPackageCustomFieldsXML.returnXML).xmlRoot>
							<cfset local.thisPackageTicketCustomFieldsXML = xmlParse(local.thisPackageTicketCustomFieldsXML.returnXML).xmlRoot>

							<!--- put package custom fields and field types into array --->
							<cfset local.arrCustomFields = []>
							<cfif arrayLen(local.thisPackageCustomFieldsXML.xmlChildren)>
								<cfloop array="#local.thisPackageCustomFieldsXML.xmlChildren#" index="local.thisfield">
									<cfset local.tmpAtt = local.thisfield.xmlattributes>
									<cfset local.thisFieldName = "ev_tpc_#local.thisTicketPackageID#_#local.thisInstanceFormNum#_#local.tmpAtt.fieldID#">
									<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID, 
															displayTypeCode=local.tmpAtt.displayTypeCode, 
															dataTypeCode=local.tmpAtt.dataTypeCode, 
															offerQty=local.tmpAtt.supportQty,
															offerAmount=local.tmpAtt.supportAmt,
															fieldinventory=local.tmpAtt.fieldinventory, 
															qtyIndivAmt=0,
															GLAccountID=val(local.tmpAtt.GLAccountID),
															value='',
															arrQtyAmt=[],
															strOptionAmt={} }>
									<cfif val(local.tmpStr.GLAccountID) eq 0>
										<cfset local.tmpStr.GLAccountID = local.thisTicketGLAccountID>
									</cfif>
									<cfif arguments.event.valueExists('#local.thisFieldName#')>
										<cfset local.tmpStr.value = arguments.event.getValue('#local.thisFieldName#')>
									</cfif>

									<cfif local.tmpStr.displayTypeCode eq 'TEXTBOX' and local.tmpStr.offerAmount eq 1>
										<cfset local.tmpStr.value = val(ReReplace(local.tmpStr.value,'[^0-9\.]','','ALL'))>

										<cfif local.tmpStr.offerQty eq 1>
											<cfset local.tmpStr.qtyIndivAmt = abs(local.tmpAtt.amount)>
											<cfif local.tmpStr.value gt 0>
												<cfset local.strQtyAmtTax = structNew()>
												<cfset local.strTaxArgs = { saleGLAccountID=local.tmpStr.GLAccountID, saleAmount=local.tmpStr.qtyIndivAmt, 
																			transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
																			stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax }>
												
												<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
												<cfset local.strQtyAmtTax[local.tmpStr.qtyIndivAmt] = val(local.strTaxRate.totalTaxAmt)>

												<cfloop from="1" to="#val(local.tmpStr.value)#" index="local.thisQtyNum">
													<cfset local.qtyStr = { amount=local.tmpStr.qtyIndivAmt, taxAmount=local.strQtyAmtTax[local.tmpStr.qtyIndivAmt] }>
													<cfset local.thisFieldOverrideName = 'override_#local.thisFieldName#_qty#local.thisQtyNum#_'>
													<cfif arguments.event.valueExists('#local.thisFieldOverrideName#') and local.qtyStr.amount neq ReReplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
														<cfset local.thisQtyActualAmount = ReReplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
														<cfif not structKeyExists(local.strQtyAmtTax,local.thisQtyActualAmount)>
															<cfset local.strTaxArgs.saleAmount = local.thisQtyActualAmount>
															<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
															<cfset local.strQtyAmtTax[local.thisQtyActualAmount] = val(local.strTaxRate.totalTaxAmt)>
														</cfif>
														<cfset local.qtyStr.amount = local.thisQtyActualAmount>
														<cfset local.qtyStr.taxAmount = local.strQtyAmtTax[local.thisQtyActualAmount]>
													</cfif>
													<cfset arrayAppend(local.tmpStr.arrQtyAmt,local.qtyStr)>
												</cfloop>
											</cfif>
										<cfelseif local.tmpStr.offerQty eq 0 and arguments.event.valueExists('override_#local.thisFieldName#')>
											<cfset local.tmpStr.value = ReReplace(arguments.event.getTrimValue('override_#local.thisFieldName#'),"[^0-9.]","","ALL")>
										</cfif>
									<cfelseif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpStr.displayTypeCode) and local.tmpStr.offerAmount eq 1 and len(local.tmpStr.value)>
										<cfset local.strOptionAmtTax = structNew()>
										<cfset local.strTaxArgs = { saleGLAccountID=local.tmpStr.GLAccountID, saleAmount=0, 
																	transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
																	stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax }>

										<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
											<cfif listFind(local.tmpStr.value,local.thisoption.xmlAttributes.valueID)>
												<cfset local.thisFieldOverrideName = 'override_#local.thisFieldName#_#local.thisoption.xmlAttributes.valueID#'>
												<cfset local.thisOptionAmount = val(local.thisoption.xmlAttributes.amount)>
												<cfif arguments.event.valueExists('#local.thisFieldOverrideName#')>
													<cfset local.thisOptionAmount = val(ReReplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL"))>
												</cfif>
												<cfif not structKeyExists(local.strOptionAmtTax,local.thisOptionAmount)>
													<cfset local.strTaxArgs.saleAmount = local.thisOptionAmount>
													<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
													<cfset local.strQtyAmtTax[local.thisOptionAmount] = val(local.strTaxRate.totalTaxAmt)>
												</cfif>
												<cfset local.tmpStr.strOptionAmt[local.thisoption.xmlAttributes.valueID] = { amount=local.thisOptionAmount, taxAmount=local.strQtyAmtTax[local.thisOptionAmount] }>
											</cfif>
										</cfloop>
									</cfif>
									<cfset arrayAppend(local.arrCustomFields,local.tmpStr)>
								</cfloop>
							</cfif>
						
							<cfset local.thisTicketPackageInstanceIncludedFromRate = 0>
							<cfif local.thisTicketPackageQtyIncludedByRate gt 0 and local.thisTicketPackageQtyIncludedByRate gte local.thisPackageInstanceNum>
								<cfset local.thisTicketPackageInstanceIncludedFromRate = 1>
							</cfif>
							
							set @instanceID = null;

							INSERT INTO dbo.ev_registrantPackageInstances (registrantID, ticketPackageID, includedFromRate, availablePriceID)
							VALUES (@registrantID, #local.thisTicketPackageID#, #val(local.thisTicketPackageInstanceIncludedFromRate)#, #local.thisTicketPackageAvailablePriceID#);
								SELECT @instanceID = SCOPE_IDENTITY();

							<cfif local.thisTicketPackageAmount gt 0 and val(local.thisTicketPackageInstanceIncludedFromRate) is 0>
								select @invoiceProfileID = null, @invoiceID = null, @ticketTransactionID = null;
								set @GLAccountID = #local.thisTicketGLAccountID#;
								select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
								select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
								set @taxAmount = #local.thisTicketPackageTaxAmount#;
								set @detail = left(@eventTitle + ' - #replace(local.thisTicketPackageSaleDetail,"'","''","ALL")#',500);

								-- if necessary, create invoice assigned to payer based on invoice profile
								IF @invoiceID is null BEGIN
									EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
										@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@accrualDate, 
										@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
				
									insert into @tblInvoices (invoiceID, invoiceProfileID)
									values (@invoiceID, @invoiceProfileID);

									IF @registrationMerchantProfiles is not null
										EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
								END

								-- handle deferred revenue
								select @xmlSchedule = null, @deferredGLAccountID = null;
								select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
								IF @deferredGLAccountID is not null
									set @xmlSchedule = '<rows><row amt="#local.thisTicketPackageAmount#" dt="' + @deferredDateStr + '" /></rows>';

								EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
									@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
									@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @parentTransactionID=@rateTransactionID, 
									@amount=#local.thisTicketPackageAmount#, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
									@zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, 
									@transactionID=@ticketTransactionID OUTPUT;
								EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@ticketTransactionID, 
									@itemType='TicketPackInst', @itemID=@instanceID, @subItemID=null;
							</cfif>
						
							<!--- loop over the package custom fields --->
							<cfloop array="#local.arrCustomFields#" index="local.cf">
								<cfswitch expression="#local.cf.displayTypeCode#">
									<cfcase value="TEXTBOX">
										<cfset local.tempSQL = addReg_cf_TEXTBOX(mode='ticketpackage', fieldID=local.cf.fieldID, customText=local.cf.value,
																				offerQty=local.cf.offerQty,
																				offerAmount=local.cf.offerAmount,
																				fieldinventory=val(local.cf.fieldinventory),
																				objAccounting=arguments.objAccounting,
																				GLAccountID=val(local.cf.GLAccountID),
																				taxInfoStruct=arguments.taxInfoStruct,
																				arrQtyAmt=local.cf.arrQtyAmt)>
										#local.tempSQL#
									</cfcase>
									<cfcase value="SELECT,RADIO,CHECKBOX">
										<cfset local.tempSQL = addReg_cf_SELECTRADIOCHECKBOX(mode='ticketpackage', fieldID=local.cf.fieldID, valueIDList=local.cf.value, GLAccountID=val(local.cf.GLAccountID), 
																	offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, strOptionAmt=local.cf.strOptionAmt)>
										#local.tempSQL#
									</cfcase>
									<cfcase value="DATE">
										<cfset local.tempSQL = addReg_cf_DATE(mode='ticketpackage', fieldID=local.cf.fieldID, customText=dateformat(local.cf.value,"m/d/yyyy"))>
										#local.tempSQL#
									</cfcase>
									<cfcase value="TEXTAREA">
										<cfset local.tempSQL = addReg_cf_TEXTAREA(mode='ticketpackage', fieldID=local.cf.fieldID, customText=local.cf.value)>
										#local.tempSQL#
									</cfcase>
								</cfswitch>
							</cfloop>
							
							<!--- add seats --->
							<cfloop from="1" to="#local.thisTicketSeats#" index="local.thisTicketInstanceSeat">
								
								<!--- put ticket custom fields and field types into array --->
								<cfset local.arrTicketCustomFields = []>
								<cfif arrayLen(local.thisPackageTicketCustomFieldsXML.xmlChildren)>
									<cfloop array="#local.thisPackageTicketCustomFieldsXML.xmlChildren#" index="local.thisfield">
										<cfset local.ticketCustomFieldName = 'ev_tc_#local.thisTicketPackageID#_#local.thisTicketTicketID#_#local.thisfield.xmlattributes.fieldID#_#local.thisTicketInstanceSeat#_#local.thisInstanceFormNum#'>
										
										<cfset local.tmpTicketStr = { fieldID=local.thisfield.xmlattributes.fieldID, 
																	displayTypeCode=local.thisfield.xmlattributes.displayTypeCode, 
																	dataTypeCode=local.thisfield.xmlattributes.dataTypeCode, 
																	offerQty=local.thisfield.xmlattributes.supportQty,
																	offerAmount=local.thisfield.xmlattributes.supportAmt,
																	fieldinventory=local.thisfield.xmlattributes.fieldinventory,  
																	qtyIndivAmt=0,
																	GLAccountID=val(local.thisfield.xmlattributes.GLAccountID),
																	value='',
																	arrQtyAmt=[],
																	strOptionAmt={} }>
										<cfif val(local.tmpTicketStr.GLAccountID) eq 0>
											<cfset local.tmpTicketStr.GLAccountID = local.thisTicketGLAccountID>
										</cfif>
										
										<cfif arguments.event.valueExists('#local.ticketCustomFieldName#')>
											<cfset local.tmpTicketStr.value = arguments.event.getValue('#local.ticketCustomFieldName#')>
										</cfif>

										<cfif local.tmpTicketStr.displayTypeCode eq 'TEXTBOX' and local.tmpTicketStr.offerAmount eq 1>
											<cfset local.tmpTicketStr.value = val(ReReplace(local.tmpTicketStr.value,'[^0-9\.]','','ALL'))>

											<cfif local.tmpTicketStr.offerQty eq 1>
												<cfset local.tmpTicketStr.qtyIndivAmt = abs(local.thisfield.xmlattributes.amount)>
												<cfif local.tmpTicketStr.value gt 0>
													<cfset local.strQtyAmtTax = structNew()>
													<cfset local.strTaxArgs = { saleGLAccountID=local.tmpTicketStr.GLAccountID, saleAmount=local.tmpTicketStr.qtyIndivAmt, 
																				transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
																				stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax }>
													
													<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
													<cfset local.strQtyAmtTax[local.tmpTicketStr.qtyIndivAmt] = val(local.strTaxRate.totalTaxAmt)>

													<cfloop from="1" to="#val(local.tmpTicketStr.value)#" index="local.thisQtyNum">
														<cfset local.qtyStr = { amount=local.tmpTicketStr.qtyIndivAmt, taxAmount=local.strQtyAmtTax[local.tmpTicketStr.qtyIndivAmt] }>
														<cfset local.thisFieldOverrideName = 'override_#local.ticketCustomFieldName#_qty#local.thisQtyNum#_'>
														<cfif arguments.event.valueExists('#local.thisFieldOverrideName#') and local.qtyStr.amount neq ReReplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
															<cfset local.thisQtyActualAmount = ReReplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
															<cfif not structKeyExists(local.strQtyAmtTax,local.thisQtyActualAmount)>
																<cfset local.strTaxArgs.saleAmount = local.thisQtyActualAmount>
																<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
																<cfset local.strQtyAmtTax[local.thisQtyActualAmount] = val(local.strTaxRate.totalTaxAmt)>
															</cfif>
															<cfset local.qtyStr.amount = local.thisQtyActualAmount>
															<cfset local.qtyStr.taxAmount = local.strQtyAmtTax[local.thisQtyActualAmount]>
														</cfif>
														<cfset arrayAppend(local.tmpTicketStr.arrQtyAmt,local.qtyStr)>
													</cfloop>
												</cfif>
											<cfelseif local.tmpTicketStr.offerQty eq 0 and arguments.event.valueExists('override_#local.ticketCustomFieldName#')>
												<cfset local.tmpTicketStr.value = ReReplace(arguments.event.getTrimValue('override_#local.ticketCustomFieldName#'),"[^0-9.]","","ALL")>
											</cfif>
										<cfelseif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpTicketStr.displayTypeCode) and local.tmpTicketStr.offerAmount eq 1 and len(local.tmpTicketStr.value)>
											<cfset local.strOptionAmtTax = structNew()>
											<cfset local.strTaxArgs = { saleGLAccountID=local.tmpTicketStr.GLAccountID, saleAmount=0, 
																		transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
																		stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax }>

											<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
												<cfif listFind(local.tmpTicketStr.value,local.thisoption.xmlAttributes.valueID)>
													<cfset local.thisFieldOverrideName = 'override_#local.ticketCustomFieldName#_#local.thisoption.xmlAttributes.valueID#'>
													<cfset local.thisOptionAmount = val(local.thisoption.xmlAttributes.amount)>
													<cfif arguments.event.valueExists('#local.thisFieldOverrideName#')>
														<cfset local.thisOptionAmount = val(ReReplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL"))>
													</cfif>
													<cfif not structKeyExists(local.strOptionAmtTax,local.thisOptionAmount)>
														<cfset local.strTaxArgs.saleAmount = local.thisOptionAmount>
														<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
														<cfset local.strQtyAmtTax[local.thisOptionAmount] = val(local.strTaxRate.totalTaxAmt)>
													</cfif>
													<cfset local.tmpTicketStr.strOptionAmt[local.thisoption.xmlAttributes.valueID] = { amount=local.thisOptionAmount, taxAmount=local.strQtyAmtTax[local.thisOptionAmount] }>
												</cfif>
											</cfloop>
										</cfif>
										<cfset arrayAppend(local.arrTicketCustomFields,local.tmpTicketStr)>
									</cfloop>
								</cfif>
								
								<cfset local.thisTicketPackageInstanceTicketSeatMID = arguments.event.getValue('ev_tgmid_#local.thisTicketPackageID#_#local.thisInstanceFormNum#_#local.thisTicketInstanceSeat#',0)>

								<!--- insert instance seats --->
								<cfset local.tempSQL = createTicketPackageInstanceSeat(memberID=val(local.thisTicketPackageInstanceTicketSeatMID))>
								#local.tempSQL#

								<!--- loop over the ticket custom fields --->
								<cfloop array="#local.arrTicketCustomFields#" index="local.cf">
									<cfswitch expression="#local.cf.displayTypeCode#">
										<cfcase value="TEXTBOX">
											<cfset local.tempSQL = addReg_cf_TEXTBOX(mode='ticket', fieldID=local.cf.fieldID, customText=local.cf.value,
																					offerQty=local.cf.offerQty,
																					offerAmount=local.cf.offerAmount,
																					fieldinventory=val(local.cf.fieldinventory),
																					objAccounting=arguments.objAccounting,
																					GLAccountID=val(local.cf.GLAccountID),
																					taxInfoStruct=arguments.taxInfoStruct,
																					arrQtyAmt=local.cf.arrQtyAmt)>
											#local.tempSQL#
										</cfcase>
										<cfcase value="SELECT,RADIO,CHECKBOX">
											<cfset local.tempSQL = addReg_cf_SELECTRADIOCHECKBOX(mode='ticket', fieldID=local.cf.fieldID, valueIDList=local.cf.value, GLAccountID=val(local.cf.GLAccountID), 
																					offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, strOptionAmt=local.cf.strOptionAmt)>
											#local.tempSQL#
										</cfcase>
										<cfcase value="DATE">
											<cfset local.tempSQL = addReg_cf_DATE(mode='ticket', fieldID=local.cf.fieldID, customText=dateformat(local.cf.value,"m/d/yyyy"))>
											#local.tempSQL#
										</cfcase>
										<cfcase value="TEXTAREA">
											<cfset local.tempSQL = addReg_cf_TEXTAREA(mode='ticket', fieldID=local.cf.fieldID, customText=local.cf.value)>
											#local.tempSQL#
										</cfcase>
									</cfswitch>
								</cfloop>

							</cfloop><!--- end loop of seats --->
						</cfloop><!--- end loop of instances --->
					</cfif>
				</cfloop>
				</cfoutput>
			</cfsavecontent>
			
		<cfelse>
			<cfset local.addRegistrantSQL = "">
		</cfif>

		<cfreturn local.addRegistrantSQL>
	</cffunction>
	
	<cffunction name="saveRegistrant_update_tickets" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		<cfargument name="eventAdminSiteResourceID" type="numeric" required="true">
		<cfargument name="objAccounting" type="any" required="true">
		<cfargument name="objResourceCustomFields" type="any" required="true">
		<cfargument name="taxInfoStruct" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event")>
		<cfset local.qryTicketDetails = getTicketForReg(registrationid=arguments.registrationID, rateID=arguments.rateID)>		
		<cfset local.qryTicketPackagesSelected = getTicketPackagesSelected(registrantID=arguments.registrantID)>
		<cfset local.qryGetRateAndEventGLAccountID = getRateandEventGLAccountID(eventID=arguments.eventID, rateID=arguments.rateID)>
		
		<cfset local.formRegPackages = structNew()>
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
			<cfif (left(local.thisField,14) eq "ticketPackage_" and arguments.event.getTrimValue(local.thisField) gt 0) or (left(local.thisField,20) eq "ticketPackageCredit_" and left(arguments.event.getTrimValue(local.thisField),10) eq 'createNew_')>
				<cfif not structKeyExists(local.formRegPackages,GetToken(local.thisField,2,'_'))>
					<cfset structInsert(local.formRegPackages, GetToken(local.thisField,2,'_'), 0, true)>
				</cfif>
				<cfif left(local.thisField,14) eq "ticketPackage_" and arguments.event.getTrimValue(local.thisField) gt 0>
					<cfset local.thisRegSelection = arguments.event.getTrimValue(local.thisField)>
				<cfelse>
					<cfset local.thisRegSelection = 1>
				</cfif>
				<cfset local.formRegPackages[GetToken(local.thisField,2,'_')] = local.formRegPackages[GetToken(local.thisField,2,'_')] + local.thisRegSelection>
			</cfif>
		</cfloop>

		<cfset local.arrPackagesSelected = arrayNew(1)>
		<cfquery name="local.qryTicketDetailsDistinct" dbtype="query">
			select distinct ticketID, ticketPackageID, ticketAssignSeats, ticketsInPackage, ticketName, ticketPackageName, qtyIncludedByRate, GLAccountID
			from [local].qryTicketDetails
		</cfquery>
		
		<cfloop query="local.qryTicketDetailsDistinct">
			<cfquery name="local.qryTicketPackageAvailablePriceID" dbtype="query">
				select availablePriceID
				from [local].qryTicketDetails
				where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
			</cfquery>

			<cfset local.thisPackageCustomFieldsXML = arguments.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', areaName='TicketPackage', csrid=arguments.eventAdminSiteResourceID, detailID=local.qryTicketDetailsDistinct.ticketPackageID, hideAdminOnly=0)>
			<cfset local.thisPackageTicketCustomFieldsXML = arguments.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Event', areaName='Ticket', csrid=arguments.eventAdminSiteResourceID, detailID=local.qryTicketDetailsDistinct.ticketID, hideAdminOnly=0)>

			<cfset local.strPackageCustomFieldsXML[local.qryTicketDetailsDistinct.ticketPackageID] = xmlParse(local.thisPackageCustomFieldsXML.returnXML).xmlRoot>
			<cfset local.strPackageTicketCustomFieldsXML[local.qryTicketDetailsDistinct.ticketID] = xmlParse(local.thisPackageTicketCustomFieldsXML.returnXML).xmlRoot>
		
			<cfset local.tmpStr = { ticketPackageID=local.qryTicketDetailsDistinct.ticketPackageID, 
									ticketID=local.qryTicketDetailsDistinct.ticketID, 
									ticketAssignSeats=local.qryTicketDetailsDistinct.ticketAssignSeats,
									hasPackageFields="#arrayLen(local.strPackageCustomFieldsXML[local.qryTicketDetailsDistinct.ticketPackageID].xmlChildren) gt 0#",
									hasTicketFields="#arrayLen(local.strPackageTicketCustomFieldsXML[local.qryTicketDetailsDistinct.ticketID].xmlChildren) gt 0#",
									packageName="#local.qryTicketDetailsDistinct.ticketName# - #local.qryTicketDetailsDistinct.ticketPackageName#",
									ticketsInPackage=local.qryTicketDetailsDistinct.ticketsInPackage,
									ticketPackageAvailablePriceIDList=valueList(local.qryTicketPackageAvailablePriceID.availablePriceID),
									instances=0,
									instanceArray=arrayNew(1),
									qtyIncludedByRate=val(local.qryTicketDetailsDistinct.qtyIncludedByRate) }>
			
			<cfif local.qryTicketDetailsDistinct.GLAccountID gt 0>
				<cfset local.tmpStr.ticketGLAccountID = local.qryTicketDetailsDistinct.GLAccountID>
			<cfelse>
				<cfset local.tmpStr.ticketGLAccountID = local.qryGetRateAndEventGLAccountID.GLAccountID>
			</cfif>
			
			<cfquery name="local.qryThisRegPackageInstance" dbtype="query">
				select instanceID, ticketPackageID
				from [local].qryTicketPackagesSelected
				where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
				and includedFromRate = 0
				order by instanceID
			</cfquery>
			<cfset local.tmpStr.instances = local.qryThisRegPackageInstance.recordCount>
			<cfloop query="local.qryThisRegPackageInstance">
				<cfif left(arguments.event.getValue('ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#',''),10) eq 'createNew_' or val(arguments.event.getValue('ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#','')) gt 0>
					<cfset local.tmpStr.instances = local.tmpStr.instances - 1>
				</cfif>
			</cfloop>
			
			<cfquery name="local.qryThisRegPackageInstanceRateIncluded" dbtype="query">
				select instanceID
				from [local].qryTicketPackagesSelected
				where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
				and includedFromRate = 1
				order by instanceID
			</cfquery>
			<cfif local.qryThisRegPackageInstanceRateIncluded.recordCount gt local.tmpStr.qtyIncludedByRate>
				<cfset local.tmpStr.instances = local.tmpStr.instances + (local.qryThisRegPackageInstanceRateIncluded.recordCount - local.tmpStr.qtyIncludedByRate)>
			</cfif>
			
			<cfif structKeyExists(local.formRegPackages,local.qryTicketDetailsDistinct.ticketPackageID)>
				<cfset local.tmpStr.instances = local.tmpStr.instances + local.formRegPackages[local.qryTicketDetailsDistinct.ticketPackageID] + local.tmpStr.qtyIncludedByRate>
			<cfelseif local.tmpStr.qtyIncludedByRate gt 0>
				<cfset local.tmpStr.instances = local.tmpStr.instances + local.tmpStr.qtyIncludedByRate>
			</cfif>
			<cfif local.tmpStr.instances gt 0>
				<cfset local.tmpStr.thisPackageInstanceNumList = "">
				<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
					<cfif left(local.thisField,16) eq "packageInstance_" and ListLen(local.thisField,"_") eq 4 and GetToken(local.thisField,2,'_') eq local.qryTicketDetailsDistinct.ticketPackageID and val(GetToken(local.thisField,4,'_')) gt 0>
						<cfset local.tmpStr.thisPackageInstanceNumList = ListAppend(local.tmpStr.thisPackageInstanceNumList,GetToken(local.thisField,4,'_'))>
					</cfif>
				</cfloop>
				<cfset local.tmpStr.thisPackageInstanceNumList = listSort(local.tmpStr.thisPackageInstanceNumList,'numeric','asc')>

				<cfquery name="local.qryThisPackageInstance" dbtype="query">
					select *
					from [local].qryTicketPackagesSelected
					where ticketPackageID = #local.qryTicketDetailsDistinct.ticketPackageID#
					order by instanceID
				</cfquery>
				<cfset local.tmpStr.includedFromRateCount = 0>
				<cfloop from="1" to="#local.tmpStr.instances#" index="local.thisInstanceNum">
					<cfset local.tmpInstanceStruct = { instanceID=0, includedFromRate=0, prevIncludedFromRate=0, availablePriceID=0,
														qryInstanceSeats=QueryNew("seatID,memberID","integer,integer") }>
					
					<cfset local.instanceIDList = listSort(valueList(local.qryThisPackageInstance.instanceID),'numeric','asc')>

					<cfif ListLen(local.instanceIDList) gte local.thisInstanceNum>
						<cfset local.tmpInstanceStruct.instanceID = GetToken(local.instanceIDList,local.thisInstanceNum,",")>

						<cfquery name="local.qryThisInstanceDetails" dbtype="query">
							select availablePriceID, includedFromRate
							from [local].qryTicketPackagesSelected
							where instanceID = #val(local.tmpInstanceStruct.instanceID)#
						</cfquery>

						<cfset local.tmpInstanceStruct.includedFromRate = local.qryThisInstanceDetails.includedFromRate>
						<cfset local.tmpInstanceStruct.availablePriceID = local.qryThisInstanceDetails.availablePriceID>
						<cfset local.tmpInstanceStruct.prevIncludedFromRate = local.qryThisInstanceDetails.includedFromRate>
						<cfset local.tmpStr.includedFromRateCount = local.tmpStr.includedFromRateCount + local.tmpInstanceStruct.includedFromRate>
						<cfquery name="local.tmpInstanceStruct.qryInstanceSeats" datasource="#application.dsn.membercentral.dsn#">
							select seatID, memberID
							from dbo.ev_registrantPackageInstanceSeats
							where instanceID = <cfqueryparam value="#local.tmpInstanceStruct.instanceID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
					</cfif>
					
					<cfif local.tmpInstanceStruct.includedFromRate eq 1 and local.tmpStr.includedFromRateCount gt local.tmpStr.qtyIncludedByRate>
						<cfset local.tmpInstanceStruct.includedFromRate = 0>
						<cfset local.tmpStr.includedFromRateCount = local.tmpStr.includedFromRateCount - 1>
						<cfif val(arguments.event.getValue('ticketPackageDebit_#local.qryTicketDetailsDistinct.ticketPackageID#_#local.tmpInstanceStruct.instanceID#',0)) gt 0>
							<cfset local.tmpInstanceStruct.availablePriceID = arguments.event.getValue('ticketPackageDebit_#local.qryTicketDetailsDistinct.ticketPackageID#_#local.tmpInstanceStruct.instanceID#')>
						</cfif>
					<cfelseif local.tmpInstanceStruct.includedFromRate eq 0 and local.tmpStr.includedFromRateCount lt local.tmpStr.qtyIncludedByRate>
						<cfif local.tmpInstanceStruct.instanceID gt 0 and val(arguments.event.getTrimValue('ticketPackageCredit_#local.qryTicketDetailsDistinct.ticketPackageID#_#local.thisInstanceNum#','')) gt 0>
							<cfset local.tmpInstanceStruct.includedFromRate = 1>
							<cfset local.tmpStr.includedFromRateCount = local.tmpStr.includedFromRateCount + 1>
						<cfelseif local.tmpInstanceStruct.instanceID eq 0>
							<cfset local.tmpInstanceStruct.includedFromRate = 1>
							<cfset local.tmpStr.includedFromRateCount = local.tmpStr.includedFromRateCount + 1>
						</cfif>
					</cfif>
					
					<cfif local.tmpInstanceStruct.includedFromRate eq 0 and local.tmpInstanceStruct.availablePriceID eq 0>
						<cfif ListLen(local.tmpStr.thisPackageInstanceNumList) gte local.thisInstanceNum>
							<cfset local.thisFormInstanceNum = GetToken(local.tmpStr.thisPackageInstanceNumList,local.thisInstanceNum,",")>
							<cfset local.tmpInstanceStruct.availablePriceID = arguments.event.getValue('tpiAvailablePriceID_#local.qryTicketDetailsDistinct.ticketPackageID#_#local.thisFormInstanceNum#',0)>
						</cfif>
					</cfif>
					<cfset arrayAppend(local.tmpStr.instanceArray,local.tmpInstanceStruct)>
				</cfloop>
				<cfset arrayAppend(local.arrPackagesSelected, local.tmpStr)>
			</cfif>
		</cfloop>
		
		<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
			<cfprocresult name="local.qryTotals" resultset="1">
			<cfprocresult name="local.qryRegTransactions" resultset="2">
			<cfprocresult name="local.qryPaymentAllocations" resultset="3">
		</cfstoredproc>
		
		<cfsavecontent variable="local.updateRegistrantSQL">
			<cfoutput>
				<cfif arrayLen(local.arrPackagesSelected)>
					<cfloop array="#local.arrPackagesSelected#" index="local.thisTicketPackage">
						<cfset local.thisPackageInstanceNum = 0>
						<cfloop list="#local.thisTicketPackage.thisPackageInstanceNumList#" index="local.thisFormInstanceNum">
							<cfset local.thisPackageInstanceNum = local.thisPackageInstanceNum + 1>
							<cfset local.thisTicketPackageInstance = local.thisTicketPackage.instanceArray[local.thisPackageInstanceNum]>
							<cfset local.thisTicketPackageID = local.thisTicketPackage.ticketPackageID>
							<cfset local.thisTicketPackageInstanceID = val(local.thisTicketPackageInstance.instanceID)>
							<cfset local.thisTicketPackageSaleAmount = 0>
							
							set @instanceID = null;
							<cfif local.thisTicketPackageInstanceID gt 0>
								set @instanceID = #local.thisTicketPackageInstanceID#;
								
								<cfquery name="local.qryThisRegPackageInstanceDetails" datasource="#application.dsn.membercentral.dsn#">
									select dataID, fieldID
									from dbo.cf_fieldData
									where itemID = <cfqueryparam value="#local.thisTicketPackageInstanceID#" cfsqltype="CF_SQL_INTEGER">
									and itemType = 'ticketPackInstCustom'
								</cfquery>
								<cfquery name="local.qryThisTicketPackageInstanceSale" dbtype="query">
									select sum(amount) as amount
									from [local].qryRegTransactions
									where itemID = #local.thisTicketPackageInstanceID#
									and itemType = 'TicketPackInst'
								</cfquery>
								<cfset local.thisTicketPackageSaleAmount = val(local.qryThisTicketPackageInstanceSale.amount)>
							</cfif>
							
							<cfquery name="local.qryThisPackageAmount" dbtype="query">
								select ticketPackageAmount, availablePriceID
								from [local].qryTicketDetails
								where ticketPackageID = #local.thisTicketPackageID#
								<cfif val(local.thisTicketPackageInstance.availablePriceID) gt 0>
									and availablePriceID = #val(local.thisTicketPackageInstance.availablePriceID)#
								</cfif>
							</cfquery>
							<cfif arguments.event.valueExists('MCEVTP_#local.thisTicketPackageID#_#local.thisFormInstanceNum#')>
								<cfset local.thisTicketPackageAmount = val(rereplace(arguments.event.getValue('MCEVTP_#local.thisTicketPackageID#_#local.thisFormInstanceNum#'),"[^0-9.]","","ALL"))>
							<cfelse>
								<cfset local.thisTicketPackageAmount = val(local.qryThisPackageAmount.ticketPackageAmount)>
							</cfif>
							<cfset local.thisTicketPackageAvailablePriceID = val(local.qryThisPackageAmount.availablePriceID)>
							<cfif val(local.thisTicketPackageAmount) gt 0>
								<cfset local.strTaxCF = arguments.objAccounting.getTaxForUncommittedSale(saleGLAccountID=val(local.thisTicketPackage.ticketGLAccountID), 
															saleAmount=val(local.thisTicketPackageAmount), transactionDate=arguments.taxInfoStruct.transactionDate, 
															stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax)>
								<cfset local.thisTicketPackageTaxAmount = local.strTaxCF.totalTaxAmt>
							<cfelse>
								<cfset local.thisTicketPackageTaxAmount = 0>
							</cfif>
							
							<cfset local.thisThisTicketPackageStruct = { ticketPackageID=local.thisTicketPackageID,
																		 ticketID=local.thisTicketPackage.ticketID,
																		 availablePriceID=local.thisTicketPackageAvailablePriceID, 
																		 ticketPackageAmount=local.thisTicketPackageAmount,
																		 instanceID=local.thisTicketPackageInstanceID,
																		 saleAmount=local.thisTicketPackageSaleAmount,
																		 GLAccountID=val(local.thisTicketPackage.ticketGLAccountID),
																		 includedFromRate=local.thisTicketPackageInstance.includedFromRate,
																		 ticketPackageTaxAmount=local.thisTicketPackageTaxAmount, 
																		 ticketPackageSaleDetail=local.thisTicketPackage.packageName,
																		 ticketsInPackage=local.thisTicketPackage.ticketsInPackage }>
							
							<!--- put package custom fields and field types into array --->
							<cfset local.arrCustomFields = []>
							<cfif arrayLen(local.strPackageCustomFieldsXML[local.thisTicketPackageID].xmlChildren)>
								<cfloop array="#local.strPackageCustomFieldsXML[local.thisTicketPackageID].xmlChildren#" index="local.thisfield">
									<cfset local.tmpAtt = local.thisfield.xmlattributes>
									<cfset local.thisFieldName = "ev_tpc_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.tmpAtt.fieldID#">
									<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID, 
															displayTypeCode=local.tmpAtt.displayTypeCode, 
															offerQty=local.tmpAtt.supportQty,
															offerAmount=local.tmpAtt.supportAmt,
															fieldinventory=local.tmpAtt.fieldinventory, 
															qtyIndivAmt=0,
															saleAmount=0,
															GLAccountID=val(local.tmpAtt.GLAccountID),
															dataID=0,
															qtyRemoveValue='',
															value='',
															arrQtyAmt=[],
															strOptionAmt={} }>
									<cfif val(local.tmpStr.GLAccountID) eq 0>
										<cfset local.tmpStr.GLAccountID = val(local.thisTicketPackage.ticketGLAccountID)>
									</cfif>
									<cfif arguments.event.valueExists('#local.thisFieldName#')>
										<cfset local.tmpStr.value = arguments.event.getValue('#local.thisFieldName#')>
									</cfif>
									<cfif local.tmpStr.displayTypeCode eq 'TEXTBOX' and local.tmpStr.offerQty eq 1>
										<cfset local.tmpStr.qtyIndivAmt = val(local.tmpAtt.amount)>
									</cfif>

									<cfif local.tmpStr.displayTypeCode eq 'TEXTBOX' and local.tmpStr.offerAmount eq 1>
										<cfset local.tmpStr.value = val(ReReplace(local.tmpStr.value,'[^0-9\.]','','ALL'))>

										<cfif local.tmpStr.offerQty eq 1>
											<cfset local.tmpStr.qtyIndivAmt = abs(local.tmpAtt.amount)>
											<cfset local.tmpStr.qtyRemoveValue = arguments.event.getTrimValue('evtpcqty_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.tmpAtt.fieldID#','')>

											<cfif local.tmpStr.value gt 0>
												<cfset local.strQtyAmtTax = structNew()>
												<cfset local.strTaxArgs = { saleGLAccountID=local.tmpStr.GLAccountID, saleAmount=local.tmpStr.qtyIndivAmt, 
																			transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
																			stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax }>
												
												<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
												<cfset local.strQtyAmtTax[local.tmpStr.qtyIndivAmt] = val(local.strTaxRate.totalTaxAmt)>

												<cfloop from="1" to="#val(local.tmpStr.value)#" index="local.thisQtyNum">
													<cfset local.qtyStr = { amount=local.tmpStr.qtyIndivAmt, taxAmount=local.strQtyAmtTax[local.tmpStr.qtyIndivAmt] }>
													<cfset local.thisFieldOverrideName = 'override_#local.thisFieldName#_qty#local.thisQtyNum#_'>
													<cfif arguments.event.valueExists('#local.thisFieldOverrideName#') and local.qtyStr.amount neq ReReplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
														<cfset local.thisQtyActualAmount = ReReplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
														<cfif not structKeyExists(local.strQtyAmtTax,local.thisQtyActualAmount)>
															<cfset local.strTaxArgs.saleAmount = local.thisQtyActualAmount>
															<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
															<cfset local.strQtyAmtTax[local.thisQtyActualAmount] = val(local.strTaxRate.totalTaxAmt)>
														</cfif>
														<cfset local.qtyStr.amount = local.thisQtyActualAmount>
														<cfset local.qtyStr.taxAmount = local.strQtyAmtTax[local.thisQtyActualAmount]>
													</cfif>
													<cfset arrayAppend(local.tmpStr.arrQtyAmt,local.qtyStr)>
												</cfloop>
											</cfif>
										<cfelseif local.tmpStr.offerQty eq 0 and arguments.event.valueExists('override_#local.thisFieldName#')>
											<cfset local.tmpStr.value = rereplace(arguments.event.getTrimValue('override_#local.thisFieldName#'),"[^0-9.]","","ALL")>
										</cfif>
									<cfelseif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpStr.displayTypeCode) and local.tmpStr.offerAmount eq 1 and len(local.tmpStr.value)>
										<cfset local.strOptionAmtTax = structNew()>
										<cfset local.strTaxArgs = { saleGLAccountID=local.tmpStr.GLAccountID, saleAmount=0, 
																	transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
																	stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax }>

										<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
											<cfif listFind(local.tmpStr.value,local.thisoption.xmlAttributes.valueID)>
												<cfset local.thisFieldOverrideName = 'override_#local.thisFieldName#_#local.thisoption.xmlAttributes.valueID#'>
												<cfset local.thisOptionAmount = val(local.thisoption.xmlAttributes.amount)>
												<cfif arguments.event.valueExists('#local.thisFieldOverrideName#')>
													<cfset local.thisOptionAmount = val(rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL"))>
												</cfif>
												<cfif not structKeyExists(local.strOptionAmtTax,local.thisOptionAmount)>
													<cfset local.strTaxArgs.saleAmount = local.thisOptionAmount>
													<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
													<cfset local.strQtyAmtTax[local.thisOptionAmount] = val(local.strTaxRate.totalTaxAmt)>
												</cfif>
												<cfset local.tmpStr.strOptionAmt[local.thisoption.xmlAttributes.valueID] = { amount=local.thisOptionAmount, taxAmount=local.strQtyAmtTax[local.thisOptionAmount] }>
											</cfif>
										</cfloop>
									</cfif>

									<cfif local.thisTicketPackageInstanceID gt 0>
										<cfquery name="local.qryThisPackageCustomDetail" dbtype="query">
											select dataID
											from [local].qryThisRegPackageInstanceDetails
											where fieldID = #local.tmpStr.fieldID#
										</cfquery>
										<cfif ListLen(local.qryThisPackageCustomDetail.dataID) gt 0>
											<cfset local.tmpStr.dataID = valueList(local.qryThisPackageCustomDetail.dataID)>
										</cfif>
									</cfif>
									<cfset arrayAppend(local.arrCustomFields,local.tmpStr)>
								</cfloop>
							</cfif>
							
							<cfif local.thisTicketPackageInstanceID gt 0>
								<cfquery name="local.qryThisRegPackageInstanceTicketSeats" datasource="#application.dsn.membercentral.dsn#">
									select seatID, memberID
									from dbo.ev_registrantPackageInstanceSeats
									where instanceID = #local.thisTicketPackageInstanceID#
									and status = 'A'
								</cfquery>
								<cfset local.thisPackageInstanceSeats = valueList(local.qryThisRegPackageInstanceTicketSeats.seatID)>
								
								<cfquery name="local.qryThisRegPackageInstanceTicketDetails" datasource="#application.dsn.membercentral.dsn#">
									select fd.dataID, rpis.seatID, fd.fieldID, fd.valueID, rpis.memberID
									from dbo.cf_fieldData as fd
									inner join dbo.ev_registrantPackageInstanceSeats as rpis on rpis.seatID = fd.itemID and fd.itemType = 'ticketPackSeatCustom'
									where rpis.instanceID = #val(local.thisTicketPackageInstanceID)#
									and rpis.status = 'A';
								</cfquery>
							</cfif>
							
							<cfset local.thisPackageArrTicketCustomFields = []>
							<cfloop from="1" to="#local.thisThisTicketPackageStruct.ticketsInPackage#" index="local.thisTicketInstanceSeat">
								
								<!--- put ticket custom fields and field types into array --->
								<cfset local.arrTicketCustomFields = []>
								<cfif arrayLen(local.strPackageTicketCustomFieldsXML[local.thisThisTicketPackageStruct.ticketID].xmlChildren)>
									<cfloop array="#local.strPackageTicketCustomFieldsXML[local.thisThisTicketPackageStruct.ticketID].xmlChildren#" index="local.thisfield">
										<cfset local.ticketCustomFieldName = 'ev_tc_#local.thisTicketPackageID#_#local.thisThisTicketPackageStruct.ticketID#_#local.thisfield.xmlattributes.fieldID#_#local.thisTicketInstanceSeat#_#local.thisFormInstanceNum#'>
										
										<cfset local.tmpTicketStr = { fieldID=local.thisfield.xmlattributes.fieldID, 
																	displayTypeCode=local.thisfield.xmlattributes.displayTypeCode, 
																	offerQty=local.thisfield.xmlattributes.supportQty,
																	offerAmount=local.thisfield.xmlattributes.supportAmt,
																	fieldinventory=local.thisfield.xmlattributes.fieldinventory, 
																	qtyIndivAmt=0,
																	saleAmount=0,
																	GLAccountID=val(local.thisfield.xmlattributes.GLAccountID),
																	dataID=0,
																	qtyRemoveValue='',
																	seatID=0,
																	value='',
																	arrQtyAmt=[],
																	strOptionAmt={} }>
										<cfif val(local.tmpTicketStr.GLAccountID) eq 0>
											<cfset local.tmpTicketStr.GLAccountID = val(local.thisTicketPackage.ticketGLAccountID)>
										</cfif>
										<cfif arguments.event.valueExists('#local.ticketCustomFieldName#')>
											<cfset local.tmpTicketStr.value = arguments.event.getValue('#local.ticketCustomFieldName#')>
										</cfif>

										<cfif local.tmpTicketStr.displayTypeCode eq 'TEXTBOX' and local.tmpTicketStr.offerAmount eq 1>
											<cfset local.tmpTicketStr.value = val(ReReplace(local.tmpTicketStr.value,'[^0-9\.]','','ALL'))>

											<cfif local.tmpTicketStr.offerQty eq 1>
												<cfset local.tmpTicketStr.qtyIndivAmt = abs(local.thisfield.xmlattributes.amount)>
												<cfset local.tmpTicketStr.qtyRemoveValue=arguments.event.getValue('evtcqty_#local.thisTicketPackageID#_#local.thisThisTicketPackageStruct.ticketID#_#local.thisFormInstanceNum#_#local.thisTicketInstanceSeat#_#local.thisfield.xmlattributes.fieldID#','')>

												<cfif local.tmpTicketStr.value gt 0>
													<cfset local.strQtyAmtTax = structNew()>
													<cfset local.strTaxArgs = { saleGLAccountID=local.tmpTicketStr.GLAccountID, saleAmount=local.tmpTicketStr.qtyIndivAmt, 
																				transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
																				stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax }>
													
													<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
													<cfset local.strQtyAmtTax[local.tmpTicketStr.qtyIndivAmt] = val(local.strTaxRate.totalTaxAmt)>

													<cfloop from="1" to="#val(local.tmpTicketStr.value)#" index="local.thisQtyNum">
														<cfset local.qtyStr = { amount=local.tmpTicketStr.qtyIndivAmt, taxAmount=local.strQtyAmtTax[local.tmpTicketStr.qtyIndivAmt] }>
														<cfset local.thisFieldOverrideName = 'override_#local.ticketCustomFieldName#_qty#local.thisQtyNum#_'>
														<cfif arguments.event.valueExists('#local.thisFieldOverrideName#') and local.qtyStr.amount neq rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
															<cfset local.thisQtyActualAmount = rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL")>
															<cfif not structKeyExists(local.strQtyAmtTax,local.thisQtyActualAmount)>
																<cfset local.strTaxArgs.saleAmount = local.thisQtyActualAmount>
																<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
																<cfset local.strQtyAmtTax[local.thisQtyActualAmount] = val(local.strTaxRate.totalTaxAmt)>
															</cfif>
															<cfset local.qtyStr.amount = local.thisQtyActualAmount>
															<cfset local.qtyStr.taxAmount = local.strQtyAmtTax[local.thisQtyActualAmount]>
														</cfif>
														<cfset arrayAppend(local.tmpTicketStr.arrQtyAmt,local.qtyStr)>
													</cfloop>
												</cfif>
											<cfelseif local.tmpTicketStr.offerQty eq 0 and arguments.event.valueExists('override_#local.ticketCustomFieldName#')>
												<cfset local.tmpTicketStr.value = rereplace(arguments.event.getTrimValue('override_#local.ticketCustomFieldName#'),"[^0-9.]","","ALL")>
											</cfif>
										<cfelseif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpTicketStr.displayTypeCode) and local.tmpTicketStr.offerAmount eq 1 and len(local.tmpTicketStr.value)>
											<cfset local.strOptionAmtTax = structNew()>
											<cfset local.strTaxArgs = { saleGLAccountID=local.tmpTicketStr.GLAccountID, saleAmount=0, 
																		transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
																		stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax }>

											<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
												<cfif listFind(local.tmpTicketStr.value,local.thisoption.xmlAttributes.valueID)>
													<cfset local.thisFieldOverrideName = 'override_#local.ticketCustomFieldName#_#local.thisoption.xmlAttributes.valueID#'>
													<cfset local.thisOptionAmount = val(local.thisoption.xmlAttributes.amount)>
													<cfif arguments.event.valueExists('#local.thisFieldOverrideName#')>
														<cfset local.thisOptionAmount = val(rereplace(arguments.event.getValue('#local.thisFieldOverrideName#'),"[^0-9.]","","ALL"))>
													</cfif>
													<cfif not structKeyExists(local.strOptionAmtTax,local.thisOptionAmount)>
														<cfset local.strTaxArgs.saleAmount = local.thisOptionAmount>
														<cfset local.strTaxRate = arguments.objAccounting.getTaxForUncommittedSale(argumentCollection=local.strTaxArgs)>
														<cfset local.strQtyAmtTax[local.thisOptionAmount] = val(local.strTaxRate.totalTaxAmt)>
													</cfif>
													<cfset local.tmpTicketStr.strOptionAmt[local.thisoption.xmlAttributes.valueID] = { amount=local.thisOptionAmount, taxAmount=local.strQtyAmtTax[local.thisOptionAmount] }>
												</cfif>
											</cfloop>
										</cfif>
										
										<cfif local.thisTicketPackageInstanceID gt 0>
											<cfset local.tmpTicketStr.seatID = GetToken(local.thisPackageInstanceSeats,local.thisTicketInstanceSeat,",")>
											<cfquery name="local.qryThisPackageTicketCustomDetail" dbtype="query">
												select dataID
												from [local].qryThisRegPackageInstanceTicketDetails
												where fieldID = #local.tmpTicketStr.fieldID#
												and seatID = #val(local.tmpTicketStr.seatID)#
											</cfquery>
											<cfif ListLen(local.qryThisPackageTicketCustomDetail.dataID) gt 0>
												<cfset local.tmpTicketStr.dataID = valueList(local.qryThisPackageTicketCustomDetail.dataID)>
											</cfif>
										</cfif>
										
										<cfset arrayAppend(local.arrTicketCustomFields,local.tmpTicketStr)>
									</cfloop>
								</cfif>
								
								<cfset arrayAppend(local.thisPackageArrTicketCustomFields,local.arrTicketCustomFields)>
							</cfloop><!--- end loop of seats --->
							
							<cfif arguments.event.getValue('removeTicketPackageInstance_#local.thisTicketPackageInstanceID#',0) eq 1>
								<cfset local.tempSQL = removeTicketPackageSQL(event=arguments.event, ticketPackageStruct=local.thisThisTicketPackageStruct, arrCustomFields=local.arrCustomFields, 
															arrTicketCustomFields=local.thisPackageArrTicketCustomFields)>
								#local.tempSQL#
							<cfelse>
								
								<cfset local.thisPackageSaveProcess = ''>
								<cfif local.thisTicketPackageInstanceID gt 0>
									<cfset local.thisPackageSaveProcess = 'update'>
									set @instanceID = null;
									set @instanceID = #local.thisTicketPackageInstanceID#;

									<cfif local.thisTicketPackageInstance.includedFromRate eq 1 and local.thisTicketPackageInstance.prevIncludedFromRate eq 0>
										<cfset local.tempSQL = removePackageInstanceSQL(instanceID=local.thisTicketPackageInstanceID)>
										#local.tempSQL#
										
										update dbo.ev_registrantPackageInstances
										set includedFromRate = 1
										where instanceID = @instanceID
										and status = 'A';
									
									<cfelseif local.thisTicketPackageInstance.prevIncludedFromRate eq 1 and local.thisTicketPackageInstance.includedFromRate eq 0 and val(arguments.event.getValue('ticketPackageDebit_#local.thisTicketPackageID#_#local.thisTicketPackageInstanceID#',0)) neq 0>
										update dbo.ev_registrantPackageInstances
										set includedFromRate = 0,
											availablePriceID = #val(arguments.event.getValue('ticketPackageDebit_#local.thisTicketPackageID#_#local.thisTicketPackageInstanceID#'))#
										where instanceID = @instanceID
										and status = 'A';
										
										<cfset local.tempSQL = createPackageInstanceSaleDetail(packageAmount=local.thisThisTicketPackageStruct.ticketPackageAmount, 
																	GLAccountID=local.thisThisTicketPackageStruct.GLAccountID, taxAmount=local.thisThisTicketPackageStruct.ticketPackageTaxAmount, 
																	saleDetail=local.thisThisTicketPackageStruct.ticketPackageSaleDetail)>
					
										#local.tempSQL#

									<cfelseif local.thisTicketPackageInstance.includedFromRate eq 0 and local.thisThisTicketPackageStruct.ticketPackageAmount neq local.thisThisTicketPackageStruct.saleAmount>
										<cfset local.tempSQL = removePackageInstanceSQL(instanceID=local.thisTicketPackageInstanceID)>
										#local.tempSQL#

										<cfset local.tempSQL = createPackageInstanceSaleDetail(packageAmount=local.thisThisTicketPackageStruct.ticketPackageAmount, 
																	GLAccountID=local.thisThisTicketPackageStruct.GLAccountID, taxAmount=local.thisThisTicketPackageStruct.ticketPackageTaxAmount, 
																	saleDetail=local.thisThisTicketPackageStruct.ticketPackageSaleDetail)>
					
										#local.tempSQL#

									</cfif>
								
								<cfelse>
									<cfset local.tempSQL = createTicketPackageInstance(ticketPackageStruct=local.thisThisTicketPackageStruct)>
									#local.tempSQL#
									
									<cfset local.thisPackageSaveProcess = 'insert'>
								</cfif>
								
								<!--- loop over the package custom fields --->
								<cfloop array="#local.arrCustomFields#" index="local.cf">
									<cfswitch expression="#local.cf.displayTypeCode#">
										<cfcase value="TEXTBOX">
											<cfset local.tempSQL = editReg_cf_TEXTBOX(mode='ticketpackage', registrantID=arguments.registrantID, fieldID=local.cf.fieldID, customText=local.cf.value,
																offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, fieldinventory=val(local.cf.fieldinventory), 
																objAccounting=arguments.objAccounting, GLAccountID=val(local.cf.GLAccountID), taxInfoStruct=arguments.taxInfoStruct, 
																removeQtyItems=local.cf.qtyRemoveValue, arrQtyAmt=local.cf.arrQtyAmt, detailID=val(local.cf.dataID))>
											
											#local.tempSQL#
										</cfcase>
										<cfcase value="SELECT,RADIO,CHECKBOX">
											<cfset local.tempSQL = editReg_cf_SELECTRADIOCHECKBOX(mode='ticketpackage', registrantID=arguments.registrantID, fieldID=local.cf.fieldID, valueIDList=local.cf.value,
																GLAccountID=val(local.cf.GLAccountID), offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, strOptionAmt=local.cf.strOptionAmt, 
																detailID=val(local.cf.dataID))>
											
											#local.tempSQL#
										</cfcase>
										<cfcase value="DATE">
											<cfset local.tempSQL = editReg_cf_DATE(mode='ticketpackage', registrantID=arguments.registrantID, fieldID=local.cf.fieldID, customText=dateformat(local.cf.value,"m/d/yyyy"), detailID=val(local.cf.dataID))>
											#local.tempSQL#
										</cfcase>
										<cfcase value="TEXTAREA">
											<cfset local.tempSQL = editReg_cf_TEXTAREA(mode='ticketpackage', registrantID=arguments.registrantID, fieldID=local.cf.fieldID, customText=local.cf.value, detailID=val(local.cf.dataID))>
											#local.tempSQL#
										</cfcase>
									</cfswitch>
								</cfloop> <!--- end package custom fields --->
															
								<cfloop from="1" to="#local.thisThisTicketPackageStruct.ticketsInPackage#" index="local.thisTicketInstanceSeat">
									<cfset local.thisTicketPackageInstanceTicketSeatMID = arguments.event.getValue('ev_tgmid_#local.thisTicketPackageID#_#local.thisFormInstanceNum#_#local.thisTicketInstanceSeat#',0)>

									set @seatID = null;
									<cfif local.thisPackageSaveProcess eq 'insert'>
										<cfset local.tempSQL = createTicketPackageInstanceSeat(memberID=val(local.thisTicketPackageInstanceTicketSeatMID))>
										#local.tempSQL#

									<cfelseif arrayLen(local.thisPackageArrTicketCustomFields[local.thisTicketInstanceSeat]) eq 0>
										select @seatID = seatID
										from (select seatID, ROW_NUMBER() over (order by seatID) as seatNum
												from dbo.ev_registrantPackageInstanceSeats
												where instanceID = @instanceID
												and [status] = 'A'
											)tmp
										where tmp.seatNum = #val(local.thisTicketInstanceSeat)#;

										IF @seatID is null BEGIN
											<cfset local.tempSQL = createTicketPackageInstanceSeat(memberID=val(local.thisTicketPackageInstanceTicketSeatMID))>
											#local.tempSQL#
										END
									</cfif>

									<!--- loop over the ticket custom fields --->
									<cfloop array="#local.thisPackageArrTicketCustomFields[local.thisTicketInstanceSeat]#" index="local.cf">
										<cfif val(local.cf.seatID) gt 0>
											set @seatID = #val(local.cf.seatID)#;
										</cfif>

										<!--- ensure seatID is not null --->
										IF @seatID is null BEGIN
											select @seatID = seatID
											from (select seatID, ROW_NUMBER() over (order by seatID) as seatNum
													from dbo.ev_registrantPackageInstanceSeats
													where instanceID = @instanceID
													and [status] = 'A'
												)tmp
											where tmp.seatNum = #val(local.thisTicketInstanceSeat)#;

											IF @seatID is null BEGIN
												<cfset local.tempSQL = createTicketPackageInstanceSeat(memberID=val(local.thisTicketPackageInstanceTicketSeatMID))>
												#local.tempSQL#
											END
										END

										<cfswitch expression="#local.cf.displayTypeCode#">
											<cfcase value="TEXTBOX">
												<cfset local.tempSQL = editReg_cf_TEXTBOX(mode='ticket', registrantID=arguments.registrantID, fieldID=local.cf.fieldID, customText=local.cf.value,
																offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, fieldinventory=val(local.cf.fieldinventory), 
																objAccounting=arguments.objAccounting, GLAccountID=val(local.cf.GLAccountID), taxInfoStruct=arguments.taxInfoStruct, 
																removeQtyItems=local.cf.qtyRemoveValue, arrQtyAmt=local.cf.arrQtyAmt, detailID=val(local.cf.dataID))>
																
												#local.tempSQL#
											</cfcase>
											<cfcase value="SELECT,RADIO,CHECKBOX">
												<cfset local.tempSQL = editReg_cf_SELECTRADIOCHECKBOX(mode='ticket', registrantID=arguments.registrantID, fieldID=local.cf.fieldID, valueIDList=local.cf.value,
																GLAccountID=val(local.cf.GLAccountID), offerQty=local.cf.offerQty, offerAmount=local.cf.offerAmount, strOptionAmt=local.cf.strOptionAmt, 
																detailID=val(local.cf.dataID))>
											
												#local.tempSQL#
											</cfcase>
											<cfcase value="DATE">
												<cfset local.tempSQL = editReg_cf_DATE(mode='ticket', registrantID=arguments.registrantID, fieldID=local.cf.fieldID, customText=dateformat(local.cf.value,"m/d/yyyy"), detailID=val(local.cf.dataID))>
												#local.tempSQL#
											</cfcase>
											<cfcase value="TEXTAREA">
												<cfset local.tempSQL = editReg_cf_TEXTAREA(mode='ticket', registrantID=arguments.registrantID, fieldID=local.cf.fieldID, customText=local.cf.value, detailID=val(local.cf.dataID))>
												#local.tempSQL#
											</cfcase>
										</cfswitch>
									</cfloop>
								</cfloop>
							</cfif>	
						</cfloop>
					</cfloop>
					
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.updateRegistrantSQL>
	</cffunction>
	
	<cffunction name="getRateandEventGLAccountID" access="private" output="no" returntype="query">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryGetRateAndEventGLAccountID = "">

		<cfquery name="qryGetRateAndEventGLAccountID" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @RateGLAccountID int, @EventGLAccountID int, @GLAccountID int;

			select @RateGLAccountID = GLAccountID from dbo.ev_rates where rateID = #arguments.rateID#;
			select @EventGLAccountID = GLAccountID from dbo.ev_events where eventID = #arguments.eventID#;
			set @GLAccountID = coalesce(@RateGLAccountID,@EventGLAccountID);

			select @RateGLAccountID as RateGLAccountID, @EventGLAccountID as EventGLAccountID, @GLAccountID as GLAccountID;
		</cfquery>	

		<cfreturn qryGetRateAndEventGLAccountID>
	</cffunction>
	
	<cffunction name="addReg_cf_TEXTBOX" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		<cfargument name="offerQty" type="boolean" required="true">
		<cfargument name="offerAmount" type="boolean" required="true">
		<cfargument name="fieldinventory" type="numeric" required="true">
		<cfargument name="objAccounting" type="any" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="taxInfoStruct" type="struct" required="true">
		<cfargument name="arrQtyAmt" type="array" required="true">
		
		<cfset var local = structNew()>

		<!--- if we offer adhoc amount, clean value so no commas etc --->
		<cfif arguments.offerAmount or arguments.offerQty>
			<cfset arguments.customText = val(ReReplace(arguments.customText,'[^0-9\.]','','ALL'))>
		</cfif>

		<cfsavecontent variable="local.addRegistrantSQL">
			<cfoutput>
			set @detail = '#replace(arguments.customText,"'","''","ALL")#';
			set @fieldID = #arguments.fieldID#;
			set @dataID = null;

			<cfif arguments.offerQty>
				<cfset local.tempSQL = addReg_cf_TEXTBOX_qty(mode=arguments.mode, qty=arguments.customText, GLAccountID=arguments.GLAccountID, fieldinventory=arguments.fieldinventory, 
														arrQtyAmt=arguments.arrQtyAmt)>
				#local.tempSQL#
			<cfelseif arguments.offerAmount>
				<cfset local.tempSQL = addReg_cf_TEXTBOX_amt(mode=arguments.mode, amount=arguments.customText, objAccounting=arguments.objAccounting, GLAccountID=arguments.GLAccountID, 
														taxInfoStruct=arguments.taxInfoStruct)>
				#local.tempSQL#
			<cfelse>
				<cfswitch expression="#arguments.mode#">
					<cfcase value="event">
						EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@registrantID, @itemType='eventRegCustom', @valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
					</cfcase>
					<cfcase value="ticketpackage">
						EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@instanceID, @itemType='ticketPackInstCustom', @valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
					</cfcase>
					<cfcase value="ticket">
						EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@seatID, @itemType='ticketPackSeatCustom', @valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
					</cfcase>
				</cfswitch>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addRegistrantSQL>
	</cffunction>

	<cffunction name="addReg_cf_TEXTBOX_qty" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="qty" type="numeric" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="fieldinventory" type="numeric" required="true">
		<cfargument name="arrQtyAmt" type="array" required="true">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="event">
				<cfset local.trItemType = "Custom">
				<cfset local.itemType = "custom">
				<cfset local.sfdItemType = "eventRegCustom">
				<cfset local.sfdItemID = "@registrantID">
			</cfcase>
			<cfcase value="ticketpackage">
				<cfset local.trItemType = "ticketPackInstCustom">
				<cfset local.itemType = "tpcustom">
				<cfset local.sfdItemType = "ticketPackInstCustom">
				<cfset local.sfdItemID = "@instanceID">
			</cfcase>
			<cfcase value="ticket">
				<cfset local.trItemType = "ticketPackSeatCustom">
				<cfset local.itemType = "tcustom">
				<cfset local.sfdItemType = "ticketPackSeatCustom">
				<cfset local.sfdItemID = "@seatID">
			</cfcase>
		</cfswitch>	

		<cfsavecontent variable="local.addRegistrantSQL">
			<cfoutput>
			SET @detailInt = cast(@detail as int);

			<!--- handle limits and oversold --->
			<cfif arguments.fieldinventory gt 0>
				IF @detailInt > 0 BEGIN
					select @currentInventory = null, @fieldInventoryLimit = #arguments.fieldinventory#;

					select @currentInventory = isnull(sum(fv.valueInteger),0)
						from dbo.cf_fieldData as fd
						inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
						where fd.fieldID = @fieldID;
					set @currentInventory = isnull(@currentInventory,0);

					IF @currentInventory + @detailInt > @fieldInventoryLimit BEGIN
						-- reduce to what is available
						SET @inventoryReducedTo = @fieldInventoryLimit - @currentInventory;

						SELECT @invDetail = fieldText from dbo.cf_fields where fieldID = @fieldID;
						SET @invDetail = @invDetail + ': Quantity reduced from ' + @detail + ' to ' + cast(@inventoryReducedTo as varchar(20));

						UPDATE dbo.ev_registrants 
						SET isFlagged = 1, 
							internalNotes = isnull(internalNotes,'') + @invDetail + '; '
						WHERE registrantID = @registrantID;

						SET @detail = cast(@inventoryReducedTo as varchar(20));
						SET @detailInt = cast(@detail as int);
					END
				END
			</cfif>

			<!--- record data --->
			EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=#local.sfdItemID#, @itemType='#local.sfdItemType#', @valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;		

			<!--- add transaction if money is tied to qty --->
			<cfif arrayLen(arguments.arrQtyAmt)>
				<cfset local.qtyNum = 1>
				<cfloop array="#arguments.arrQtyAmt#" index="local.thisQty">
					INSERT INTO @tblMonetaryQtyFieldDetails (qty, amount, taxAmount)
					VALUES (#local.qtyNum#, #local.thisQty.amount#, #local.thisQty.taxAmount#);
					<cfset local.qtyNum = local.qtyNum + 1>
				</cfloop>

				IF @detailInt > 0 BEGIN
					select @invDetail = null, @invoiceProfileID = null, @invoiceID = null, @deferredGLAccountID = null;
					set @GLAccountID = #arguments.GLAccountID#;
					select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
					select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
					select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
					SELECT @invDetail = left(@eventTitle + isnull(' - ' + fieldReference,' - QTY'),500) from dbo.cf_fields where fieldID = @fieldID;

					-- if necessary, create invoice assigned to payer based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@accrualDate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);

						IF @registrationMerchantProfiles is not null
							EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
					END

					-- remove oversold items
					DELETE FROM @tblMonetaryQtyFieldDetails WHERE qty > @detailInt;

					set @counter = 1;
					while @counter <= @detailInt begin
						select @taxAmount=null, @detailAmt=null, @xmlSchedule = null, @customTransactionID = null;
					
						select @taxAmount = taxAmount, @detailAmt = amount
						from @tblMonetaryQtyFieldDetails
						where qty = @counter;

						-- handle deferred revenue
						IF @deferredGLAccountID is not null
							set @xmlSchedule = '<rows><row amt="' + cast(@detailAmt as varchar(16)) + '" dt="' + @deferredDateStr + '" /></rows>';

						EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
							@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @status='Active', @detail=@invDetail, @parentTransactionID=@rateTransactionID, 
							@amount=@detailAmt, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
							@zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, 
							@transactionID=@customTransactionID OUTPUT;
						EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@customTransactionID, 
							@itemType='#local.trItemType#', @itemID=@dataID, @subItemID=@counter;

						set @counter = @counter + 1;
					end
				END
				
				-- clear table
				DELETE FROM @tblMonetaryQtyFieldDetails;
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addRegistrantSQL>
	</cffunction>

	<cffunction name="addReg_cf_TEXTBOX_amt" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="amount" type="numeric" required="true">
		<cfargument name="objAccounting" type="any" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="taxInfoStruct" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.strTaxCF = arguments.objAccounting.getTaxForUncommittedSale(saleGLAccountID=arguments.GLAccountID, saleAmount=arguments.amount, 
											transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
											stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax)>
		
		<cfswitch expression="#arguments.mode#">
			<cfcase value="event">
				<cfset local.trItemType = "Custom">
				<cfset local.itemType = "custom">
				<cfset local.sfdItemType = "eventRegCustom">
				<cfset local.sfdItemID = "@registrantID">
			</cfcase>
			<cfcase value="ticketpackage">
				<cfset local.trItemType = "ticketPackInstCustom">
				<cfset local.itemType = "tpcustom">
				<cfset local.sfdItemType = "ticketPackInstCustom">
				<cfset local.sfdItemID = "@instanceID">
			</cfcase>
			<cfcase value="ticket">
				<cfset local.trItemType = "ticketPackSeatCustom">
				<cfset local.itemType = "tcustom">
				<cfset local.sfdItemType = "ticketPackSeatCustom">
				<cfset local.sfdItemID = "@seatID">
			</cfcase>
		</cfswitch>	

		<cfsavecontent variable="local.addRegistrantSQL">
			<cfoutput>
			<!--- record data --->
			EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=#local.sfdItemID#, @itemType='#local.sfdItemType#', @valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;

			<!--- add transaction --->
			set @detailAmt = cast(@detail as decimal(18,2));
			IF @detailAmt > 0 BEGIN
				select @invDetail = null, @invoiceProfileID = null, @invoiceID = null, @customTransactionID = null;
				set @GLAccountID = #arguments.GLAccountID#;
				select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
				select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				set @taxAmount = #val(local.strTaxCF.totalTaxAmt)#;

				SELECT @invDetail = left(@eventTitle + isnull(' - ' + fieldReference,' - Amount'),500) from dbo.cf_fields where fieldID = @fieldID;

				-- if necessary, create invoice assigned to payer based on invoice profile
				IF @invoiceID is null BEGIN
					EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
						@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@accrualDate, 
						@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

					insert into @tblInvoices (invoiceID, invoiceProfileID)
					values (@invoiceID, @invoiceProfileID);

					IF @registrationMerchantProfiles is not null
						EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
				END

				-- handle deferred revenue
				select @xmlSchedule = null, @deferredGLAccountID = null;
				select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
				IF @deferredGLAccountID is not null
					set @xmlSchedule = '<rows><row amt="#arguments.amount#" dt="' + @deferredDateStr + '" /></rows>';

				EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
					@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
					@statsSessionID=@statsSessionID, @status='Active', @detail=@invDetail, @parentTransactionID=@rateTransactionID, 
					@amount=#arguments.amount#, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
					@zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, 
					@transactionID=@customTransactionID OUTPUT;
				EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@customTransactionID, 
					@itemType='#local.trItemType#', @itemID=@dataID, @subItemID=null;
			END
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addRegistrantSQL>
	</cffunction>

	<cffunction name="editReg_cf_TEXTBOX" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		<cfargument name="offerQty" type="boolean" required="true">
		<cfargument name="offerAmount" type="boolean" required="true">
		<cfargument name="fieldinventory" type="numeric" required="true">
		<cfargument name="objAccounting" type="any" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="taxInfoStruct" type="struct" required="true">
		<cfargument name="removeQtyItems" type="string" required="true">
		<cfargument name="arrQtyAmt" type="array" required="true">
		<cfargument name="detailID" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="event">
				<cfset local.trItemType = "Custom">
				<cfset local.itemType = "custom">
				<cfset local.sfdItemType = "eventRegCustom">
				<cfset local.sfdItemID = "@registrantID">
				<cfset local.itemID = arguments.registrantID>
			</cfcase>
			<cfcase value="ticketpackage">
				<cfset local.trItemType = "ticketPackInstCustom">
				<cfset local.itemType = "tpcustom">
				<cfset local.sfdItemType = "ticketPackInstCustom">
				<cfset local.sfdItemID = "@instanceID">
				<cfset local.itemID = arguments.detailID>
			</cfcase>
			<cfcase value="ticket">
				<cfset local.trItemType = "ticketPackSeatCustom">
				<cfset local.itemType = "tcustom">
				<cfset local.sfdItemType = "ticketPackSeatCustom">
				<cfset local.sfdItemID = "@seatID">
				<cfset local.itemID = arguments.detailID>
			</cfcase>
		</cfswitch>

		<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
			select dataID  
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
			<cfif arguments.detailID gt 0>
				and dataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.itemID#">
			<cfelse>
				and itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.itemID#">
			</cfif>
			and itemType = '#local.sfdItemType#'
		</cfquery>

		<cfif NOT local.qryGetDataID.recordcount>
			<cfset local.editRegistrantSQL = addReg_cf_TEXTBOX(mode=arguments.mode, fieldID=arguments.fieldID, customText=arguments.customText, 
				offerQty=arguments.offerQty, offerAmount=arguments.offerAmount, fieldinventory=arguments.fieldinventory, objAccounting=arguments.objAccounting, 
				GLAccountID=arguments.GLAccountID, taxInfoStruct=arguments.taxInfoStruct, arrQtyAmt=arguments.arrQtyAmt)>
		<cfelse>
			<!--- if we offer adhoc amount, clean value so no commas etc --->
			<cfif arguments.offerAmount or arguments.offerQty>
				<cfset arguments.customText = val(ReReplace(arguments.customText,'[^0-9\.]','','ALL'))>
			</cfif>

			<cfsavecontent variable="local.editRegistrantSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = #local.qryGetDataID.dataID#;

				<!--- qty questions --->
				<cfif arguments.offerQty>
					<cfset local.tempSQL = editReg_cf_TEXTBOX_qty(mode=arguments.mode, trItemType=local.trItemType, qty=arguments.customText, GLAccountID=arguments.GLAccountID, 
													offerAmount=arguments.offerAmount, fieldinventory=arguments.fieldinventory, removeQtyItems=arguments.removeQtyItems,
													arrQtyAmt=arguments.arrQtyAmt)>
					#local.tempSQL#
				
				<!--- adhoc amount questions --->
				<cfelseif arguments.offerAmount>
					<cfset local.tempSQL = editReg_cf_TEXTBOX_amt(mode=arguments.mode, trItemType=local.trItemType, qtyAdhocAmt=arguments.customText, 
													objAccounting=arguments.objAccounting, GLAccountID=arguments.GLAccountID, taxInfoStruct=arguments.taxInfoStruct)>
					#local.tempSQL#

				<!--- text questions --->
				<cfelse>
					set @valueID = null;

					EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
						@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

					UPDATE dbo.cf_fieldData
					SET valueID = @valueID
					WHERE dataID = @dataID;
				</cfif>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.editRegistrantSQL>
	</cffunction>

	<cffunction name="editReg_cf_TEXTBOX_qty" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="trItemType" type="string" required="true">
		<cfargument name="qty" type="numeric" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="offerAmount" type="boolean" required="true">
		<cfargument name="fieldinventory" type="numeric" required="true">
		<cfargument name="removeQtyItems" type="string" required="true">
		<cfargument name="arrQtyAmt" type="array" required="true">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.editRegistrantSQL">
			<cfoutput>
			<!--- if removing qty --->
			<cfif listLen(arguments.removeQtyItems)>
				<cfloop list="#listSort(arguments.removeQtyItems,'numeric','desc')#" index="local.delSubItemID">
					set @customTransactionIDForAdj = null;

					select @customTransactionIDForAdj = min(transactionID) 
					from @tblExistingTrans 
					where itemType = '#arguments.trItemType#' 
					and itemID = @dataID 
					and subItemID = #val(local.delSubItemID)#;

					WHILE @customTransactionIDForAdj IS NOT NULL BEGIN
						SELECT @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null, @oldtotalRegCustomFee = null, @oldtotalRegCustomFeeNeg = null;

						SELECT @oldtotalRegCustomFee = amount FROM @tblExistingTrans where transactionID = @customTransactionIDForAdj;

						set @oldtotalRegCustomFee = isNull(@oldtotalRegCustomFee,0);
						set @oldtotalRegCustomFeeNeg = @oldtotalRegCustomFee * -1;

						-- adjust custom sale transaction to 0 if exists
						IF @oldtotalRegCustomFee > 0 BEGIN
							select @invoiceProfileID = gl.invoiceProfileID
							from dbo.tr_glAccounts as gl
							inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.glAccountID
							where t.transactionID = @customTransactionIDForAdj;

							select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

							-- if necessary, create invoice assigned to registrant based on invoice profile
							IF @invoiceID is null BEGIN
								EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
									@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
									@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
			
								insert into @tblInvoices (invoiceID, invoiceProfileID)
								values (@invoiceID, @invoiceProfileID);

								IF @registrationMerchantProfiles is not null
									EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
							END	

							EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@loggedInMemberID, 
								@statsSessionID=@statsSessionID, @amount=@oldtotalRegCustomFeeNeg, @taxAmount=null, @transactionDate=@nowDate, 
								@autoAdjustTransactionDate=1, @saleTransactionID=@customTransactionIDForAdj, @invoiceID=@invoiceID, 
								@byPassTax=0, @byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;
						END

						select @customTransactionIDForAdj = min(transactionID) 
						from @tblExistingTrans 
						where itemType = '#arguments.trItemType#' 
						and itemID = @dataID 
						and subItemID = #val(local.delSubItemID)#
						and transactionID > @customTransactionIDForAdj;
					END

					-- update regDetails to reduce qty by 1
					select @detail = null, @valueID = null, @oldQtyVal = null, @newQtyVal = null;

					select @oldQtyVal = isnull(valueInteger,0)
					from dbo.cf_fieldData as fd 
					inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
					where fd.dataID = @dataID;

					select @newQtyVal = case when @oldQtyVal <= 1 then 0 else isnull(@oldQtyVal,1) - 1 end;
					set @detail = cast(@newQtyVal as varchar(10))

					EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
						@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

					UPDATE dbo.cf_fieldData
					SET valueID = @valueID
					WHERE dataID = @dataID;
					
					-- change tr_application status to D
					UPDATE dbo.tr_applications
					SET status = 'D'
					WHERE itemType = '#arguments.trItemType#'
					AND applicationTypeID = @EventsAppTypeID
					AND itemID = @dataID
					AND subItemID = #val(local.delSubItemID)#;

					-- update tr_application sub item id
					UPDATE dbo.tr_applications
					SET subItemID = subItemID - 1
					WHERE itemType = '#arguments.trItemType#'
					AND applicationTypeID = @EventsAppTypeID
					AND itemID = @dataID
					AND status = 'A'
					AND subItemID > #val(local.delSubItemID)#;
				</cfloop>
			</cfif>

			<cfif arguments.qty gt 0>
				set @detailInt = #arguments.qty#;

				<!--- handle limits and oversold --->
				<cfif arguments.fieldinventory gt 0>
					IF @detailInt > 0 BEGIN
						select @currentInventory = null, @oldQtyVal=null, @newInventoryCount=null, @fieldInventoryLimit = #arguments.fieldinventory#;

						select @currentInventory = isnull(sum(fv.valueInteger),0)
							from dbo.cf_fieldData as fd
							inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
							where fd.fieldID = @fieldID;
						set @currentInventory = isnull(@currentInventory,0);

						select @oldQtyVal = isnull(valueInteger,0)
						from dbo.cf_fieldData as fd
						inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
						where fd.dataID = @dataID;
						set @oldQtyVal = isnull(@oldQtyVal,0);

						<cfif arguments.offerAmount is 1>
							SET @newInventoryCount = @currentInventory + @detailInt;
						<cfelse>
							SET @newInventoryCount = (@currentInventory - @oldQtyVal) + @detailInt;
						</cfif>

						IF @newInventoryCount > @fieldInventoryLimit BEGIN
							select @inventoryReducedTo=null, @totalQty=null, @newQtyVal=null;

							-- reduce to what is available
							<cfif arguments.offerAmount is 1>
								SET @inventoryReducedTo = @fieldInventoryLimit - @currentInventory;
								SET @totalQty = @oldQtyVal + @detailInt;
								SET @newQtyVal = @oldQtyVal + @inventoryReducedTo;
							<cfelse>
								SET @inventoryReducedTo = @oldQtyVal + @fieldInventoryLimit - @currentInventory;
								SET @totalQty = @detailInt;
								SET @newQtyVal = @inventoryReducedTo;
							</cfif>
							
							SELECT @invDetail = fieldText from dbo.cf_fields where fieldID = @fieldID;
							SET @invDetail = @invDetail + ': Quantity reduced from ' + cast(@totalQty as varchar(20)) + ' to ' + cast(@newQtyVal as varchar(20));

							UPDATE dbo.ev_registrants 
							SET isFlagged = 1, 
								internalNotes = isnull(internalNotes,'') + @invDetail + '; '
							WHERE registrantID = @registrantID;

							SET @detail = cast(@inventoryReducedTo as varchar(20));
							SET @detailInt = cast(@detail as int);
						END
					END
				</cfif>

				IF @detailInt > 0 BEGIN
					SELECT @valueID = null, @oldQtyVal = null, @detailAmt = 0;

					<!--- qty fields supporting amount --->
					<cfif arguments.offerAmount is 1>
						select @oldQtyVal = isnull(valueInteger,0)
						from dbo.cf_fieldData as fd 
						inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
						where fd.dataID = @dataID;

						SET @detail = cast((@oldQtyVal + @detailInt) as varchar(10));
					<cfelse>
						SET @detail = cast(@detailInt as varchar(10));
					</cfif>

					EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
						@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

					UPDATE dbo.cf_fieldData
					SET valueID = @valueID
					WHERE dataID = @dataID;
				END

				<!--- add transaction if money is tied to new qty --->
				<cfif arguments.offerAmount is 1>
					<cfset local.qtyNum = 1>
					<cfloop array="#arguments.arrQtyAmt#" index="local.thisQty">
						INSERT INTO @tblMonetaryQtyFieldDetails (qty, amount, taxAmount)
						VALUES (#local.qtyNum#, #local.thisQty.amount#, #local.thisQty.taxAmount#);
						<cfset local.qtyNum = local.qtyNum + 1>
					</cfloop>

					IF @detailInt > 0 BEGIN
						select @invDetail = null, @invoiceProfileID = null, @invoiceID = null, @deferredGLAccountID = null;
						set @GLAccountID = #arguments.GLAccountID#;
						select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
						select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
						select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
						SELECT @invDetail = left(@eventTitle + isnull(' - ' + fieldReference,' - QTY'),500) from dbo.cf_fields where fieldID = @fieldID;

						-- if necessary, create invoice assigned to payer based on invoice profile
						IF @invoiceID is null BEGIN
							EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
								@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
								@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

							insert into @tblInvoices (invoiceID, invoiceProfileID)
							values (@invoiceID, @invoiceProfileID);

							IF @registrationMerchantProfiles is not null
								EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
						END

						-- remove oversold items
						DELETE FROM @tblMonetaryQtyFieldDetails WHERE qty > @detailInt;

						set @counter = 1;
						while @counter <= @detailInt begin
							select @taxAmount=null, @detailAmt=null, @xmlSchedule = null, @customTransactionID = null, @thisSubItemID = null;
						
							select @taxAmount = taxAmount, @detailAmt = amount
							from @tblMonetaryQtyFieldDetails
							where qty = @counter;

							-- handle deferred revenue
							IF @deferredGLAccountID is not null
								set @xmlSchedule = '<rows><row amt="' + cast(@detailAmt as varchar(16)) + '" dt="' + @deferredDateStr + '" /></rows>';

							set @thisSubItemID = @oldQtyVal + @counter;

							EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
								@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
								@statsSessionID=@statsSessionID, @status='Active', @detail=@invDetail, 
								@parentTransactionID=@rateTransactionID, @amount=@detailAmt, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, 
								@invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, @taxAmount=@taxAmount, 
								@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, 
								@transactionID=@customTransactionID OUTPUT;
							EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@customTransactionID, 
								@itemType='#arguments.trItemType#', @itemID=@dataID, @subItemID=@thisSubItemID;

							set @counter = @counter + 1;
						end
					END
					
					-- clear table
					DELETE FROM @tblMonetaryQtyFieldDetails;
				</cfif>
			<cfelseif arguments.qty EQ 0 AND NOT arguments.offerAmount>
				-- delete qty field data
				IF @dataID IS NOT NULL
					DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.editRegistrantSQL>
	</cffunction>

	<cffunction name="editReg_cf_TEXTBOX_amt" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="trItemType" type="string" required="true">
		<cfargument name="qtyAdhocAmt" type="numeric" required="true">
		<cfargument name="objAccounting" type="any" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="taxInfoStruct" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.editRegistrantSQL">
			<cfoutput>
			select @oldtotalRegCustomFee = null, @customTransactionIDForAdj = null, @detailAmt = null;
			
			select @customTransactionIDForAdj = min(transactionID), @oldtotalRegCustomFee = sum(amount)
			from @tblExistingTrans 
			where itemType = '#arguments.trItemType#'
			and itemID = @dataID;

			select @detailAmt = isnull(fv.valueDecimal2,0)
			from dbo.cf_fieldData as fd
			inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
			where fd.dataID = @dataID;

			set @oldtotalRegCustomFee = isNull(@oldtotalRegCustomFee,0);
			set @newtotalRegCustomFee = #arguments.qtyAdhocAmt#;

			IF @detailAmt <> @newtotalRegCustomFee AND @oldtotalRegCustomFee <> @newtotalRegCustomFee BEGIN

				-- delete old regDetails
				DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;

				<!--- since the fees are different, we need to adjust out the old sale to 0 and add a new sale. --->
				WHILE @customTransactionIDForAdj is not null BEGIN
					select @oldRegCustomFee=null, @oldRegCustomFeeNeg=null;

					select @oldRegCustomFee = isnull(amount,0)
					from @tblExistingTrans 
					where itemType = '#arguments.trItemType#'
					and itemID = @dataID
					and transactionID = @customTransactionIDForAdj;

					-- adjust custom sale transaction to 0 if exists
					IF @oldRegCustomFee > 0 BEGIN
						select @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null, @adjTransactionID = null;

						select @invoiceProfileID = gl.invoiceProfileID
						from dbo.tr_glAccounts as gl
						inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.glAccountID
						where t.transactionID = @customTransactionIDForAdj;

						select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

						-- if necessary, create invoice assigned to registrant based on invoice profile
						IF @invoiceID is null BEGIN
							EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
								@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
								@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
		
							insert into @tblInvoices (invoiceID, invoiceProfileID)
							values (@invoiceID, @invoiceProfileID);

							IF @registrationMerchantProfiles is not null
								EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
						END	

						set @oldRegCustomFeeNeg = @oldRegCustomFee * -1;

						EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @amount=@oldRegCustomFeeNeg, @taxAmount=null, @transactionDate=@nowDate, 
							@autoAdjustTransactionDate=1, @saleTransactionID=@customTransactionIDForAdj, @invoiceID=@invoiceID, 
							@xmlSchedule=null, @byPassTax=0, @byPassAccrual=0, @transactionID=@adjTransactionID OUTPUT;
					END

					UPDATE dbo.tr_applications
					SET status = 'D'
					WHERE itemType = '#arguments.trItemType#'
					AND applicationTypeID = @EventsAppTypeID
					AND itemID = @dataID
					AND transactionID = @customTransactionIDForAdj;

					select @customTransactionIDForAdj = min(transactionID)
					from @tblExistingTrans 
					where itemType = '#arguments.trItemType#'
					and itemID = @dataID
					and transactionID > @customTransactionIDForAdj;
				END

				<cfif arguments.qtyAdhocAmt gt 0>
					<cfset local.strTaxCF = arguments.objAccounting.getTaxForUncommittedSale(saleGLAccountID=arguments.GLAccountID, saleAmount=arguments.qtyAdhocAmt, 
													transactionDate="#dateformat(arguments.taxInfoStruct.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#", 
													stateIDForTax=arguments.taxInfoStruct.stateIDForTax, zipForTax=arguments.taxInfoStruct.zipForTax)>

					-- add new regDetails
					set @dataID = null;
					<cfswitch expression="#arguments.mode#">
						<cfcase value="event">
							EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@registrantID, @itemType='eventRegCustom', 
								@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;

							SET @detail = null;
							SELECT @detail = left(@eventTitle + isnull(' - ' + fieldReference,' - Amount'),500) from dbo.cf_fields where fieldID = @fieldID;
						</cfcase>
						<cfcase value="ticketpackage">
							EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@instanceID, @itemType='ticketPackInstCustom', 
								@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;

							SET @detail = null;
							SELECT @detail = left(@eventTitle + isnull(' - ' + fieldReference,' - Amount'),500) from dbo.cf_fields where fieldID = @fieldID;
						</cfcase>
						<cfcase value="ticket">
							EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@seatID, @itemType='ticketPackSeatCustom', 
								@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;

							SET @detail = null;
							SELECT @detail = left(@eventTitle + isnull(' - ' + fieldReference,' - Amount'),500) from dbo.cf_fields where fieldID = @fieldID;
						</cfcase>
					</cfswitch>

					select @invoiceProfileID = null, @invoiceID = null, @customTransactionID = null;
					set @GLAccountID = #arguments.GLAccountID#;
					set @taxAmount = #val(local.strTaxCF.totalTaxAmt)#;
					select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
					select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

					-- if necessary, create invoice assigned to payer based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);

						IF @registrationMerchantProfiles is not null
							EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
					END

					-- handle deferred revenue
					select @xmlSchedule = null, @deferredGLAccountID = null;
					select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
					IF @deferredGLAccountID is not null
						set @xmlSchedule = '<rows><row amt="#arguments.qtyAdhocAmt#" dt="' + @deferredDateStr + '" /></rows>';

					EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
						@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
						@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @parentTransactionID=@rateTransactionID, 
						@amount=#arguments.qtyAdhocAmt#, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
						@zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, 
						@transactionID=@customTransactionID OUTPUT;
					EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@customTransactionID, 
						@itemType='#arguments.trItemType#', @itemID=@dataID, @subItemID=null;
				</cfif>

			END
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.editRegistrantSQL>
	</cffunction>

	<cffunction name="addReg_cf_SELECTRADIOCHECKBOX" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="offerQty" type="boolean" required="true">
		<cfargument name="offerAmount" type="boolean" required="true">
		<cfargument name="strOptionAmt" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.strOptionArgs = { mode=arguments.mode, offerAmount=arguments.offerAmount, GLAccountID=arguments.GLAccountID, amount=0, taxAmount=0 }>

		<cfsavecontent variable="local.addRegistrantSQL">
			<cfoutput>
			<cfloop list="#arguments.valueIDList#" index="local.valueitem">
				<cfif val(local.valueitem) gt 0>
					set @fieldID = #arguments.fieldID#;
					set @valueID = #val(local.valueitem)#;
					set @dataID = null;
					set @invDetail = null;

					<cfif arguments.offerAmount is 1 and structKeyExists(arguments.strOptionAmt,local.valueitem)>
						<cfset local.strOptionArgs.amount = arguments.strOptionAmt[local.valueitem].amount>
						<cfset local.strOptionArgs.taxAmount = arguments.strOptionAmt[local.valueitem].taxAmount>
					</cfif>

					<cfif arguments.offerQty>
						<cfset local.tempSQL = addReg_cf_SELECTRADIOCHECKBOX_qty(argumentCollection=local.strOptionArgs)>
						#local.tempSQL#
					<cfelse>
						<cfset local.tempSQL = addReg_cf_SELECTRADIOCHECKBOX_nonqty(argumentCollection=local.strOptionArgs)>
						#local.tempSQL#
					</cfif>
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addRegistrantSQL>
	</cffunction>
	
	<cffunction name="addReg_cf_SELECTRADIOCHECKBOX_nonqty" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="offerAmount" type="boolean" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="amount" type="numeric" required="true">
		<cfargument name="taxAmount" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="event">
				<cfset local.trItemType = "Custom">
				<cfset local.itemType = "custom">
				<cfset local.sfdItemType = "eventRegCustom">
				<cfset local.sfdItemID = "@registrantID">
			</cfcase>
			<cfcase value="ticketpackage">
				<cfset local.trItemType = "ticketPackInstCustom">
				<cfset local.itemType = "tpcustom">
				<cfset local.sfdItemType = "ticketPackInstCustom">
				<cfset local.sfdItemID = "@instanceID">
			</cfcase>
			<cfcase value="ticket">
				<cfset local.trItemType = "ticketPackSeatCustom">
				<cfset local.itemType = "tcustom">
				<cfset local.sfdItemType = "ticketPackSeatCustom">
				<cfset local.sfdItemID = "@seatID">
			</cfcase>
		</cfswitch>	

		<cfsavecontent variable="local.addRegistrantSQL">
			<cfoutput>
			<!--- handle option limits --->
			select @currentInventory = null, @fieldInventoryLimit = null;
			select @fieldInventoryLimit = inventory from dbo.cf_fieldValues where valueID = @valueID;
			set @fieldInventoryLimit = isnull(@fieldInventoryLimit,0);

			select @invDetail = left(@eventTitle + isnull(' - ' + f.fieldReference + ': ','') + 
										isnull(case when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(max))
													when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
													when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(15))
													when ft.dataTypeCode = 'BIT' then case when fv.valueBit = 1 then 'Yes' else 'No' end
													when ft.dataTypeCode = 'DATE' then convert(varchar(12), fv.valueDate, 101)
													else null end,''),500)
			from dbo.cf_fields as f
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
			where f.fieldID = @fieldID
			and fv.valueID = @valueID;

			IF @fieldInventoryLimit > 0 BEGIN
				select @currentInventory = count(dataID) from dbo.cf_fieldData where fieldID = @fieldID and valueID = @valueID;
				set @currentInventory = isnull(@currentInventory,0);

				IF @currentInventory + 1 > @fieldInventoryLimit BEGIN
					select @invDetailSold = f.fieldText + ': Removed "' + 
						case 
						when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(max))
						when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
						when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(15))
						when ft.dataTypeCode = 'BIT' then case when fv.valueBit = 1 then 'Yes' else 'No' end
						when ft.dataTypeCode = 'DATE' then convert(varchar(12), fv.valueDate, 101)
						end + '" because that selection is no longer available.'
					from dbo.cf_fields as f
					inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
					inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
					where f.fieldID = @fieldID
					and fv.valueID = @valueID;

					UPDATE dbo.ev_registrants 
					SET isFlagged = 1, 
						internalNotes = isnull(internalNotes,'') + @invDetailSold + '; '
					WHERE registrantID = @registrantID;

					SET @valueID = 0;
				END
			END

			IF @valueID > 0 BEGIN
				<!--- save data --->
				EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=#local.sfdItemID#, @itemType='#local.sfdItemType#', @valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;

				<!--- add transaction if money is tied to field --->
				<cfif arguments.offerAmount is 1>
					select @invoiceProfileID = null, @invoiceID = null, @customTransactionID = null;
					set @GLAccountID = #arguments.GLAccountID#;
					select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
					select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
					set @taxAmount = #arguments.taxAmount#;

					-- if necessary, create invoice assigned to payer based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@accrualDate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);

						IF @registrationMerchantProfiles is not null
							EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
					END

					-- handle deferred revenue
					select @xmlSchedule = null, @deferredGLAccountID = null;
					select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
					IF @deferredGLAccountID is not null
						set @xmlSchedule = '<rows><row amt="#arguments.amount#" dt="' + @deferredDateStr + '" /></rows>';

					EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
						@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
						@statsSessionID=@statsSessionID, @status='Active', @detail=@invDetail, @parentTransactionID=@rateTransactionID, 
						@amount=#arguments.amount#, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
						@taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@customTransactionID OUTPUT;
					EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@customTransactionID, 
						@itemType='#local.trItemType#', @itemID=@dataID, @subItemID=null;
				</cfif>
			END
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addRegistrantSQL>
	</cffunction>

	<cffunction name="addReg_cf_SELECTRADIOCHECKBOX_qty" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="offerAmount" type="boolean" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="amount" type="numeric" required="true">
		<cfargument name="taxAmount" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="event">
				<cfset local.trItemType = "Custom">
				<cfset local.itemType = "custom">
				<cfset local.sfdItemType = "eventRegCustom">
				<cfset local.sfdItemID = "@registrantID">
			</cfcase>
			<cfcase value="ticketpackage">
				<cfset local.trItemType = "ticketPackInstCustom">
				<cfset local.itemType = "tpcustom">
				<cfset local.sfdItemType = "ticketPackInstCustom">
				<cfset local.sfdItemID = "@instanceID">
			</cfcase>
			<cfcase value="ticket">
				<cfset local.trItemType = "ticketPackSeatCustom">
				<cfset local.itemType = "tcustom">
				<cfset local.sfdItemType = "ticketPackSeatCustom">
				<cfset local.sfdItemID = "@seatID">
			</cfcase>
		</cfswitch>	

		<cfsavecontent variable="local.addRegistrantSQL">
			<cfoutput>
			<!--- handle qty limits and oversold --->
			select @currentInventory = null, @fieldInventoryLimit = null, @detailInt = null;
			select @fieldInventoryLimit = inventory from dbo.cf_fields where fieldID = @fieldID;
			set @fieldInventoryLimit = isnull(@fieldInventoryLimit,0);

			select @invDetail = left(@eventTitle + isnull(' - ' + f.fieldReference + ': ','') + isnull(cast(fv.valueInteger as varchar(15)),''),500),
				@detailInt = fv.valueInteger
			from dbo.cf_fields as f
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
			where f.fieldID = @fieldID
			and fv.valueID = @valueID;

			IF @fieldInventoryLimit > 0 BEGIN
				select @currentInventory = isnull(sum(fv.valueInteger),0)
					from dbo.cf_fieldData as fd
					inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
					where fd.fieldID = @fieldID;
				set @currentInventory = isnull(@currentInventory,0);

				IF @currentInventory + @detailInt > @fieldInventoryLimit BEGIN
					select @invDetailSold = f.fieldText + ': Removed "' + cast(fv.valueInteger as varchar(15)) + '" because that selection is no longer available.'
					from dbo.cf_fields as f
					inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
					inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
					where f.fieldID = @fieldID
					and fv.valueID = @valueID;

					UPDATE dbo.ev_registrants 
					SET isFlagged = 1, 
						internalNotes = isnull(internalNotes,'') + @invDetailSold + '; '
					WHERE registrantID = @registrantID;

					SET @valueID = 0;
				END
			END			

			IF @valueID > 0 BEGIN
				<!--- save data --->
				EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=#local.sfdItemID#, @itemType='#local.sfdItemType#', @valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;

				<!--- add transaction if money is tied to field --->
				<cfif arguments.offerAmount is 1>
					select @invoiceProfileID = null, @invoiceID = null, @customTransactionID = null;
					set @GLAccountID = #arguments.GLAccountID#;
					select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
					select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
					set @taxAmount = #arguments.taxAmount#;

					-- if necessary, create invoice assigned to payer based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@accrualDate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);

						IF @registrationMerchantProfiles is not null
							EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
					END

					-- handle deferred revenue
					select @xmlSchedule = null, @deferredGLAccountID = null;
					select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
					IF @deferredGLAccountID is not null
						set @xmlSchedule = '<rows><row amt="#arguments.amount#" dt="' + @deferredDateStr + '" /></rows>';

					EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
						@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
						@statsSessionID=@statsSessionID, @status='Active', @detail=@invDetail, @parentTransactionID=@rateTransactionID, 
						@amount=#arguments.amount#, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
						@taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@customTransactionID OUTPUT;
					EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@customTransactionID, 
						@itemType='#local.trItemType#', @itemID=@dataID, @subItemID=null;
				</cfif>
			END
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addRegistrantSQL>
	</cffunction>
	
	<cffunction name="editReg_cf_SELECTRADIOCHECKBOX" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="offerQty" type="boolean" required="true">
		<cfargument name="offerAmount" type="boolean" required="true">
		<cfargument name="strOptionAmt" type="struct" required="true">
		<cfargument name="detailID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="event">
				<cfset local.trItemType = "Custom">
				<cfset local.itemType = "custom">
				<cfset local.sfdItemType = "eventRegCustom">
				<cfset local.sfdItemID = "@registrantID">
				<cfset local.itemID = arguments.registrantID>
			</cfcase>
			<cfcase value="ticketpackage">
				<cfset local.trItemType = "ticketPackInstCustom">
				<cfset local.itemType = "tpcustom">
				<cfset local.sfdItemType = "ticketPackInstCustom">
				<cfset local.sfdItemID = "@instanceID">
				<cfset local.itemID = arguments.detailID>
			</cfcase>
			<cfcase value="ticket">
				<cfset local.trItemType = "ticketPackSeatCustom">
				<cfset local.itemType = "tcustom">
				<cfset local.sfdItemType = "ticketPackSeatCustom">
				<cfset local.sfdItemID = "@seatID">
				<cfset local.itemID = arguments.detailID>
			</cfcase>
		</cfswitch>

		<!--- existing options --->
		<cfquery name="local.qryExistingOptions" datasource="#application.dsn.membercentral.dsn#">
			select dataID, valueID
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
			and itemType = '#local.sfdItemType#'
			<cfif arguments.detailID gt 0>
				and itemID = (select itemID from dbo.cf_fieldData where dataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.detailID#">)
			<cfelse>
				and itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.itemID#">
			</cfif>
		</cfquery>

		<!--- get any options we need to remove --->
		<cfquery name="local.qryOptionsToRemove" dbtype="query">
			select dataID, valueID
			from [local].qryExistingOptions
			<cfif listLen(arguments.valueIDList)>
				where valueID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.valueIDList#" list="true">)
			</cfif>
		</cfquery>
		
		<!--- get any options we need to add --->
		<cfif listLen(arguments.valueIDList)>
			<cfquery name="local.qryOptionsToAdd" datasource="#application.dsn.membercentral.dsn#">
				select valueID
				from dbo.cf_fieldValues
				where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
				and valueID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.valueIDList#" list="true">)
				<cfif local.qryExistingOptions.recordcount>
					and valueID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#valueList(local.qryExistingOptions.valueID)#" list="true">)
				</cfif>
			</cfquery>
			<cfset local.optionsToAdd = valueList(local.qryOptionsToAdd.valueID)>
		<cfelse>
			<cfset local.optionsToAdd = "">
		</cfif>

		<!--- get any existing options not deleted --->
		<cfquery name="local.qryExistingOptionsNotDeleted" dbtype="query">
			select dataID, valueID
			from [local].qryExistingOptions
			<cfif local.qryOptionsToRemove.recordcount>
				where valueID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#valueList(local.qryOptionsToRemove.valueID)#" list="true">)
			</cfif>
		</cfquery>

		<cfsavecontent variable="local.editRegistrantSQL">
			<cfoutput>
			<!--- remove options we dont want --->
			<cfloop query="local.qryOptionsToRemove">
				select @dataID=null, @customTransactionIDForAdj = null, @oldtotalRegCustomFee = null;

				set @dataID = #local.qryOptionsToRemove.dataID#;
				
				select @customTransactionIDForAdj = min(transactionID), @oldtotalRegCustomFee = sum(amount)
				from @tblExistingTrans 
				where itemType = '#local.trItemType#' 
				and itemID = @dataID;

				set @oldtotalRegCustomFee = isNull(@oldtotalRegCustomFee,0);
				
				-- adjust custom sale transaction to 0 if exists
				IF @oldtotalRegCustomFee > 0 BEGIN
					WHILE @customTransactionIDForAdj is not null BEGIN
						select @oldRegCustomFee=null, @oldRegCustomFeeNeg=null;

						select @oldRegCustomFee = isnull(amount,0)
						from @tblExistingTrans 
						where itemType = '#local.trItemType#' 
						and itemID = @dataID
						and transactionID = @customTransactionIDForAdj;

						IF @oldRegCustomFee > 0 BEGIN
							select @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null, @adjTransactionID = null;

							select @invoiceProfileID = gl.invoiceProfileID
							from dbo.tr_glAccounts as gl
							inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.glAccountID
							where t.transactionID = @customTransactionIDForAdj;

							select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

							-- if necessary, create invoice assigned to registrant based on invoice profile
							IF @invoiceID is null BEGIN
								EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
									@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
									@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
			
								insert into @tblInvoices (invoiceID, invoiceProfileID)
								values (@invoiceID, @invoiceProfileID);

								IF @registrationMerchantProfiles is not null
									EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
							END	

							set @oldRegCustomFeeNeg = @oldRegCustomFee * -1;

							EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@loggedInMemberID, 
								@statsSessionID=@statsSessionID, @amount=@oldRegCustomFeeNeg, @taxAmount=null, @transactionDate=@nowDate, 
								@autoAdjustTransactionDate=1, @saleTransactionID=@customTransactionIDForAdj, @invoiceid=@invoiceid, 
								@byPassTax=0, @byPassAccrual=0, @xmlSchedule=null, @transactionID=@adjTransactionID OUTPUT;
						END

						UPDATE dbo.tr_applications
						SET status = 'D'
						WHERE itemType = '#local.trItemType#'
						AND applicationTypeID = @EventsAppTypeID
						AND itemID = @dataID
						AND transactionID = @customTransactionIDForAdj;

						select @customTransactionIDForAdj = min(transactionID)
						from @tblExistingTrans 
						where itemType = '#local.trItemType#' 
						and itemID = @dataID
						and transactionID > @customTransactionIDForAdj;
					END
				END
		
				-- update regDetails
				DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
			</cfloop>

			<!--- add new options. pass in the new options only --->
			<cfset local.tempSQL = addReg_cf_SELECTRADIOCHECKBOX(mode=arguments.mode, fieldID=arguments.fieldID, valueIDList=local.optionsToAdd, 
					GLAccountID=arguments.GLAccountID, offerQty=arguments.offerQty, offerAmount=arguments.offerAmount, strOptionAmt=arguments.strOptionAmt)>
			#local.tempSQL#

			<cfif arguments.offerAmount is 1 and local.qryExistingOptionsNotDeleted.recordcount>
				set @fieldID = #arguments.fieldID#;

				<cfloop query="local.qryExistingOptionsNotDeleted">
					select @customTransactionIDForAdj = null, @oldtotalRegCustomFee = null, @newtotalRegCustomFee = null;
					set @valueID = #local.qryExistingOptionsNotDeleted.valueID#;
					set @dataID = #local.qryExistingOptionsNotDeleted.dataID#;
					set @newtotalRegCustomFee = #arguments.strOptionAmt[local.qryExistingOptionsNotDeleted.valueID].amount#;

					select @customTransactionIDForAdj = min(transactionID), @oldtotalRegCustomFee = sum(amount)
					from @tblExistingTrans 
					where itemType = '#local.trItemType#' 
					and itemID = @dataID;
					
					set @oldtotalRegCustomFee = isnull(@oldtotalRegCustomFee,0);
					
					IF @oldtotalRegCustomFee <> @newtotalRegCustomFee BEGIN
						UPDATE dbo.cf_fieldData
						SET amount = @newtotalRegCustomFee
						WHERE dataID = @dataID;

						WHILE @customTransactionIDForAdj IS NOT NULL BEGIN
							select @oldRegCustomFee=null, @oldRegCustomFeeNeg=null;

							select @oldRegCustomFee = isnull(amount,0)
							from @tblExistingTrans 
							where itemType = '#local.trItemType#' 
							and itemID = @dataID
							and transactionID = @customTransactionIDForAdj;

							-- adjust custom sale transaction to 0 if exists
							IF @oldRegCustomFee > 0 BEGIN
								select @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null, @adjTransactionID = null;
								set @oldRegCustomFeeNeg = @oldRegCustomFee * -1;

								select @invoiceProfileID = gl.invoiceProfileID
								from dbo.tr_glAccounts as gl
								inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.glAccountID
								where t.transactionID = @customTransactionIDForAdj;

								select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

								-- if necessary, create invoice assigned to registrant based on invoice profile
								IF @invoiceID is null BEGIN
									EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
										@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
										@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
				
									insert into @tblInvoices (invoiceID, invoiceProfileID)
									values (@invoiceID, @invoiceProfileID);

									IF @registrationMerchantProfiles is not null
										EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
								END	

								EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@loggedInMemberID, 
									@statsSessionID=@statsSessionID, @amount=@oldRegCustomFeeNeg, @taxAmount=null, @transactionDate=@nowDate, 
									@autoAdjustTransactionDate=1, @saleTransactionID=@customTransactionIDForAdj, @invoiceid=@invoiceid, 
									@byPassTax=0, @byPassAccrual=0, @xmlSchedule=null, @transactionID=@adjTransactionID OUTPUT;
							END

							UPDATE dbo.tr_applications
							SET status = 'D'
							WHERE itemType = '#local.trItemType#'
							AND applicationTypeID = @EventsAppTypeID
							AND itemID = @dataID
							AND transactionID = @customTransactionIDForAdj;

							select @customTransactionIDForAdj = min(transactionID)
							from @tblExistingTrans 
							where itemType = '#local.trItemType#' 
							and itemID = @dataID
							and transactionID > @customTransactionIDForAdj;
						END
						
						-- new sale
						IF @newtotalRegCustomFee >= 0 BEGIN
							select @invoiceProfileID = null, @invoiceID = null, @customTransactionID = null, @invDetail = null;
							set @GLAccountID = #arguments.GLAccountID#;
							select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
							select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
							set @taxAmount = #arguments.strOptionAmt[local.qryExistingOptionsNotDeleted.valueID].taxAmount#;

							select @invDetail = left(@eventTitle + isnull(' - ' + f.fieldReference + ': ','') + 
													isnull(case when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(max))
																when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
																when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(15))
																when ft.dataTypeCode = 'BIT' then case when fv.valueBit = 1 then 'Yes' else 'No' end
																when ft.dataTypeCode = 'DATE' then convert(varchar(12), fv.valueDate, 101)
																else null end,''),500)
							from dbo.cf_fields as f
							inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
							inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
							where f.fieldID = @fieldID
							and fv.valueID = @valueID;

							-- if necessary, create invoice assigned to payer based on invoice profile
							IF @invoiceID is null BEGIN
								EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
									@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
									@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

								insert into @tblInvoices (invoiceID, invoiceProfileID)
								values (@invoiceID, @invoiceProfileID);

								IF @registrationMerchantProfiles is not null
									EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
							END

							-- handle deferred revenue
							select @xmlSchedule = null, @deferredGLAccountID = null;
							select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
							IF @deferredGLAccountID is not null
								set @xmlSchedule = '<rows><row amt=" ' + cast(@newtotalRegCustomFee as varchar(16)) + ' " dt="' + @deferredDateStr + '" /></rows>';

							EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
								@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
								@statsSessionID=@statsSessionID, @status='Active', @detail=@invDetail, @parentTransactionID=@rateTransactionID, 
								@amount=@newtotalRegCustomFee, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
								@zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@customTransactionID OUTPUT;
							EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@customTransactionID, 
								@itemType='#local.trItemType#', @itemID=@dataID, @subItemID=null;
						END
					END
				</cfloop>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.editRegistrantSQL>
	</cffunction>

	<cffunction name="addReg_cf_DATE" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.customText)>
			<cfsavecontent variable="local.addRegistrantSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = null;

				<cfswitch expression="#arguments.mode#">
					<cfcase value="event">
						EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@registrantID, @itemType='eventRegCustom', 
							@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
					</cfcase>
					<cfcase value="ticketpackage">
						EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@instanceID, @itemType='ticketPackInstCustom', 
							@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
					</cfcase>
					<cfcase value="ticket">
						EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@seatID, @itemType='ticketPackSeatCustom', 
							@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
					</cfcase>
				</cfswitch>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.addRegistrantSQL = "">
		</cfif>

		<cfreturn local.addRegistrantSQL>
	</cffunction>

	<cffunction name="editReg_cf_DATE" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		<cfargument name="detailID" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="event">
				<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
					select dataID 
					from dbo.cf_fieldData 
					where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
					and itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrantID#">
					and itemType = 'eventRegCustom'
				</cfquery>
			</cfcase>
			<cfcase value="ticketpackage">
				<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
					select dataID 
					from dbo.cf_fieldData 
					where dataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.detailID#">
					and fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
					and itemType = 'ticketPackInstCustom'
				</cfquery>
			</cfcase>
			<cfcase value="ticket">
				<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
					select dataID 
					from dbo.cf_fieldData 
					where dataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.detailID#">
					and fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
					and itemType = 'ticketPackSeatCustom'
				</cfquery>
			</cfcase>
		</cfswitch>

		<cfif NOT local.qryGetDataID.recordcount>
			<cfset local.editRegistrantSQL = addReg_cf_DATE(mode=arguments.mode, fieldID=arguments.fieldID, customText=arguments.customText)>
		<cfelse>
			<cfsavecontent variable="local.editRegistrantSQL">
				<cfoutput>
				set @dataID = #local.qryGetDataID.dataID#;
								
				<cfif len(arguments.customText)>
					set @detail = '#arguments.customText#';
					set @fieldID = #arguments.fieldID#;
					set @valueID = null;

					EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
						@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

					UPDATE dbo.cf_fieldData
					SET valueID = @valueID
					WHERE dataID = @dataID;
				<cfelse>
					DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
				</cfif>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.editRegistrantSQL>
	</cffunction>

	<cffunction name="addReg_cf_TEXTAREA" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfsavecontent variable="local.addRegistrantSQL">
			<cfoutput>
			set @fieldID = #arguments.fieldID#;
			set @detail = '#replace(arguments.customText,"'","''","ALL")#';
			set @dataID = null;

			<cfswitch expression="#arguments.mode#">
				<cfcase value="event">
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@registrantID, @itemType='eventRegCustom', 
						@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfcase>
				<cfcase value="ticketpackage">
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@instanceID, @itemType='ticketPackInstCustom', 
						@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfcase>
				<cfcase value="ticket">
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@seatID, @itemType='ticketPackSeatCustom', 
						@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfcase>
			</cfswitch>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addRegistrantSQL>
	</cffunction>
	
	<cffunction name="editReg_cf_TEXTAREA" access="private" output="false" returntype="string">
		<cfargument name="mode" type="string" required="true" hint="event, ticketpackage, or ticket">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		<cfargument name="detailID" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="event">
				<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
					select dataID 
					from dbo.cf_fieldData 
					where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
					and itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrantID#">
					and itemType = 'eventRegCustom'
				</cfquery>
			</cfcase>
			<cfcase value="ticketpackage">
				<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
					select dataID 
					from dbo.cf_fieldData 
					where dataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.detailID#">
					and fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
					and itemType = 'ticketPackInstCustom'
				</cfquery>
			</cfcase>
			<cfcase value="ticket">
				<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
					select dataID 
					from dbo.cf_fieldData 
					where dataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.detailID#">
					and fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
					and itemType = 'ticketPackSeatCustom'
				</cfquery>
			</cfcase>
		</cfswitch>

		<cfif NOT local.qryGetDataID.recordcount>
			<cfset local.editRegistrantSQL = addReg_cf_TEXTAREA(mode=arguments.mode, fieldID=arguments.fieldID, customText=arguments.customText)>
		<cfelse>
			<cfsavecontent variable="local.editRegistrantSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @dataID = #local.qryGetDataID.dataID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @valueID = null;
				
				EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
					@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

				UPDATE dbo.cf_fieldData
				SET valueID = @valueID
				WHERE dataID = @dataID;
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.editRegistrantSQL>
	</cffunction>

	<cffunction name="getTicketPackagesForEventRate" access="public" output="false" returntype="query">
		<cfargument name="registrationID" type="numeric" required="true">

		<cfset var qryTicketPackages = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTicketPackages">
			SELECT tp.ticketPackageID, t.ticketName + ' \ ' + tp.ticketPackageName as ticketPackageNameExpanded, rtp.rateID, rtp.quantity
			FROM dbo.ev_ticketPackages as tp
			INNER JOIN dbo.ev_tickets as t on t.ticketID = tp.ticketID
			INNER JOIN dbo.ev_rateTicketPackages as rtp on rtp.ticketPackageID = tp.ticketPackageID
			WHERE t.registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">
			ORDER BY t.sortOrder, tp.sortOrder
		</cfquery>
		
		<cfreturn qryTicketPackages>
	</cffunction>
	
	<cffunction name="getTicketPackagesSelected" access="public" output="false" returntype="query">
		<cfargument name="registrantID" type="numeric" required="true">

		<cfset var qryTicketPackagesSelected = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTicketPackagesSelected">
			select rpi.instanceID, rpi.ticketPackageID, tp.ticketID, rpi.availablePriceID, rpi.includedFromRate, tpa.amount as ticketPackageAmount, 
			ps.rangeName, ROW_NUMBER() OVER (partition by rpi.ticketPackageID order by rpi.instanceID, rpi.ticketPackageID) as instanceNum
			from dbo.ev_registrantPackageInstances as rpi
			inner join dbo.ev_ticketPackageAvailable as tpa on tpa.ticketPackageID = rpi.ticketPackageID 
				and tpa.priceID = rpi.availablePriceID 
				and tpa.isActive = 1
			inner join dbo.ev_priceSchedule as ps on ps.scheduleID = tpa.scheduleID
			inner join dbo.ev_ticketPackages as tp on tp.ticketPackageID = rpi.ticketPackageID
			where rpi.registrantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrantID#">
			and rpi.status = 'A'
		</cfquery>
		
		<cfreturn qryTicketPackagesSelected>
	</cffunction>

	<cffunction name="removeRegPackageInstance" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	

		<cfset var local = structNew()>

		<cfset local.instanceID = arguments.event.getValue('instanceID',0)>
		<cfset local.ticketPackageID = arguments.event.getValue('tpID',0)>
		<cfset local.defaultCurrencyType = "">
		<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1>
			<cfset local.defaultCurrencyType = " #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#">
		</cfif>

		<cfquery name="local.qryRegPackageInstances" datasource="#application.dsn.membercentral.dsn#">
			select rpi.instanceID, t.ticketName + ' \ ' + tp.ticketPackageName as ticketPackageNameExpanded, tp.ticketCount,
				rpi.availablePriceID, ROW_NUMBER() OVER (ORDER BY rpi.instanceID) as row
			from dbo.ev_registrantPackageInstances rpi
			inner join dbo.ev_ticketPackages as tp on tp.ticketPackageID = rpi.ticketPackageID
			inner join dbo.ev_tickets as t on t.ticketID = tp.ticketID
			where rpi.ticketPackageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ticketPackageID#">
			and rpi.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrantID',0)#">
			and rpi.status = 'A';
		</cfquery>
		<cfquery name="local.qryThisRegPackageInstance" dbtype="query">
			select *
			from [local].qryRegPackageInstances
			where instanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.instanceID#">
		</cfquery>
		<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrantID',0)#">
			<cfprocresult name="local.qryTotals" resultset="1">
			<cfprocresult name="local.qryRegTransactions" resultset="2">
			<cfprocresult name="local.qryPaymentAllocations" resultset="3">
		</cfstoredproc>
		<cfquery name="local.qryThisRegPackageInstanceDetails" datasource="#application.dsn.membercentral.dsn#">
			select dataID
			from dbo.cf_fieldData
			where itemID = <cfqueryparam value="#local.instanceID#" cfsqltype="CF_SQL_INTEGER">
			and itemType = 'ticketPackInstCustom'
		</cfquery>
		<cfquery name="local.qryThisRegTicketSeatDetails" datasource="#application.dsn.membercentral.dsn#">
			select fd.dataID 
			from dbo.cf_fieldData as fd
			inner join dbo.ev_registrantPackageInstanceSeats as rpis on rpis.seatID = fd.itemID and fd.itemType = 'ticketPackSeatCustom'
			where rpis.instanceID = <cfqueryparam value="#local.instanceID#" cfsqltype="CF_SQL_INTEGER">
			and rpis.status = 'A'
		</cfquery>
		<cfquery name="local.qryThisPackageInstancePrice" dbtype="query">
			select sum(amount) as amount
			from [local].qryRegTransactions
			where itemID = <cfqueryparam value="#local.instanceID#" cfsqltype="CF_SQL_INTEGER">
			and itemType = 'TicketPackInst'
		</cfquery>
		<cfquery name="local.qryThisPackageInstanceCustomPrice" dbtype="query">
			select sum(amount) as amount
			from [local].qryRegTransactions
			where itemID in (0#valueList(local.qryThisRegPackageInstanceDetails.dataID)#)
			and itemType = 'ticketPackInstCustom'
		</cfquery>
		<cfquery name="local.qryThisPackageTicketSeatCustomPrice" dbtype="query">
			select sum(amount) as amount
			from [local].qryRegTransactions
			where itemID in (0#valueList(local.qryThisRegTicketSeatDetails.dataID)#)
			and itemType = 'ticketPackSeatCustom'
		</cfquery>

		<cfreturn local>
	</cffunction>
	
	<cffunction name="createTicketPackageInstance" access="private" output="false" returntype="string">
		<cfargument name="ticketPackageStruct" type="struct" required="true">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.createPackageSQL">
			<cfoutput>
				set @instanceID = null;
				
				INSERT INTO dbo.ev_registrantPackageInstances (registrantID, ticketPackageID, includedFromRate, availablePriceID)
				VALUES (@registrantID, #arguments.ticketPackageStruct.ticketPackageID#, #arguments.ticketPackageStruct.includedFromRate#, #arguments.ticketPackageStruct.availablePriceID#);
					SELECT @instanceID = SCOPE_IDENTITY();

				<cfif arguments.ticketPackageStruct.ticketPackageAmount gt 0 and arguments.ticketPackageStruct.includedFromRate is 0>
					<cfset local.tempSQL = createPackageInstanceSaleDetail(packageAmount=arguments.ticketPackageStruct.ticketPackageAmount, GLAccountID=arguments.ticketPackageStruct.GLAccountID,
													taxAmount=arguments.ticketPackageStruct.ticketPackageTaxAmount, saleDetail=arguments.ticketPackageStruct.ticketPackageSaleDetail)>
					
					#local.tempSQL#
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.createPackageSQL>
	</cffunction>
	
	<cffunction name="createPackageInstanceSaleDetail" access="private" output="false" returntype="string">
		<cfargument name="packageAmount" type="numeric" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="taxAmount" type="numeric" required="true">
		<cfargument name="saleDetail" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.createPackageInstanceSaleSQL">
			<cfoutput>
				select @invoiceProfileID = null, @invoiceID = null, @ticketTransactionID = null;
				set @GLAccountID = #arguments.GLAccountID#;
				select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
				select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				set @taxAmount = #arguments.taxAmount#;
				set @detail = left(@eventTitle + ' - #replace(arguments.saleDetail,"'","''","ALL")#',500);

				-- if necessary, create invoice assigned to payer based on invoice profile
				IF @invoiceID is null BEGIN
					EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
						@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@accrualDate, 
						@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

					insert into @tblInvoices (invoiceID, invoiceProfileID)
					values (@invoiceID, @invoiceProfileID);

					IF @registrationMerchantProfiles is not null
						EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
				END

				-- handle deferred revenue
				select @xmlSchedule = null, @deferredGLAccountID = null;
				select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
				IF @deferredGLAccountID is not null
					set @xmlSchedule = '<rows><row amt="#arguments.packageAmount#" dt="' + @deferredDateStr + '" /></rows>';

				EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
					@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
					@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @parentTransactionID=@rateTransactionID, 
					@amount=#arguments.packageAmount#, @transactionDate=@transDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
					@zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, 
					@transactionID=@ticketTransactionID OUTPUT;
				EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@ticketTransactionID, 
					@itemType='TicketPackInst', @itemID=@instanceID, @subItemID=null;
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.createPackageInstanceSaleSQL>
	</cffunction>
	
	<cffunction name="removeTicketPackageSQL" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfargument name="ticketPackageStruct" type="struct" required="true">
		<cfargument name="arrCustomFields" type="array" required="true">
		<cfargument name="arrTicketCustomFields" type="array" required="true">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.removePackageSQL">
			<cfoutput>
				<cfset local.tempSQL = removePackageInstanceSQL(instanceID=arguments.ticketPackageStruct.instanceID)>
				#local.tempSQL#

				update dbo.ev_registrantPackageInstanceSeats
				set status = 'D'
				where instanceID = #val(arguments.ticketPackageStruct.instanceID)#
				and status = 'A';
				
				update dbo.ev_registrantPackageInstances
				set status = 'D'
				where instanceID = #val(arguments.ticketPackageStruct.instanceID)#
				and status = 'A';
				
				<!--- loop over the package custom fields --->
				<cfloop array="#arguments.arrCustomFields#" index="local.cf">
					<cfif ListLen(local.cf.dataID) gt 0>
						<cfloop list="#local.cf.dataID#" index="local.thisDataID">
							<cfif val(local.thisDataID) gt 0>
								<cfset local.tempSQL = delRegCustomDetails(dataID=val(local.thisDataID), itemID=arguments.ticketPackageStruct.instanceID, sfdItemType='ticketPackInstCustom', 
									fieldID=local.cf.fieldID, trItemType='ticketPackInstCustom', offerAmount=local.cf.offerAmount)>
								#local.tempSQL#
							</cfif>
						</cfloop>
					</cfif>					
				</cfloop>
				
				<cfloop from="1" to="#arguments.ticketPackageStruct.ticketsInPackage#" index="local.thisTicketInstanceSeat">
					<!--- loop over the ticket custom fields --->
					<cfloop array="#arguments.arrTicketCustomFields[local.thisTicketInstanceSeat]#" index="local.cf">
						<cfif ListLen(local.cf.dataID) gt 0>
							<cfloop list="#local.cf.dataID#" index="local.thisDataID">
								<cfif val(local.thisDataID) gt 0 and val(local.cf.seatID) gt 0>									
									<cfset local.tempSQL = delRegCustomDetails(dataID=val(local.thisDataID), itemID=val(local.cf.seatID), sfdItemType='ticketPackSeatCustom', fieldID=val(local.cf.fieldID), 
										trItemType='ticketPackSeatCustom', offerAmount=local.cf.offerAmount)>
									#local.tempSQL#
								</cfif>
							</cfloop>
						</cfif>
					</cfloop>
					
				</cfloop><!--- end loop of seats --->
				
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.removePackageSQL>
	</cffunction>
	
	<cffunction name="removePackageInstanceSQL" access="private" output="false" returntype="string">
		<cfargument name="instanceID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.removePackageInstanceSQL">
			<cfoutput>
				select @customTransactionIDForAdj = null, @olddetailID = null;
				set @olddetailID = #arguments.instanceID#;

				select @customTransactionIDForAdj = min(transactionID)
				from @tblExistingTrans 
				where itemType = 'TicketPackInst'
				and itemID = @olddetailID;

				WHILE @customTransactionIDForAdj is not null BEGIN
					select @oldRegPackageFee=null, @oldRegPackageFeeNeg=null;

					select @oldRegPackageFee = isnull(amount,0)
					from @tblExistingTrans 
					where itemType = 'TicketPackInst'
					and itemID = @olddetailID
					and transactionID = @customTransactionIDForAdj;

					-- adjust custom sale transaction to 0 if exists
					IF @oldRegPackageFee > 0 BEGIN
						select @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null, @adjTransactionID = null;

						select @invoiceProfileID = gl.invoiceProfileID
						from dbo.tr_glAccounts as gl
						inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.glAccountID
						where t.transactionID = @customTransactionIDForAdj;

						select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

						-- if necessary, create invoice assigned to registrant based on invoice profile
						IF @invoiceID is null BEGIN
							EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
								@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
								@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
		
							insert into @tblInvoices (invoiceID, invoiceProfileID)
							values (@invoiceID, @invoiceProfileID);

							IF @registrationMerchantProfiles is not null
								EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
						END	

						set @oldRegPackageFeeNeg = @oldRegPackageFee * -1;

						EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @amount=@oldRegPackageFeeNeg, @taxAmount=null, @transactionDate=@nowDate, 
							@autoAdjustTransactionDate=1, @saleTransactionID=@customTransactionIDForAdj, @invoiceID=@invoiceID, 
							@byPassTax=0, @byPassAccrual=0, @xmlSchedule=null, @transactionID=@adjTransactionID OUTPUT;
					END

					UPDATE dbo.tr_applications
					SET status = 'D'
					WHERE itemType = 'TicketPackInst'
					AND applicationTypeID = @EventsAppTypeID
					AND itemID = @olddetailID
					AND transactionID = @customTransactionIDForAdj;

					select @customTransactionIDForAdj = min(transactionID)
					from @tblExistingTrans 
					where itemType = 'TicketPackInst'
					and itemID = @olddetailID
					and transactionID > @customTransactionIDForAdj;
				END
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.removePackageInstanceSQL>
	</cffunction>
	
	<cffunction name="delRegCustomDetails" access="private" output="false" returntype="string">
		<cfargument name="dataID" type="numeric" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="sfdItemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="trItemType" type="string" required="true">
		<cfargument name="offerAmount" type="boolean" required="true">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.delRegCustomSQL">
			<cfoutput>
				<cfif arguments.offerAmount>
					select @customTransactionIDForAdj = null, @olddetailID = null;
					set @olddetailID = #val(arguments.dataID)#;

					select @customTransactionIDForAdj = min(transactionID)
					from @tblExistingTrans 
					where itemType = '#arguments.trItemType#'
					and itemID = @olddetailID;

					WHILE @customTransactionIDForAdj is not null BEGIN
						select @oldRegCustomFee=null, @oldRegCustomFeeNeg=null;

						select @oldRegCustomFee = isnull(amount,0)
						from @tblExistingTrans 
						where itemType = '#arguments.trItemType#'
						and itemID = @olddetailID
						and transactionID = @customTransactionIDForAdj;

						-- adjust custom sale transaction to 0 if exists
						IF @oldRegCustomFee > 0 BEGIN
							select @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null, @adjTransactionID = null;

							select @invoiceProfileID = gl.invoiceProfileID
							from dbo.tr_glAccounts as gl
							inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.glAccountID
							where t.transactionID = @customTransactionIDForAdj;

							select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

							-- if necessary, create invoice assigned to registrant based on invoice profile
							IF @invoiceID is null BEGIN
								EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
									@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@accrualDate, 
									@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
			
								insert into @tblInvoices (invoiceID, invoiceProfileID)
								values (@invoiceID, @invoiceProfileID);

								IF @registrationMerchantProfiles is not null
									EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
							END	

							set @oldRegCustomFeeNeg = @oldRegCustomFee * -1;

							EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@loggedInMemberID, 
								@statsSessionID=@statsSessionID, @amount=@oldRegCustomFeeNeg, @taxAmount=null, @transactionDate=@nowDate, 
								@autoAdjustTransactionDate=1, @saleTransactionID=@customTransactionIDForAdj, @invoiceID=@invoiceID, 
								@xmlSchedule=null, @byPassTax=0, @byPassAccrual=0, @transactionID=@adjTransactionID OUTPUT;
						END

						UPDATE dbo.tr_applications
						SET status = 'D'
						WHERE itemType = '#arguments.trItemType#'
						AND applicationTypeID = @EventsAppTypeID
						AND itemID = @olddetailID
						AND transactionID = @customTransactionIDForAdj;

						select @customTransactionIDForAdj = min(transactionID)
						from @tblExistingTrans 
						where itemType = '#arguments.trItemType#'
						and itemID = @olddetailID
						and transactionID > @customTransactionIDForAdj;
					END
				</cfif>

				delete 
				from dbo.cf_fieldData
				where dataID = #val(arguments.dataID)#
				and itemID = #val(arguments.itemID)#
				and itemType = '#arguments.sfdItemType#'
				and fieldID = #val(arguments.fieldID)#;
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.delRegCustomSQL>
	</cffunction>

	<cffunction name="createTicketPackageInstanceSeat" access="private" output="false" returntype="string">
		<cfargument name="memberID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.createPackageInstanceSeatSQL">
			<cfoutput>
				set @seatID = null;
				
				INSERT INTO dbo.ev_registrantPackageInstanceSeats (instanceID, memberID, status)
				VALUES (@instanceID, <cfif val(arguments.memberID) gt 0>#val(arguments.memberID)#<cfelse>null</cfif>, 'A');
					select @seatID = SCOPE_IDENTITY();
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.createPackageInstanceSeatSQL>
	</cffunction>

	<cffunction name="generateRegCapReachedEmail" access="public" output="no" returntype="struct">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.objEvents = CreateObject("component","model.events.events")>
		
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfset local.strEvent = local.objEvents.getEvent(eventID=arguments.eventID, siteID=local.mc_siteInfo.siteid, languageID=1)>
		
		<cfsavecontent variable="local.returnStruct.emailContent">
			<cfoutput>
			Event: #DateFormat(local.strEvent.qryEventTimes_selected.startTime, "m/d/yyyy")# #local.strEvent.qryEventMeta.EventContentTitle#<br/>
			The registration cap for this event has been reached.<br/>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.returnStruct.emailTitle = "#local.mc_siteInfo.sitename# Registration Cap Reached">

		<!--- determine replyto --->
		<cfset local.replyto = trim(local.strEvent.qryEventRegMeta.replyToEmail)>
		<cfif NOT len(local.replyto)>
			<cfset local.replyto = local.mc_siteInfo.supportProviderEmail>
		</cfif>

		<cfset local.emailsubject = 'Registration cap reached for #local.strEvent.qryEventMeta.EventContentTitle#'>

		<!--- mail collection --->
		<cfscript>
		local.returnStruct.mailCollection = structNew();

		local.returnStruct.mailCollection["from"] = local.mc_siteInfo.networkEmailFrom & ' ("' & local.mc_siteinfo.orgname & '")';
		local.returnStruct.mailCollection["subject"] = local.emailsubject;
		local.returnStruct.mailCollection["mailerid"] = local.mc_siteinfo.sitename;
		local.returnStruct.mailCollection["to"] = local.replyto;
		local.returnStruct.mailCollection["replyto"] = local.mc_siteInfo.supportProviderEmail;
		
		local.returnStruct.arrEmailTo = [];
		local.toEmailArr = listToArray(local.returnStruct.mailCollection["to"],';');
		for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
			local.returnStruct.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
		}
		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="validateCouponCode" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="couponCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		<cfargument name="rateAmtOverride" type="string" required="true">
		
		<cfscript>
		var local = structNew();
		
		local.returnStruct = { success=false, isvalidcoupon=false, couponresponse="Invalid Promo Code", discount=0 };

		arguments.couponCode = trim(arguments.couponCode);
		
		// if no length 
		if (len(arguments.couponCode) is 0) return local.returnStruct;

		cfxml(variable="local.cartItemsXML") {
			writeOutput('<cart><item mid="#arguments.memberID#" rateid="#arguments.rateID#" itemtype="Events" /></cart>');
		}

		local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','');

		var sqlParams = {
			siteID = { value=arguments.mcproxy_siteID, cfsqltype="CF_SQL_INTEGER" },
			applicationType = { value="Events", cfsqltype="CF_SQL_VARCHAR" },
			cartItemsXML = { value=local.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
			couponCode = { value=arguments.couponCode, cfsqltype="CF_SQL_VARCHAR" }
		};

		var qryValidCoupon = queryExecute("
			SET NOCOUNT ON;
			
			DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
				@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml,
				@isAvailable bit;
			
			EXEC dbo.tr_isValidCouponCodeForAdmin @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
				@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
				@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT, @isAvailable=@isAvailable OUTPUT;
			
			SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML, @isAvailable AS isAvailable;
			", 
			sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
		);

		local.couponID = val(qryValidCoupon.couponID);
		local.returnStruct.couponResponse = qryValidCoupon.couponMessage;

		// valid coupon
		if (local.couponID AND arrayLen(XMLSearch(qryValidCoupon.qualifiedCartItemsXML,'/cart/item[@rateid="#arguments.rateID#"]'))) {
			local.returnStruct.isValidCoupon = true;
			local.strRegDiscount = getRegistrationDiscount(mcproxy_siteID=arguments.mcproxy_siteID, couponID=local.couponID, rateID=arguments.rateID, rateAmtOverride=arguments.rateAmtOverride);
			local.returnStruct.couponID = val(local.couponID);
			local.returnStruct.isAvailable = val(qryValidCoupon.isAvailable);
			local.returnStruct.discount = local.strRegDiscount.discount;

			local.returnStruct.success = true;		
		}		
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="getRegistrationDiscount" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		<cfargument name="rateAmtOverride" type="string" required="true">	
		
		<cfset var local = structNew()>

		<cfset local.objAdminEvent = CreateObject("component","event")>
		<cfset local.strDiscount = { success=false, discount=0 }>

		<cfset local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=arguments.mcproxy_siteID, couponID=arguments.couponID)>

		<cfset local.strCoupon = { couponID = local.qryCoupon.couponID,
								couponCode = local.qryCoupon.couponCode,
								pctOff = local.qryCoupon.pctOff,
								pctOffMaxOff = local.qryCoupon.pctOffMaxOff,
								amtOff = local.qryCoupon.amtOff,
								redeemDetail = local.qryCoupon.redeemDetail,
								invoiceDetail = local.qryCoupon.invoiceDetail }>
		
		<cfif len(arguments.rateAmtOverride)>
			<cfset local.rateAmount = val(rereplace(arguments.rateAmtOverride,"[^0-9.]","","ALL"))>
		<cfelse>
			<cfset local.rateAmount = val(local.objAdminEvent.getRateAmount(arguments.rateID))>
		</cfif>	
		
		<cfset local.strRateDiscount = getAmountAfterCouponApplied(strCoupon=local.strCoupon, rateAmount=local.rateAmount)>		

		<cfset local.strDiscount["discount"] = local.strRateDiscount.discount>
		<cfset local.strDiscount["success"] = true>

		<cfreturn local.strDiscount>
	</cffunction>

	<cffunction name="getAmountAfterCouponApplied" access="private" output="false" returntype="struct">
		<cfargument name="strCoupon" type="struct" required="true">
		<cfargument name="rateAmount" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.strDiscount = { finalAmt=0, discount=0 }>

		<cfif arguments.rateAmount eq 0>
			<cfreturn local.strDiscount>
		</cfif>
		<cfif val(arguments.strCoupon.pctOff) gt 0>
			<cfset local.strDiscount.discount = NumberFormat((arguments.rateAmount * (arguments.strCoupon.pctOff / 100)),"9.99")>
			<cfif val(arguments.strCoupon.pctOffMaxOff) gt 0 and max(arguments.strCoupon.pctOffMaxOff,local.strDiscount.discount) eq local.strDiscount.discount>
				<cfset local.strDiscount.discount = arguments.strCoupon.pctOffMaxOff>
			</cfif>
		<cfelseif val(arguments.strCoupon.amtOff) gt 0>
			<cfset local.strDiscount.discount = arguments.strCoupon.amtOff>
		</cfif>

		<cfset local.strDiscount.discount = local.strDiscount.discount>
		<cfset local.strDiscount.finalAmt = NumberFormat(arguments.rateAmount - local.strDiscount.discount,"9.99")>
		
		<cfif local.strDiscount.finalAmt lte 0>
			<cfset local.strDiscount.finalAmt = 0>
			<cfset local.strDiscount.discount = arguments.rateAmount>
		</cfif>
		<cfreturn local.strDiscount>
	</cffunction>

</cfcomponent>