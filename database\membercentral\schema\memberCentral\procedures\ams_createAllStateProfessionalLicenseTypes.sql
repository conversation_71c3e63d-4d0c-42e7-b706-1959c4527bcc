ALTER PROC dbo.ams_createAllStateProfessionalLicenseTypes
@orgID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tblStates TABLE (stateName VARCHAR(50), orderNum INT);

	-- Get all US states + DC not already a professional license. seed with 300
	INSERT INTO @tblStates (stateName, orderNum)
	SELECT s.[Name], 300 + ROW_NUMBER() OVER (ORDER BY s.[Name])
	FROM dbo.ams_states as s
	WHERE s.countryID = 1
	AND s.orderPref = 1
	AND dbo.fn_ams_isValidNewMemberViewColumn(@orgID, s.[Name]) = 0;

	-- delete any invalid state based on other reserved column names
	DELETE s
	FROM @tblStates as s
	CROSS JOIN dbo.fn_varcharListToTable('licenseNumber,activeDate,status',',') as f
	WHERE dbo.fn_ams_isValidNewMemberViewColumn(@orgID, s.stateName + '_' + f.listItem) = 1;

	-- get default site default State and move that to the top of the list by setting it to 300, above all the others.
	UPDATE tmp
	SET tmp.orderNum = 300
	FROM dbo.organizations as o
	INNER JOIN dbo.sites as s on s.siteID = o.defaultSiteID
	INNER JOIN dbo.ams_states as st on st.Code = s.defaultPostalState and st.countryID = 1
	INNER JOIN @tblStates as tmp on tmp.stateName = st.Name
	WHERE o.orgID = @orgID;

	-- add the new licenses and reorder
	INSERT INTO dbo.ams_memberProfessionalLicenseTypes (orgID, PLName, orderNum, alertDuplicates)
	SELECT @orgID, stateName, orderNum, 0
	FROM @tblStates;

	EXEC dbo.ams_reorderMemberProfessionalLicenseTypes @orgID=@orgID;

	-- Recreate the member data view to include the new license types
	EXEC dbo.ams_createVWMemberData @orgID=@orgID;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
