ALTER PROC dbo.ams_autoRunLinkedDateCustomFieldRule
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpCustomFields') IS NOT NULL 
		DROP TABLE #tmpCustomFields;
	CREATE TABLE #tmpCustomFields (columnID int, orgID int);

	DECLARE @orgID int, @columnID int, @sysMemberID int;
	SELECT @sysMemberID = dbo.fn_ams_getMCSystemMemberID();

	INSERT INTO #tmpCustomFields (columnID, orgID)
	SELECT columnID, orgID
	FROM dbo.ams_memberDataColumns
	WHERE linkedDateColumnID IS NOT NULL;

	SELECT @columnID = MIN(columnID) FROM #tmpCustomFields;
	WHILE @columnID IS NOT NULL BEGIN
		SET @orgID = NULL;

		SELECT @orgID = orgID
		FROM #tmpCustomFields
		WHERE columnID = @columnID;

		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=NULL, @columnID=@columnID,
			@recordedByMemberID=@sysMemberID, @byPassQueue=0;

		SELECT @columnID = MIN(columnID) FROM #tmpCustomFields WHERE columnID > @columnID;
	END
	
	IF OBJECT_ID('tempdb..#tmpCustomFields') IS NOT NULL 
		DROP TABLE #tmpCustomFields;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
