ALTER PROC dbo.ams_importMemberPayProfiles_BD_import
@siteID int,
@memberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpNewBankAccounts') IS NOT NULL 
		DROP TABLE #tmpNewBankAccounts;
	CREATE TABLE #tmpNewBankAccounts (MPPPayProfileID int, routingNumber varchar(9), accountNumber varchar(17), acctType varchar(8));

	DECLARE @orgID int;
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	BEGIN TRY
		-- we do it this way because insert with OUTPUT INTO can only refer to columns of the inserted table
		MERGE INTO dbo.ams_memberPaymentProfiles as mpp USING #mcBDImport AS tmp on 1 = 0
		WHEN NOT MATCHED THEN
			INSERT (memberid, profileID, [status], detail, nickname, customerProfileID, paymentProfileID, 
				dateAdded, addedStatsSessionID, otherFields, addedByMemberID, surchargeEligible)
			VALUES (tmp.MCMemberID, tmp.MCPayProfileID, 'A', 'XXXX' + right(tmp.Account,4), tmp.NickName, 
				tmp.Routing, tmp.Account, getdate(), 0, '<fields><fld_27_>' + tmp.AccountType + '</fld_27_></fields>', 
				@memberID, 0)
			OUTPUT inserted.payProfileID, inserted.customerProfileID, inserted.paymentProfileID, tmp.AccountType
			INTO #tmpNewBankAccounts (MPPPayProfileID, routingNumber, accountNumber, acctType);

		INSERT INTO dbo.tr_bankAccounts (orgID, siteID, MPPPayProfileID, routingNumber, accountNumber, acctType)
		SELECT @orgID, @siteID, MPPPayProfileID, routingNumber, accountNumber, acctType
		FROM #tmpNewBankAccounts;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblBDProfileErrors (msg)
		VALUES ('Unable to import bank draft records.');

		INSERT INTO #tblBDProfileErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblBDProfileErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tmpNewBankAccounts') IS NOT NULL 
		DROP TABLE #tmpNewBankAccounts;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
