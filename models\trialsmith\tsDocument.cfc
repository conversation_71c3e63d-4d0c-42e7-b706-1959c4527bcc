<cfcomponent>
	<cfset this.documentid = 0>
	<cfset this.depomemberdataid = 0>
	<cfset this.sourcestate = "">
	<cfset this.groupid = 0>
	<cfset this.documentdate = 0>
	<cfset this.casetypeid = 0>
	<cfset this.ExpertName = 0>
	<cfset this.Style = "">
	<cfset this.disabled = 0>
	<cfset this.orgcode = 0>
	<cfset this.pages = 0>
	<cfset this.datelastmodified = "">
	<cfset this.docstatusname = "">
	<cfset this.documenttype = CreateObject('component', 'models.trialsmith.tsDocumentType')>

	<cffunction name="load" access="public">
		<cfargument name="documentid" type="numeric" required="true">
		<cfset var local = structnew()>

		<cfquery name="local.getDoc" datasource="#application.settings.dsn.trialsmith.dsn#">
			SELECT d.documentid, d.depomemberdataid, d.groupid, d.documentdate, d.documenttypeid, d.expertname, d.style, d.disabled, d.state,
				d.casetypeid, d.pages, d.datelastmodified, ds.statusName AS documentStatusName
			FROM dbo.depodocuments AS d
			INNER JOIN dbo.depoDocumentStatusHistory AS docSH ON docSH.depoDocumentHistoryID = d.currentStatusHistoryID
			INNER JOIN dbo.depoDocumentStatuses AS ds ON ds.statusID = docSH.statusID
			WHERE d.documentid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentid#">
		</cfquery>

		<cfset this.documentid = local.getDoc.documentid>
		<cfset this.documenttypeid = local.getDoc.documenttypeid>
		<cfset this.depomemberdataid = local.getDoc.depomemberdataid>
		<cfset this.sourcestate = local.getDoc.state>
		<cfset this.groupid = local.getDoc.groupid>
		<cfset this.documentdate = local.getDoc.documentdate>
		<cfset this.ExpertName = local.getDoc.ExpertName>
		<cfset this.Style = local.getDoc.Style>
		<cfset this.disabled = local.getDoc.disabled>
		<cfset this.orgcode = local.getDoc.state>
		<cfset this.casetypeid = local.getDoc.casetypeid>
		<cfset this.pages = local.getDoc.pages>
		<cfset this.datelastmodified = local.getDoc.datelastmodified>
		<cfset this.docstatusname = local.getDoc.documentStatusName>
		<cfset this.documenttype.load(local.getDoc.documenttypeid)>
	</cffunction>


	<cffunction name="addPermissions" access="public">
		<cfargument name="depomemberdataID" type="numeric" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.insertPerm" datasource="#application.settings.dsn.trialsmith.dsn#">
			insert into depoPermissions (depomemberdataid, documentid, dateadded)
			values (
				<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
				<cfqueryparam value="#this.documentid#" cfsqltype="CF_SQL_INTEGER">,
				getdate()
			)
		</cfquery>
	</cffunction>


	<cffunction name="hasContributed" access="public" returntype="boolean" output="no">
		<cfargument name="depomemberdataid" type="numeric" required="true">

		<cfset var qryGetContributed = "">

		<cfquery name="qryGetContributed" datasource="#application.settings.dsn.trialsmith.dsn#">
			select dbo.fn_Documents_hasContributed(<cfqueryparam value="#this.documentid#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">) as hasContributed
		</cfquery>

		<cfreturn qryGetContributed.hasContributed>
	</cffunction>


	<cffunction name="hasPermissions" access="public" returntype="boolean" output="no">
		<cfargument name="depomemberdataid" type="numeric" required="true">

		<cfset var qryGetPermission = "">

		<cfquery name="qryGetPermission" datasource="#application.settings.dsn.trialsmith.dsn#">
			select dbo.fn_Documents_checkPermissionsToDoc(<cfqueryparam value="#this.documentid#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">) as hasPermission
		</cfquery>

		<cfreturn qryGetPermission.hasPermission>
	</cffunction>


	<cffunction name="hasBankAccess" access="public" returntype="boolean" output="no">
		<cfargument name="depomemberdataid" type="numeric" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.getbanks" datasource="#application.settings.dsn.trialsmith.dsn#">
			Select dbo.fn_Documents_hasBankAccess(<cfqueryparam value="#this.documentid#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">) as bitBankAccess
		</cfquery>

		<cfreturn local.getbanks.bitBankAccess>
	</cffunction>

	<cffunction name="isExpertDoc" access="public" returntype="boolean" output="no">
		<cfreturn ((this.documenttype.documenttypeid eq 288) and (this.casetypeid eq 115))>
	</cffunction>
	<cffunction name="isCourtDoc" access="public" returntype="boolean">
		<cfreturn this.documenttype.isCourtDoc()>
	</cffunction>
	<cffunction name="isSpecial" access="public" returntype="boolean">
		<cfreturn this.documenttype.isSpecial()>
	</cffunction>
	<cffunction name="isOther" access="public" returntype="boolean">
		<cfreturn this.documenttype.isOther()>
	</cffunction>
	<cffunction name="isAvailLaser" access="public" returntype="boolean">
		<cfreturn this.documenttype.isAvailLaser()>
	</cffunction>
	<cffunction name="isAvailCD" access="public" returntype="boolean">
		<cfreturn this.documenttype.isAvailCD()>
	</cffunction>
	<cffunction name="isAvailEmail" access="public" returntype="boolean">
		<cfreturn this.documenttype.isAvailEmail()>
	</cffunction>
	<cffunction name="isAvailDownload" access="public" returntype="boolean">
		<cfreturn this.documenttype.isAvailDownload()>
	</cffunction>
	<cffunction name="isAvailView" access="public" returntype="boolean">
		<cfreturn this.documenttype.isAvailView()>
	</cffunction>
	<cffunction name="isAvailVideo" access="public" returntype="boolean">
		<cfreturn this.documenttype.isAvailVideo()>
	</cffunction>

	<cffunction name="getDocumentInfoForWebViewer" access="public" output="false" returntype="struct">
		<cfargument name="docID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.returnStruct = structNew() />
		<cfset local.returnStruct["documentID"] = 0 />
		<cfset local.s3bucket = "trialsmith-depos" />
		<cfset local.s3requesttype = "vhost" />
		<cfset local.s3region = "us-east-1" />
		<cfif application.objSiteInfo.isRequestSecure()>
			<cfset local.s3protocol = "https" />
		<cfelse>
			<cfset local.s3protocol = "http" />
		</cfif>

		<cfset local.s3expire = 15 />

		<cfif arguments.docID gt 0>
			<cfset load(arguments.docID)>
			<cfset local.currentDocument = this>
			<cfset local.returnStruct["documentID"] = val(local.currentDocument.DocumentID)>

			<cfset local.s3keyMod = numberFormat(local.currentDocument.documentid mod 1000,"0000")>
			<cfset local.unencryptedPassword = "tsdocumentID#local.currentDocument.documentid#/#local.s3keyMod#/#local.currentDocument.documentid#">
			<cfset local.encryptedPassword = lcase(hash(local.unencryptedPassword))>

			<cfset local.objectKey = "">
			<cfif local.currentDocument.docstatusname EQ 'Pending Approval' OR local.currentDocument.docstatusname EQ 'Flagged for Review'>
				<cfset local.objectKey = lcase("depos/approvals/#local.s3keyMod#/#local.currentDocument.DocumentID#.xod")>
			<cfelseif local.currentDocument.docstatusname EQ 'Approved'>
				<cfset local.objectKey = lcase("depos/xod/#local.s3keyMod#/#local.currentDocument.DocumentID#.xod")>
			</cfif>
		</cfif>

		<cfif isDefined("session.loggedin") and session.loggedin is not 1>
			<cfset local.returnStruct.result = "requireLogin" />
		<cfelseif NOT local.returnStruct["documentID"] OR NOT len(local.objectKey)>
			<cfset local.returnStruct.result = "invalidDocument" />
		<cfelseif NOT application.objS3.s3FileExists(bucket=local.s3bucket, objectKey=local.objectKey, requestType='vhost', region=local.s3region)>
			<cfset local.returnStruct.result = "documentNotAvailable" />
		<cfelse>
			<cfset local.returnStruct.result = "viewingAllowed" />

			<cfset local.arrAmzHeaders = arrayNew(1)>
			<cfset local.tmpStr = { key="response-content-disposition", value="inline;" }>
			<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>
			<cfset local.s3Link = application.objS3.s3Url(bucket=local.s3bucket, objectKey=local.objectKey, requestType=local.s3requesttype, expireInMinutes=local.s3expire, canonicalizedAmzHeaders=local.arrAmzHeaders,region=local.s3region,protocol=local.s3protocol)>

			<cfset local.returnStruct["s3Link"] = local.s3Link />
			<cfset local.returnStruct["encPass"] = local.encryptedPassword />
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>