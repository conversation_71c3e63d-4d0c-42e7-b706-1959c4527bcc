ALTER PROC dbo.queue_CACLECredits_clear
@status varchar(60)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	BEGIN TRAN;
		DELETE qi
		FROM dbo.tblQueueItems as qi
		inner join dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'caCLECredits'
		and qs.queueStatus = @status;

		DELETE from dbo.tblQueueItemData
		where itemUID not in (select itemUID from dbo.tblQueueItems);
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
