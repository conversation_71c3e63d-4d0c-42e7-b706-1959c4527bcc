ALTER PROC dbo.sponsors_reorderSponsorGroupings
@siteID int,
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmpGroupings TABLE (sponsorGroupingID int NOT NULL, sponsorGroupingOrder int NOT NULL, neworder int NOT NULL);

	INSERT INTO @tmpGroupings (sponsorGroupingID, sponsorGroupingOrder, newOrder)
	SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) as newOrder
	FROM dbo.sponsorsGrouping
	WHERE siteID = @siteID
	AND referenceType = @referenceType
	AND referenceID = @referenceID;

	UPDATE sg
	SET sg.sponsorGroupingOrder = tmp.neworder
	FROM dbo.sponsorsGrouping as sg
	INNER JOIN @tmpGroupings as tmp on tmp.sponsorGroupingID = sg.sponsorGroupingID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
