ALTER PROC dbo.ams_getAlertGroupsWithMembers
@orgID int,
@limitRowsCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
	DECLARE @totalCount int;

	IF OBJECT_ID('tempdb..#tmpGroups') IS NOT NULL 
		DROP TABLE #tmpGroups;
	CREATE TABLE #tmpGroups (groupID int, rowID int);

	INSERT INTO #tmpGroups (groupID, rowID)
	SELECT g.groupID, ROW_NUMBER() OVER(order by g.groupPathExpanded)
	FROM dbo.ams_groups AS g
	WHERE g.orgID = @orgID
	AND g.[status] = 'A'
	AND g.alertIfPopulated = 1
	AND EXISTS (
		SELECT TOP 1 autoID
		FROM dbo.cache_members_groups AS cmg
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
			AND m.memberID = cmg.memberID 
			AND m.[status] <> 'D'
		WHERE cmg.orgID = @orgID
		AND cmg.groupID = g.groupID
	);

	SET @totalCount = @@ROWCOUNT;

	SELECT g.groupID, g.groupPathExpanded AS groupPath, @totalCount AS totalCount
	FROM #tmpGroups AS tmp
	INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID 
		AND g.groupID = tmp.groupID
	WHERE tmp.rowID <= @limitRowsCount
	ORDER BY tmp.rowID;

	IF OBJECT_ID('tempdb..#tmpGroups') IS NOT NULL 
		DROP TABLE #tmpGroups;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
