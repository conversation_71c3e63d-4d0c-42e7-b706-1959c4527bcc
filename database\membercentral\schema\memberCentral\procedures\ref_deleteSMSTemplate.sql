ALTER PROC dbo.ref_deleteSMSTemplate
@templateID int,
@isTemplateInUse bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @isTemplateInUse = 0;

	IF @isTemplateInUse = 0
		UPDATE dbo.ref_smsTemplates
		SET isActive = 0
		WHERE templateID = @templateID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLL<PERSON>CK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
