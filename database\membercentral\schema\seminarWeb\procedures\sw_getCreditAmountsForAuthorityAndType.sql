ALTER PROCEDURE dbo.sw_getCreditAmountsForAuthorityAndType
	@catalogOrgCode VARCHAR(10),
	@authorityID INT,
	@creditType VARCHAR(200)
AS
BEGIN
	-- Use CTEs and proper joins instead of parameter lists
	WITH AvailableSWLPrograms AS (
		SELECT smc.seminarID
		FROM dbo.swl_SeminarsInMyCatalog(@catalogOrgCode, '1-1-2005', DATEADD(yy,2,GETDATE())) smc
		INNER JOIN dbo.tblSeminarsSWLive swl ON swl.seminarID = smc.seminarID
		WHERE swl.dateStart > GETDATE()
	),
	AvailableSWODPrograms AS (
		SELECT seminarID
		FROM dbo.swod_SeminarsInMyCatalog(@catalogOrgCode)
	)
	SELECT DISTINCT
		CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') AS creditAmount
	FROM dbo.tblSeminarsAndCredit AS sac
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities csa ON csa.CSALinkID = sac.CSALinkID
	INNER JOIN dbo.tblCreditAuthorities ca ON ca.authorityID = csa.authorityID
		AND ca.authorityID = @authorityID
	INNER JOIN dbo.tblCreditStatuses cstat ON sac.statusID = cstat.statusID
		AND cstat.status IN ('Approved','Pending','Self-Submitting')
	LEFT JOIN dbo.tblSeminarsSWLive sswl ON sswl.seminarID = sac.seminarID
	LEFT JOIN dbo.tblSeminarsSWOD sswod ON sswod.seminarID = sac.seminarID
	CROSS APPLY sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') AS CRDA(credittype)
	CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') AS CRDT(credittype)
	WHERE (
		(sswl.liveid IS NOT NULL AND sac.seminarID IN (SELECT seminarID FROM AvailableSWLPrograms))
		OR
		(sswod.ondemandID IS NOT NULL 
		AND GETDATE() BETWEEN sac.creditOfferedStartDate AND sac.creditOfferedEndDate
		AND sac.seminarID IN (SELECT seminarID FROM AvailableSWODPrograms))
	)
	AND CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') > 0
	AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)')
	AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = @creditType
	ORDER BY creditAmount;
END
GO
