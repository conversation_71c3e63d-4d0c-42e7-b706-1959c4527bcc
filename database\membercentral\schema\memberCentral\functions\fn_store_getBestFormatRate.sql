ALTER FUNCTION dbo.fn_store_getBestFormatRate (
	@formatID int, @FID int, @memberID int, @siteID int
)
RETURNS @bestRate TABLE (
	rateid int,
	rate decimal(18,2)
)
AS
BEGIN

	DECLARE @groupPrintID int;

	select @groupPrintID = groupPrintID 
	from dbo.ams_members
	where memberID = @memberID;

	INSERT INTO @bestRate (rateid, rate)
	select top 1 r.rateid, case when min(pro.rateOverride) is null then min(r.rate) else min(pro.rateOverride) end as rate
	from dbo.store_rates as r
	left outer join dbo.store_ProductRatesOverride as pro on pro.rateid = r.rateid
		and (pro.startDate <= getDate() 
			and pro.endDate >= getDate()
		) 
		or pro.startDate is null 
		or pro.endDate is null
	inner join dbo.cms_siteResources as c on c.siteID = @siteID
		and r.siteResourceID = c.siteResourceID 
		and c.siteResourceStatusID = 1
	inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
		and srfrp.siteresourceID = c.siteResourceID 
		and srfrp.functionID = @FID
	inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
		and gprp.rightPrintID = srfrp.rightPrintID
		and gprp.groupPrintID = @groupPrintID
	where r.formatID in (@formatID) 
		and (r.startDate is null or CAST( CONVERT( char(8), r.startDate, 112) AS datetime) <= CAST( CONVERT( char(8), getDate(), 112) as datetime))
		and (r.endDate is null or DATEADD (d , 1, CAST( CONVERT( char(8), r.endDate, 112) AS datetime) ) > getdate())
	group by r.rateid
	order by rate;

	RETURN;
END
GO
