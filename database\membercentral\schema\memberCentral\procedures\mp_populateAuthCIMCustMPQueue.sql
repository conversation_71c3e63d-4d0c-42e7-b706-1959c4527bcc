ALTER PROC dbo.mp_populateAuthCIMCustMPQueue
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady INT, @WeekToProcess TINYINT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='authCIMCustMP', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	-- Calculate which of the four buckets to process based on the week number of the year.
	-- this task runs every Saturday in a continuous, repeating 4-week cycle (0, 1, 2, 3, 0, 1, 2, 3, ...)
	SET @WeekToProcess = (DATEPART(week, GETDATE()) - 1) % 4;

	-- queue the pay profiles marked for this weeknum
	INSERT INTO platformQueue.dbo.queue_authCIMCustMP (profileID, gatewayUsername, gatewayPassword, statusID, dateAdded, dateUpdated)
	SELECT MIN(mp.profileID), mp.gatewayUsername, mp.gatewayPassword, @statusReady, GETDATE(), GETDATE()
	FROM dbo.mp_profiles AS mp
	LEFT OUTER JOIN platformQueue.dbo.queue_authCIMCustMP AS qid ON qid.profileID = mp.profileID
	WHERE mp.gatewayID = 10
	AND mp.[status] = 'A'
	AND mp.authorizeMPCheckWeekNum = @WeekToProcess
	AND qid.itemID IS NULL
	GROUP BY mp.gatewayUsername, mp.gatewayPassword;

	SET @itemCount = @@ROWCOUNT;

	-- if weeknum is last in the cycle (3), redistribute the pay profiles based on most updated counts for the next cycle
	IF @WeekToProcess = 3
		EXEC dbo.mp_distributeAuthCIMChecks;

	-- resume task to check each pay profile in the queue
	IF @itemCount > 0
		EXEC dbo.sched_resumeTask @name='Process Authorize CIM Customer Merchant Profiles Queue', @engine='BERLinux';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
