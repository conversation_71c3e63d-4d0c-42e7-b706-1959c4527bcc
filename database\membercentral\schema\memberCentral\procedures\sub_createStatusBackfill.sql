ALTER PROC dbo.sub_createStatusBackfill
@siteID int,
@subscriberID int,
@isRenewal bit,
@enteredByMemberID int,
@bypassQueue bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @memberID int, @currStatusID int, @currStatusCode varchar(1), @subStartDate datetime, 
		@subEndDate datetime, @currUpdateDate datetime, @activeStatusID int, @InactiveStatusID int, @prebillStatusID int, 
		@billedStatusID int, @acceptedStatusID int, @expiredStatusID int, @offerExpiredStatusID int, @subscriptionID int;

	set @enteredByMemberID = nullif(@enteredByMemberID,0);

	select @orgID = orgID from dbo.sites where siteID = @siteID;
	select @activeStatusID = statusID from dbo.sub_statuses where statusCode = 'A';
	select @InactiveStatusID = statusID from dbo.sub_statuses where statusCode = 'I';
	select @prebillStatusID = statusID from dbo.sub_statuses where statusCode = 'R';
	select @billedStatusID = statusID from dbo.sub_statuses where statusCode = 'O';
	select @acceptedStatusID = statusID from dbo.sub_statuses where statusCode = 'P';
	select @expiredStatusID = statusID from dbo.sub_statuses where statusCode = 'E';
	select @offerExpiredStatusID = statusID from dbo.sub_statuses where statusCode = 'X';

	select @currStatusID = s.statusID, @currStatusCode=st.statusCode, @subStartDate=s.subStartDate, 
		@subEndDate=s.subEndDate, @memberID=s.memberID, @subscriptionID=s.subscriptionID
	from dbo.sub_subscribers s
	inner join dbo.sub_statuses st on st.statusID = s.statusID
	where s.subscriberID = @subscriberID;

	IF @currStatusID is null OR @currStatusCode is null
		RAISERROR('Current status not identified.',16,1);

	set @currUpdateDate = @subStartDate;

	BEGIN TRAN;
		delete from dbo.sub_statusHistory
		where orgID = @orgID
		and subscriberID = @subscriberID;
		
		IF @IsRenewal = 1 BEGIN
			insert into dbo.sub_statusHistory (subscriberID, updateDate, statusID, enteredByMemberID, orgID, subscriptionID)
			values (@subscriberID, @currUpdateDate, @prebillStatusID, @enteredByMemberID, @orgID, @subscriptionID);
			
			set @currUpdateDate = DateAdd(ss, 5, @currUpdateDate);
		END

		IF @currStatusCode in ('O','P','A','I','E','X') BEGIN
			IF @IsRenewal = 1
				insert into dbo.sub_statusHistory (subscriberID, updateDate, statusID, enteredByMemberID, oldStatusID, orgID, subscriptionID)
				values (@subscriberID, @currUpdateDate, @billedStatusID, @enteredByMemberID, @prebillStatusID, @orgID, @subscriptionID);
			ELSE
				insert into dbo.sub_statusHistory (subscriberID, updateDate, statusID, enteredByMemberID, orgID, subscriptionID)
				values (@subscriberID, @currUpdateDate, @billedStatusID, @enteredByMemberID, @orgID, @subscriptionID);
			
			set @currUpdateDate = DateAdd(ss, 5, @currUpdateDate);
		END

		IF @currStatusCode = 'X'
			insert into dbo.sub_statusHistory (subscriberID, updateDate, statusID, enteredByMemberID, oldStatusID, orgID, subscriptionID)
			values (@subscriberID, @subEndDate, @offerExpiredStatusID, @enteredByMemberID, @billedStatusID, @orgID, @subscriptionID);

		IF @currStatusCode IN ('P','A','I','E') BEGIN
			insert into dbo.sub_statusHistory (subscriberID, updateDate, statusID, enteredByMemberID, oldStatusID, orgID, subscriptionID)
			values (@subscriberID, @currUpdateDate, @acceptedStatusID, @enteredByMemberID, @billedStatusID, @orgID, @subscriptionID);
			
			set @currUpdateDate = DateAdd(ss, 5, @currUpdateDate);
		END

		IF @currStatusCode IN ('A','I','E') BEGIN
			insert into dbo.sub_statusHistory (subscriberID, updateDate, statusID, enteredByMemberID, oldStatusID, orgID, subscriptionID)
			values (@subscriberID, @currUpdateDate, @activeStatusID, @enteredByMemberID, @acceptedStatusID, @orgID, @subscriptionID);
			
			set @currUpdateDate = DateAdd(ss, 5, @currUpdateDate);
		END

		IF @currStatusCode = 'I' BEGIN
			insert into dbo.sub_statusHistory (subscriberID, updateDate, statusID, enteredByMemberID, oldStatusID, orgID, subscriptionID)
			values (@subscriberID, @currUpdateDate, @InactiveStatusID, @enteredByMemberID, @activeStatusID, @orgID, @subscriptionID);
			
			set @currUpdateDate = DateAdd(ss, 5, @currUpdateDate);
		END

		IF @currStatusCode = 'E' BEGIN
			insert into dbo.sub_statusHistory (subscriberID, updateDate, statusID, enteredByMemberID, oldStatusID, orgID, subscriptionID)
			values (@subscriberID, @subEndDate, @expiredStatusID, @enteredByMemberID, @activeStatusID, @orgID, @subscriptionID);
			
			update dbo.sub_subscribers set
				graceEndDate = @subEndDate
			where subscriberID = @subscriberID
			and graceEndDate is not null;
		END
	COMMIT TRAN;

	-- process conditions
	IF @bypassQueue = 0 BEGIN	
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		SELECT distinct c.orgID, null, c.conditionID
		from dbo.ams_virtualGroupConditions as c
		inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
			and cv.conditionValue = cast(@subscriptionID as varchar(10))
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID
			and k.conditionKey = 'subSubscription'
		where c.orgID = @orgID;

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
	END
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
